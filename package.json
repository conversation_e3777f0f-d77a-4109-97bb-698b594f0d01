{"name": "internal-tooling", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "generate-types": "npx supabase gen types typescript --local > ./src/types/gen/database.types.ts", "migration": "npx supabase db diff | npx supabase migration new", "create-migration": "npx supabase db diff | npx supabase migration new"}, "dependencies": {"@headlessui/react": "^2.0.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.6", "@supabase/ssr": "^0.0.10", "@tabler/icons-react": "^2.46.0", "@tanstack/react-table": "^8.17.3", "@trigger.dev/nextjs": "^2.3.16", "@trigger.dev/react": "^2.3.17", "@trigger.dev/sdk": "^2.3.16", "@types/papaparse": "^5.3.14", "@upstash/redis": "^1.29.0", "@vercel/functions": "^2.0.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^1.0.0", "cron-timezone-converter": "^1.0.0", "date-fns": "^3.3.1", "dropbox": "^10.34.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.378.0", "next": "^14.2.5", "node-fetch": "^3.3.2", "nodemailer": "^6.9.11", "papaparse": "^5.4.1", "posthog-js": "^1.131.3", "posthog-node": "^4.0.1", "puppeteer": "^22.12.1", "react": "^18.3.1", "react-day-picker": "^8.10.0", "react-dom": "^18.3.1", "redis": "^4.6.13", "swr": "^2.2.5", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/xlsx": "^0.0.36", "autoprefixer": "^10.0.1", "eslint": "^8.56.0", "eslint-config-next": "^14.2.5", "jest": "^29.7.0", "postcss": "^8", "prettier": "3.2.4", "supabase": "2.12.1", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.2", "typescript": "^5"}, "trigger.dev": {"endpointId": "internal-tools-xR7z"}}