import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getClientsCountAction } from '@/server-actions/widgets/get-clients-count';

async function ClientCountWidget() {
  const { count, error } = await getClientsCountAction();

  if (error) {
    return (
      <Card className="flex flex-col w-[270px] h-[130px]">
        <CardHeader>Kunden</CardHeader>
        <CardContent>
          <p>{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[250px]">
      <CardHeader>Kunden</CardHeader>
      <CardContent>
        <p className="text-xl">{count}</p>
      </CardContent>
    </Card>
  );
}

export default ClientCountWidget;
