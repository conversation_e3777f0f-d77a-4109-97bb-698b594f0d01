import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getRevenueForecast } from '@/server-actions/projekt-reporting/revenue-forecast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { prettyPrintCurrency } from '@/lib/utils';

async function ForecastWidget() {
  const { data, error } = await getRevenueForecast();

  if (error) {
    return (
      <Card className="flex flex-col w-[800px] h-[170px]">
        <CardHeader>Umsatzprognose</CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="min-w-[800px]">
      <CardHeader>Umsatzprognose</CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap">Total</TableHead>
              <TableHead className="whitespace-nowrap">Aktueller Monat</TableHead>
              <TableHead className="whitespace-nowrap">Nächster <PERSON>t</TableHead>
              <TableHead className="whitespace-nowrap">Übernächster Monat</TableHead>
              <TableHead className="whitespace-nowrap">Aktuelles Jahr</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(data?.total)}</TableCell>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(data?.currMonthBudget)}</TableCell>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(data?.nextMonthBudget)}</TableCell>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(data?.nextNextMonthBudget)}</TableCell>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(data?.curYearBudget)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

export default ForecastWidget;
