import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getProjectsCountAction } from '@/server-actions/widgets/get-projects-count';

async function ProjectCountWidget() {
  const { count, error } = await getProjectsCountAction();

  if (error) {
    return (
      <Card className="flex flex-col w-[270px] h-[130px]">
        <CardHeader>Projekte</CardHeader>
        <CardContent>
          <p>{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[250px]">
      <CardHeader>Projekte</CardHeader>
      <CardContent>
        <p className="text-xl">{count}</p>
      </CardContent>
    </Card>
  );
}

export default ProjectCountWidget;
