import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getTasksCountAction } from '@/server-actions/widgets/get-tasks-count';

async function TaskCountWidget() {
  const { count, error } = await getTasksCountAction();

  if (error) {
    return (
      <Card className="flex flex-col w-[270px] h-[130px]">
        <CardHeader>Tasks</CardHeader>
        <CardContent>
          <p>{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[250px]">
      <CardHeader>Tasks</CardHeader>
      <CardContent>
        <p className="text-xl">{count}</p>
      </CardContent>
    </Card>
  );
}

export default TaskCountWidget;
