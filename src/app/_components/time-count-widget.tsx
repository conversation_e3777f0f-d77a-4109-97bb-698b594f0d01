import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getTimeCountAction } from '@/server-actions/widgets/get-time-count';

async function TaskCountWidget() {
  const { data, error } = await getTimeCountAction();

  if (error) {
    return (
      <Card className="flex flex-col w-[270px] h-[130px]">
        <CardHeader>Zeit</CardHeader>
        <CardContent>
          <p>{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[250px]">
      <CardHeader>Zeit</CardHeader>
      <CardContent>
        <p>Total: {data.total}h</p>
        <p>Kunden: {data.billable}h</p>
        <p>Bexio: {data.billableSynced}h</p>
      </CardContent>
    </Card>
  );
}

export default TaskCountWidget;
