import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

type WidgetLoaderProps = {
  size?: 'small' | 'medium' | 'large';
};

export function WidgetLoader({ size = 'small' }: WidgetLoaderProps) {
  return (
    <Card className={cn('flex flex-col', size === 'small' ? 'w-[270px] h-[130px]' : 'w-[800px] h-[170px]')}>
      <CardHeader>
        <Skeleton className="h-4" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4" />
      </CardContent>
    </Card>
  );
}
