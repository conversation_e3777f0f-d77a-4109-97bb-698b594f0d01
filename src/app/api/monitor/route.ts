export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = () => {
  const memUsage = process.memoryUsage();
  const { getHeapStatistics } = require('v8');
  const detailedStats = getHeapStatistics();

  const metrics = [
    `# HELP nodejs_heap_size_total_bytes Total size of the V8 heap in bytes.`,
    `# TYPE nodejs_heap_size_total_bytes gauge`,
    `nodejs_heap_size_total_bytes ${detailedStats.total_heap_size}`,

    `# HELP nodejs_heap_size_executable_bytes Total size of executable code within the V8 heap.`,
    `# TYPE nodejs_heap_size_executable_bytes gauge`,
    `nodejs_heap_size_executable_bytes ${detailedStats.total_heap_size_executable}`,

    `# HELP nodejs_heap_size_physical_bytes Total physical size of the V8 heap.`,
    `# TYPE nodejs_heap_size_physical_bytes gauge`,
    `nodejs_heap_size_physical_bytes ${detailedStats.total_physical_size}`,

    `# HELP nodejs_heap_available_size_bytes Total available size of the V8 heap.`,
    `# TYPE nodejs_heap_available_size_bytes gauge`,
    `nodejs_heap_available_size_bytes ${detailedStats.total_available_size}`,

    `# HELP nodejs_heap_used_bytes Amount of V8 heap currently in use.`,
    `# TYPE nodejs_heap_used_bytes gauge`,
    `nodejs_heap_used_bytes ${detailedStats.used_heap_size}`,

    `# HELP nodejs_heap_size_limit_bytes Heap size limit according to V8.`,
    `# TYPE nodejs_heap_size_limit_bytes gauge`,
    `nodejs_heap_size_limit_bytes ${detailedStats.heap_size_limit}`,

    `# HELP nodejs_process_rss_bytes Resident set size of the process.`,
    `# TYPE nodejs_process_rss_bytes gauge`,
    `nodejs_process_rss_bytes ${memUsage.rss}`,
  ];

  return new Response(metrics.join('\n\n') + '\n', {
    headers: {
      'Content-Type': 'text/plain',
    },
  });
};
