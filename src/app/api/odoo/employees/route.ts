import { createClient } from '@/data/supabase-server';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = async () => {
  const headersList = headers();
  const apiKey = headersList.get('x-api-key');
  if (!apiKey || apiKey !== process.env.ODOO_API_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  const supabase = createClient();
  const { data, error } = await supabase.from('employees').select('*');

  if (error) {
    return new NextResponse('Error fetching projects', { status: 500 });
  }

  return NextResponse.json(data);
};
