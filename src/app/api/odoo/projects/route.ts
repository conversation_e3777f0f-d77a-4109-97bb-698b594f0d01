import { createClient } from '@/data/supabase-server';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = async () => {
  const headersList = headers();
  const apiKey = headersList.get('x-api-key');
  if (!apiKey || apiKey !== process.env.ODOO_API_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  const supabase = createClient();
  const { data, error } = await supabase.from('bexio_projects').select('*');
  const { data: projects } = await supabase.from('projects').select('*');

  if (error) {
    return new NextResponse('Error fetching projects', { status: 500 });
  }

  const mappedData = data.map((curProj) => {
    const project = projects?.find((project) => project.bexio_project_id === curProj.bexio_id);
    return {
      ...curProj,
      clickup_list_id: project?.clickup_list_id ? Number(project.clickup_list_id) : null,
      clickup_user_id: project?.clickup_user_id,
    };
  });

  return NextResponse.json(mappedData);
};
