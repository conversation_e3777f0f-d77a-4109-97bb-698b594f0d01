import { createClient } from '@/data/supabase-server';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { createHash } from 'node:crypto';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = async () => {
  const headersList = headers();
  const apiKey = headersList.get('x-api-key');
  if (!apiKey || apiKey !== process.env.ODOO_API_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  const supabase = createClient();
  const { data, error } = await supabase
    .from('tasks')
    .select('*, projects(clickup_name)')
    .neq('bexio_work_package_id', null);

  if (error) {
    return new NextResponse('Error fetching projects', { status: 500 });
  }

  const string = JSON.stringify(data);
  const hash = createHash('md5').update(string).digest('hex');

  return NextResponse.json({ hash });
};
