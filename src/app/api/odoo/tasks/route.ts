import { createClient } from '@/data/supabase-server';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = async () => {
  const headersList = headers();
  const apiKey = headersList.get('x-api-key');
  if (!apiKey || apiKey !== process.env.ODOO_API_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  const supabase = createClient();
  const { data, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('is_client_task', true)
    .not('bexio_work_package_id', 'is', null);

  if (error) {
    return new NextResponse('Error fetching projects', { status: 500 });
  }

  const mappedData = data.map((curTask) => {
    const due_date = curTask.clickup_due_date ? new Date(curTask.clickup_due_date) : null;
    return {
      ...curTask,
      due_date: due_date ? due_date.toISOString() : null,
    };
  });

  return NextResponse.json(mappedData);
};
