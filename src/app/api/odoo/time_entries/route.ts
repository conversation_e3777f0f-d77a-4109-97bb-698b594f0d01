import { createClient } from '@/data/supabase-server';
import { NextResponse } from 'next/server';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export const GET = async () => {
  const headersList = headers();
  const apiKey = headersList.get('x-api-key');
  if (!apiKey || apiKey !== process.env.ODOO_API_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  const supabase = createClient();
  const { data, error } = await supabase
    .from('time_entries')
    .select('*')
    .eq('billable', true)
    .not('bexio_timesheet_id', 'is', null);

  if (error) {
    return new NextResponse('Error fetching projects', { status: 500 });
  }

  const mappedData = data.map((curTask) => {
    const start = curTask.clickup_start ? new Date(curTask.clickup_start) : null;
    return {
      ...curTask,
      start: start ? start.toISOString() : null,
    };
  });

  return NextResponse.json(mappedData);
};
