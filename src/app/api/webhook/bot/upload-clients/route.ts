import { NextRequest, NextResponse } from 'next/server';
import { submitBexioClients } from '@/jobs/sync/clients/submit-bexio-clients';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file');

    if (!file || !(file instanceof Blob)) {
      return new NextResponse('No file uploaded', { status: 400 });
    }

    const text = await file.text();
    await submitBexioClients.invoke({ csv: text });

    return new NextResponse('Success');
  } catch (error) {
    console.error('Error processing upload', error);
    return new NextResponse(`Error processing upload: ${JSON.stringify(error)}`, { status: 500 });
  }
}
