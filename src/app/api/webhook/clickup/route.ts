import { NextRequest } from 'next/server';
import { newClickupListCreateNewBexioProjectJob } from '@/jobs/sync/lists/new-clickup-list-create-new-bexio-project';
import { containsMeaningfulListHistory, isSectionChange } from '@/app/api/webhook/clickup/utils';
import { updatedClickupListUpdateBexioProjectJob } from '@/jobs';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { moveTaskToNewList } from '@/jobs/sync/tasks/move-task-to-new-list';
import { deletedClickupListJob } from '@/jobs/sync/lists/deleted-clickup-list-job';
import { taskDeletedJob } from '@/jobs/sync/tasks/task-deleted-job';
import { taskCreatedJob } from '@/jobs/sync/tasks/task-created-job';
import { taskUpdatedJob } from '@/jobs/sync/tasks/task-updated-job';
import { updatedClickupFolderJob } from '@/jobs/sync/folder/updated-clickup-folder';
import { waitUntil } from '@vercel/functions';
import { ingestClickupWebhook } from '@/sync-engine/persistence';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export async function POST(req: NextRequest) {
  const data = await req.json();

  waitUntil(ingestClickupWebhook(data));

  const { data: globalSettings } = await getGlobalSettings();
  if (!globalSettings?.webhooks_active) {
    console.log('Webhooks are not active');
    return new Response('Webhooks are not active');
  }

  console.log('Webhook Called: ', data.event);
  console.log('Data: ', data);

  switch (data.event) {
    case 'taskCreated':
      await taskCreatedJob.invoke({ createdTaskId: data.task_id });
      break;
    case 'taskUpdated':
      if (containsMeaningfulListHistory(data.history_items)) {
        if (isSectionChange(data.history_items)) {
          await moveTaskToNewList.invoke({ clickupTaskId: data.task_id });
        } else {
          await taskUpdatedJob.invoke({ updatedTaskId: data.task_id });
        }
      }
      break;
    case 'taskDeleted':
      await taskDeletedJob.invoke({ deletedTaskId: data.task_id });
      break;

    case 'listCreated':
      await newClickupListCreateNewBexioProjectJob.invoke({ newClickupListId: data.list_id });
      break;

    case 'listUpdated':
      await updatedClickupListUpdateBexioProjectJob.invoke({ updatedClickupListId: data.list_id });
      break;

    case 'listDeleted':
      await deletedClickupListJob.invoke({ deletedListId: data.list_id });
      break;

    case 'folderCreated':
      // error = await folderCreatedSyncAction(data.folder_id);
      break;

    case 'folderUpdated':
      await updatedClickupFolderJob.invoke({ updatedFolderId: data.folder_id });
      break;
  }

  return new Response('Success');
}
