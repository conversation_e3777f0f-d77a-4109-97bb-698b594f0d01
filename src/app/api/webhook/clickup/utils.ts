const permittedChanges = [
  'name',
  'archived',
  'section_moved',
  'time_estimate',
  'due_date',
  'assignee_add',
  'assignee_rem',
];

export function containsMeaningfulListHistory(listHistory: any[]) {
  if (!listHistory) {
    return true;
  }

  const isPermittedChange = listHistory?.some((item: any) => permittedChanges.includes(item.field));

  if (!isPermittedChange) {
    return false;
  }

  const nameChange = listHistory.find((item: any) => item.field === 'name');
  // if the prefix is changed, we don't want to trigger the job
  if (nameChange && !nameChange.before.startsWith('P-') && nameChange.after.startsWith('P-')) {
    return false;
  }

  return true;
}

export function isSectionChange(listHistory: any[]) {
  return listHistory?.some((item: any) => item.field === 'section_moved');
}
