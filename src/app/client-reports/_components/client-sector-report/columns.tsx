'use client';

import { ColumnDef } from '@tanstack/react-table';
import { SliderFilter } from '@/components/table/slider-filter';
import { prettyPrintCurrency, prettyPrintPercentage } from '@/lib/utils';
import { ColumnSortButton } from '@/components/table/column-sort-button';
import { Multiselect } from '@/components/table/multiselect';
import { MinMaxTableState } from '@/components/table/minmax';
import { RowData } from '@tanstack/table-core';
import {
  calculateAverage,
  calculateSum,
  multiSelectFilter,
  numericalSort,
  rangeFilter,
} from '@/components/table/filter-utils';

declare module '@tanstack/react-table' {
  interface TableState extends MinMaxTableState {}

  interface ColumnMeta<TData extends RowData, TValue> {
    readableName?: string;
  }
}

export type ClientsBySectorColumnType = {
  sector: string;
  servicesAmount: number;
  tradesAmount: number;
  revenue: number;
  percentage: number;
  revenueByClient: number;
  numClients: number;
  avgPercentage: number;
  totalBudgetDiff: number;
};

export const clientsBySectorColumns: ColumnDef<ClientsBySectorColumnType>[] = [
  {
    accessorKey: 'sector',
    meta: {
      readableName: 'Branche',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Branche
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
    footer: 'TOTAL',
  },
  {
    accessorKey: 'servicesAmount',
    meta: {
      readableName: 'Dienstleistungserlös (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Dienstleistungserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'servicesAmount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'tradesAmount',
    meta: {
      readableName: 'Handelserlös (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Handelserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'tradesAmount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'revenue',
    meta: {
      readableName: 'Umsatz (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Umsatz
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'revenue'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'percentage',
    meta: {
      readableName: 'Anteil %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Anteil %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintPercentage(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateSum(rows, 'percentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'revenueByClient',
    meta: {
      readableName: 'Ø Umsatz pro Kunde (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Ø Umsatz pro Kunde
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateAverage(rows, 'revenueByClient'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'numClients',
    meta: {
      readableName: 'Anzahl Kunden',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Anzahl Kunden
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{Number(row.renderValue() ?? 0)}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return calculateAverage(rows, 'numClients').toFixed(2);
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'avgPercentage',
    meta: {
      readableName: 'Durchschnitt ∆ Budget/eff. Kosten %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Durchschnitt ∆ Budget/eff. Kosten %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintPercentage(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'avgPercentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'totalBudgetDiff',
    meta: {
      readableName: 'Total ∆ Budget/eff. Kosten (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Total ∆ Budget/eff. Kosten
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateAverage(rows, 'totalBudgetDiff'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
];
