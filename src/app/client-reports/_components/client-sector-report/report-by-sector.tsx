import { Card, CardContent } from '@/components/ui/card';
import { getReportBySector } from '@/server-actions/clients/report-by-sector';
import { DataTable } from '@/components/table/data-table';
import { clientsBySectorColumns } from '@/app/client-reports/_components/client-sector-report/columns';

type ReportBySectorProps = {
  start: string;
  end: string;
};

export async function ReportBySector({ start, end }: ReportBySectorProps) {
  const res = await getReportBySector(start, end);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const revenueBySector = res.data!;

  return (
    <Card>
      <CardContent className="p-4">
        <DataTable title="Nach Branche" columns={clientsBySectorColumns} data={revenueBySector} />
      </CardContent>
    </Card>
  );
}
