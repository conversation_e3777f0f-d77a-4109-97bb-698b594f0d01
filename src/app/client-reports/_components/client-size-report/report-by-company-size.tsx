import { Card, CardContent } from '@/components/ui/card';
import { getReportByCompanySize } from '@/server-actions/clients/report-by-company-size';
import { DataTable } from '@/components/table/data-table';
import { clientsBySizeColumns } from '@/app/client-reports/_components/client-size-report/columns';

type ReportByCompanySizeProps = {
  start: string;
  end: string;
};

export async function ReportByCompanySize({ start, end }: ReportByCompanySizeProps) {
  const res = await getReportByCompanySize(start, end);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const revenueByCompanySize = res.data!;

  return (
    <Card>
      <CardContent className="p-4">
        <DataTable title="Nach Unternehmensgrösse" columns={clientsBySizeColumns} data={revenueByCompanySize} />
      </CardContent>
    </Card>
  );
}
