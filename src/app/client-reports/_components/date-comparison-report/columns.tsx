'use client';

import { ColumnDef } from '@tanstack/react-table';
import { SliderFilter } from '@/components/table/slider-filter';
import { prettyPrintCurrency, prettyPrintPercentage } from '@/lib/utils';
import { ColumnSortButton } from '@/components/table/column-sort-button';
import { MinMaxTableState } from '@/components/table/minmax';
import { RowData } from '@tanstack/table-core';
import {
  calculateAverage,
  calculateSum,
  multiSelectFilter,
  numericalSort,
  rangeFilter,
} from '@/components/table/filter-utils';
import { Multiselect } from '@/components/table/multiselect';

declare module '@tanstack/react-table' {
  interface TableState extends MinMaxTableState {}

  interface ColumnMeta<TData extends RowData, TValue> {
    key?: string;
    readableName?: string;
  }
}

export type DateComparisonColumnType = {
  client: string;
  firstRevenue: number;
  secondRevenue: number;
  diff: number;
  diffPercent: number;
};

export const dateComparisonColumns: ColumnDef<DateComparisonColumnType>[] = [
  {
    accessorKey: 'client',
    meta: {
      readableName: 'Kunde',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Kunde
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
    footer: 'TOTAL',
  },
  {
    accessorKey: 'firstRevenue',
    meta: {
      key: 'firstRevenue',
      readableName: 'Umsatz Periode 1',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Umsatz Periode 1
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'firstRevenue'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'secondRevenue',
    meta: {
      key: 'secondRevenue',
      readableName: 'Umsatz Periode 2',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Umsatz Periode 2
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'secondRevenue'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'diff',
    meta: {
      readableName: '∆ in CHF',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ in CHF
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'diff'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'diffPercent',
    meta: {
      readableName: '∆ in %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ in %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintPercentage(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'diffPercent'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
];
