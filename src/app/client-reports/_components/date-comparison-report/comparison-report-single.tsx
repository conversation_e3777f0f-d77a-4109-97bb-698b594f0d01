'use client';

import { Card, CardContent } from '@/components/ui/card';
import { prettyPrintDateShort } from '@/lib/utils';
import { DataTable } from '@/components/table/data-table';
import { dateComparisonColumns } from '@/app/client-reports/_components/date-comparison-report/columns';
import { ColumnSortButton } from '@/components/table/column-sort-button';
import { SliderFilter } from '@/components/table/slider-filter';

type ComparisonReportProps = {
  revenueTitle?: string;
  revenueComparison: {
    client: string;
    firstRevenue: number;
    secondRevenue: number;
    diff: number;
    diffPercent: number;
  }[];
  firstStart: string;
  firstEnd: string;
  secondStart: string;
  secondEnd: string;
};

export async function ComparisonReportSingle({
  revenueTitle = 'Umsatz',
  revenueComparison,
  firstStart,
  firstEnd,
  secondStart,
  secondEnd,
}: ComparisonReportProps) {
  const columns = dateComparisonColumns.map((column) => {
    if (column.meta?.key === 'firstRevenue') {
      column.meta.readableName = `Umsatz Periode ${prettyPrintDateShort(new Date(firstStart))} - ${prettyPrintDateShort(
        new Date(firstEnd),
      )} (CHF)`;
      column.header = ({ column, table }) => {
        return (
          <div className="flex items-center">
            Umsatz Periode {prettyPrintDateShort(new Date(firstStart))} - {prettyPrintDateShort(new Date(firstEnd))}
            <ColumnSortButton column={column} />
            <SliderFilter column={column} table={table} type="CHF" />
          </div>
        );
      };
    } else if (column.meta?.key === 'secondRevenue') {
      column.meta.readableName = `Umsatz Periode ${prettyPrintDateShort(new Date(secondStart))} - ${prettyPrintDateShort(
        new Date(secondEnd),
      )} (CHF)`;
      column.header = ({ column, table }) => {
        return (
          <div className="flex items-center">
            Umsatz Periode {prettyPrintDateShort(new Date(secondStart))} - {prettyPrintDateShort(new Date(secondEnd))}
            <ColumnSortButton column={column} />
            <SliderFilter column={column} table={table} type="CHF" />
          </div>
        );
      };
    }
    return column;
  });

  return (
    <Card>
      <CardContent className="p-4">
        <DataTable title={`Vergleich ${revenueTitle}`} columns={columns} data={revenueComparison} />
      </CardContent>
    </Card>
  );
}
