import { getClientRevenueComparison } from '@/server-actions/clients/client-revenue-comparison';
import { ComparisonReportSingle } from '@/app/client-reports/_components/date-comparison-report/comparison-report-single';

type ComparisonReportProps = {
  firstStart: string;
  firstEnd: string;
  secondStart: string;
  secondEnd: string;
};

export async function ComparisonReport({ firstStart, firstEnd, secondStart, secondEnd }: ComparisonReportProps) {
  const res = await getClientRevenueComparison(firstStart, firstEnd, secondStart, secondEnd);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  if (!res.data) {
    return <div>No data</div>;
  }

  const revenueComparison = res.data.revenueComparison;
  const servicesComparison = res.data.servicesComparison;
  const tradesComparison = res.data.tradesComparison;

  return (
    <div className="flex flex-wrap gap-4 justify-center">
      <ComparisonReportSingle
        revenueTitle="Dienstleistungserlös"
        revenueComparison={servicesComparison}
        firstStart={firstStart}
        firstEnd={firstEnd}
        secondStart={secondStart}
        secondEnd={secondEnd}
      />
      <ComparisonReportSingle
        revenueTitle="Handelserlös"
        revenueComparison={tradesComparison}
        firstStart={firstStart}
        firstEnd={firstEnd}
        secondStart={secondStart}
        secondEnd={secondEnd}
      />
      <ComparisonReportSingle
        revenueTitle="Gesamtumsatz"
        revenueComparison={revenueComparison}
        firstStart={firstStart}
        firstEnd={firstEnd}
        secondStart={secondStart}
        secondEnd={secondEnd}
      />
    </div>
  );
}
