import { Card, CardContent } from '@/components/ui/card';
import { prettyPrintDateShort } from '@/lib/utils';
import { getLostClients } from '@/server-actions/clients/lost-clients';
import { DataTable } from '@/components/table/data-table';
import { lostClientsColumns } from '@/app/client-reports/_components/lost-clients-report/columns';

type LostClientsProps = {
  start: string;
};

export async function LostClients({ start }: LostClientsProps) {
  const res = await getLostClients(start);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const newClients = res.data!;

  return (
    <Card className="max-w-full">
      <CardContent className="p-4">
        <DataTable
          title={`Verlorene <PERSON> (Nach ${prettyPrintDateShort(new Date(start))})`}
          columns={lostClientsColumns}
          data={newClients}
        />
      </CardContent>
    </Card>
  );
}
