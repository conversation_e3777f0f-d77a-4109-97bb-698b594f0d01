'use client';

import { ColumnDef } from '@tanstack/react-table';
import { SliderFilter } from '@/components/table/slider-filter';
import { prettyPrintCurrency } from '@/lib/utils';
import { ColumnSortButton } from '@/components/table/column-sort-button';
import { Multiselect } from '@/components/table/multiselect';
import { MinMaxTableState } from '@/components/table/minmax';
import { RowData } from '@tanstack/table-core';
import { calculateSum, multiSelectFilter, numericalSort, rangeFilter } from '@/components/table/filter-utils';

declare module '@tanstack/react-table' {
  interface TableState extends MinMaxTableState {}

  interface ColumnMeta<TData extends RowData, TValue> {
    readableName?: string;
  }
}

export type NewClientsColumnType = {
  client: string;
  servicesAmount: number;
  tradesAmount: number;
  revenue: number;
};

export const newClientsColumns: ColumnDef<NewClientsColumnType>[] = [
  {
    accessorKey: 'client',
    meta: {
      readableName: 'Kunde',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Kunde
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
    footer: 'TOTAL',
  },
  {
    accessorKey: 'servicesAmount',
    meta: {
      readableName: 'Dienstleistungserlös (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Dienstleistungserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'servicesAmount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'tradesAmount',
    meta: {
      readableName: 'Handelserlös (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Handelserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'tradesAmount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'revenue',
    meta: {
      readableName: 'Umsatz (CHF)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Umsatz
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="CHF" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'revenue'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
];
