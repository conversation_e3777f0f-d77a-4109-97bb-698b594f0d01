import { Card, CardContent } from '@/components/ui/card';
import { getNewClients } from '@/server-actions/clients/new-clients';
import { DataTable } from '@/components/table/data-table';
import { newClientsColumns } from '@/app/client-reports/_components/new-clients-report/columns';

type NewClientsProps = {
  start: string;
  end: string;
};

export async function NewClients({ start, end }: NewClientsProps) {
  const res = await getNewClients(start, end);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const newClients = res.data!;

  return (
    <Card>
      <CardContent className="p-4">
        <DataTable title="Neukunden" columns={newClientsColumns} data={newClients} />
      </CardContent>
    </Card>
  );
}
