import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { prettyPrintCurrency } from '@/lib/utils';
import { getNoClients } from '@/server-actions/clients/no-clients';

export async function NoClients() {
  const res = await getNoClients();

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const newClients = res.data;
  const total = newClients?.reduce((acc, [_, revs]) => acc + revs.revenue, 0);

  return (
    <Card className="min-w-[200px]">
      <CardHeader>Keine <PERSON></CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap">Kunde</TableHead>
              <TableHead className="whitespace-nowrap">Umsatz</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {newClients?.map(([kunde, revenue]) => (
              <TableRow key={kunde}>
                <TableCell className="whitespace-nowrap">{kunde}</TableCell>
                <TableCell className="whitespace-nowrap">{prettyPrintCurrency(revenue)}</TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="whitespace-nowrap">Total</TableCell>
              <TableCell className="whitespace-nowrap">{prettyPrintCurrency(total)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
