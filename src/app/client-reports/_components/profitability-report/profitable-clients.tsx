import { Card, CardContent } from '@/components/ui/card';
import { getProfitableClients } from '@/server-actions/clients/profitable-clients';
import { DataTable } from '@/components/table/data-table';
import { profitableClientsColumns } from '@/app/client-reports/_components/profitability-report/columns';

type ProfitableClientsProps = {
  title: string;
  reverse?: boolean;
  start: string;
  end: string;
};

export async function ProfitableClients({ start, end, title, reverse = false }: ProfitableClientsProps) {
  const res = await getProfitableClients(start, end, reverse);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const top20Clients = res.data;
  if (!top20Clients) {
    return <div>No data</div>;
  }

  return (
    <Card className="max-w-full">
      <CardContent className="p-4">
        <DataTable title={title} columns={profitableClientsColumns} data={top20Clients} />
      </CardContent>
    </Card>
  );
}
