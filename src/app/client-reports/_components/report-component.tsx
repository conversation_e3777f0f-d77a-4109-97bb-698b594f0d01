import { Suspense } from 'react';
import { ProfitableClients } from '@/app/client-reports/_components/profitability-report/profitable-clients';
import { Top20Clients } from '@/app/client-reports/_components/top-20-report/top-20-clients';
import { NewClients } from '@/app/client-reports/_components/new-clients-report/new-clients';
import { NoClients } from '@/app/client-reports/_components/no-clients';
import { ReportBySector } from '@/app/client-reports/_components/client-sector-report/report-by-sector';
import { ReportByCompanySize } from '@/app/client-reports/_components/client-size-report/report-by-company-size';
import { ComparisonReport } from '@/app/client-reports/_components/date-comparison-report/comparison-report';
import { LostClients } from '@/app/client-reports/_components/lost-clients-report/lost-clients';

type ReportComponentProps = {
  start: string;
  end: string;
  secondStart?: string;
  secondEnd?: string;
  report?: string;
};

export async function ReportComponent({ start, end, secondStart, secondEnd, report }: ReportComponentProps) {
  const RenderedReportComponent = () => {
    switch (report) {
      case 'profitable-clients':
        return <ProfitableClients title="Rentable Kunden" start={start} end={end} />;
      case 'non-profitable-clients':
        return <ProfitableClients reverse={true} title="Nicht rentable Kunden" start={start} end={end} />;
      case 'top-20-clients':
        return <Top20Clients start={start} end={end} />;
      case 'new-clients':
        return <NewClients start={start} end={end} />;
      case 'no-clients':
        return <NoClients />;
      case 'report-by-sector':
        return <ReportBySector start={start} end={end} />;
      case 'report-by-company-size':
        return <ReportByCompanySize start={start} end={end} />;
      case 'lost-clients':
        return <LostClients start={start} />;
      case 'comparison-report':
        if (!secondStart || !secondEnd) {
          return <div>Bitte zwei Perioden angeben</div>;
        }
        return <ComparisonReport firstStart={start} firstEnd={end} secondStart={secondStart} secondEnd={secondEnd} />;
      default:
        return <div></div>;
    }
  };

  return <Suspense fallback={<div>Loading...</div>}>{<RenderedReportComponent />}</Suspense>;
}
