'use client';

import { CustomTimeRange } from '@/components/custom-time-range';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { reportOptions } from '@/app/client-reports/_components/reports-options';
import { Button } from '@/components/ui/button';
import { useMemo, useState } from 'react';
import { getISODateString } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { SimpleTimeRange } from '@/components/simple-time-range';

type ReportSelectorProps = {
  startDate: string;
  endDate: string;
  secondStartDate?: string;
  secondEndDate?: string;
  defaultRange?: string;
  defaultReport?: string;
  defaultSingleTime?: string;
};

export function SelectReport({
  startDate,
  endDate,
  secondStartDate,
  secondEndDate,
  defaultRange = 'this-year',
  defaultReport,
  defaultSingleTime,
}: ReportSelectorProps) {
  const [[start, end], setDateRange] = useState<[Date, Date]>([new Date(startDate), new Date(endDate)]);
  const [[secondStart, secondEnd], setSecondDateRange] = useState<[Date, Date]>([
    new Date(secondStartDate || startDate),
    new Date(secondEndDate || endDate),
  ]);
  const [range, setRange] = useState<string | undefined>(defaultRange);
  const [report, setReport] = useState<string | undefined>(defaultReport);
  const [selectedSingleTime, setSelectedSingleTime] = useState<string | undefined>(defaultSingleTime);
  const router = useRouter();

  const onChangeDateRange = (start: Date, end: Date, range?: string) => {
    setDateRange([start, end]);
    if (range) {
      setRange(range);
    }
  };

  const onChangeSecondDateRange = (start: Date, end: Date) => {
    setSecondDateRange([start, end]);
  };

  const handleChangeReport = (report: string) => {
    setReport(report);
    if (report === 'comparison-report') {
      setDateRange([
        new Date(`${new Date().getFullYear() - 1}-01-01`),
        new Date(`${new Date().getFullYear() - 1}-12-31`),
      ]);
      setSecondDateRange([
        new Date(`${new Date().getFullYear()}-01-01`),
        new Date(`${new Date().getFullYear()}-12-31`),
      ]);
    }
  };

  const onChangeFirstStart = (newSelection: string) => {
    setSelectedSingleTime(newSelection);
    switch (newSelection) {
      case '3-months':
        setDateRange([new Date(new Date().setMonth(new Date().getMonth() - 3)), new Date()]);
        break;
      case '6-months':
        setDateRange([new Date(new Date().setMonth(new Date().getMonth() - 6)), new Date()]);
        break;
      case '1-year':
        setDateRange([new Date(new Date().setFullYear(new Date().getFullYear() - 1)), new Date()]);
        break;
      case '2-years':
        setDateRange([new Date(new Date().setFullYear(new Date().getFullYear() - 2)), new Date()]);
        break;
    }
  };

  const handleGo = () => {
    if (!report) {
      return;
    }

    const searchParams = new URLSearchParams();
    searchParams.set('startDate', getISODateString(start));
    searchParams.set('endDate', getISODateString(end));
    searchParams.set('report', report);
    if (range) {
      searchParams.set('range', range);
    }
    if (secondStart && secondEnd) {
      searchParams.set('secondStartDate', getISODateString(secondStart));
      searchParams.set('secondEndDate', getISODateString(secondEnd));
    }
    if (selectedSingleTime) {
      searchParams.set('singleTime', selectedSingleTime);
    }

    router.push('client-reports?' + searchParams.toString(), {});
  };

  const selectedReport = useMemo(() => {
    return reportOptions.find((r) => r.key === report);
  }, [report]);

  return (
    <div className="flex gap-2">
      <Select onValueChange={handleChangeReport} defaultValue={defaultReport}>
        <SelectTrigger className="w-[277px]">
          <SelectValue placeholder="Wähle einen Report" />
        </SelectTrigger>
        <SelectContent>
          {reportOptions.map((option) => (
            <SelectItem key={option.key} value={option.key}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {selectedReport?.timeRange && (
        <CustomTimeRange
          start={start}
          end={end}
          onChange={onChangeDateRange}
          earliestDate={new Date('2022-01-01')}
          defaultValue={defaultRange}
        />
      )}
      {selectedReport?.doubleTimeRange && (
        <div className="flex flex-col gap-2">
          <SimpleTimeRange
            defaultStart={new Date(startDate || start)}
            defaultEnd={new Date(endDate || end)}
            onChange={onChangeDateRange}
          />
          <SimpleTimeRange
            defaultStart={new Date(secondStartDate || secondStart)}
            defaultEnd={new Date(secondEndDate || secondEnd)}
            onChange={onChangeSecondDateRange}
          />
        </div>
      )}
      {selectedReport?.singleTime && (
        <div className="flex gap-2 items-center">
          <Select onValueChange={onChangeFirstStart} value={selectedSingleTime}>
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Wähle eine Zeitspanne" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="3-months">3 Monate</SelectItem>
                <SelectItem value="6-months">6 Monate</SelectItem>
                <SelectItem value="1-year">1 Jahr</SelectItem>
                <SelectItem value="2-years">2 Jahre</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      )}

      <Button onClick={handleGo}>GO!</Button>
    </div>
  );
}
