import { getTop20ClientsByRevenue } from '@/server-actions/clients/top-20-clients';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/table/data-table';
import { top20ClientsColumns } from '@/app/client-reports/_components/top-20-report/columns';

type Top20ClientsProps = {
  start: string;
  end: string;
};

export async function Top20Clients({ start, end }: Top20ClientsProps) {
  const res = await getTop20ClientsByRevenue(start, end);

  if (res.error) {
    return <div>Error: {res.error}</div>;
  }

  const top20Clients = res.data!;

  return (
    <Card className="min-w-[200px]">
      <CardContent className="p-4">
        <DataTable title="Top 20 Kunden" columns={top20ClientsColumns} data={top20Clients} />
      </CardContent>
    </Card>
  );
}
