import { getPermission } from '@/lib/employee';
import { SelectReport } from '@/app/client-reports/_components/select-report';
import { ReportComponent } from '@/app/client-reports/_components/report-component';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

type ClientReportsPageProps = {
  searchParams: {
    startDate: string | undefined;
    endDate: string | undefined;
    secondStartDate: string | undefined;
    secondEndDate: string | undefined;
    range: string | undefined;
    report: string | undefined;
    singleTime: string | undefined;
  };
};

async function ClientReportsPage({ searchParams }: ClientReportsPageProps) {
  const permission = await getPermission('client-reports');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  const start = searchParams.startDate ? new Date(searchParams.startDate) : new Date('2024-01-01');
  const end = searchParams.endDate ? new Date(searchParams.endDate) : new Date();
  const secondStart = searchParams.secondStartDate ? new Date(searchParams.secondStartDate) : new Date('2024-01-01');
  const secondEnd = searchParams.secondEndDate ? new Date(searchParams.secondEndDate) : new Date();

  return (
    <div className="flex flex-col gap-4 items-center mt-4 w-full">
      <SelectReport
        startDate={start.toISOString()}
        endDate={end.toISOString()}
        secondStartDate={secondStart.toISOString()}
        secondEndDate={secondEnd.toISOString()}
        defaultRange={searchParams.range}
        defaultReport={searchParams.report}
        defaultSingleTime={searchParams.singleTime}
      />
      <ReportComponent
        start={start.toISOString()}
        end={end.toISOString()}
        secondStart={secondStart.toISOString()}
        secondEnd={secondEnd.toISOString()}
        report={searchParams.report}
      />
    </div>
  );
}

export default ClientReportsPage;
