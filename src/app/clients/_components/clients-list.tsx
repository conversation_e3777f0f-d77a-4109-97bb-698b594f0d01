import { getDomainClients } from '@/server-actions/domain-clients';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { prettyPrintCurrency } from '@/lib/utils';
import { IconExternalLink } from '@tabler/icons-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export async function ClientsList() {
  const { error, data } = await getDomainClients();

  if (error) {
    return <div>Error</div>;
  }

  return (
    <div className="flex gap-4 justify-center mt-4">
      <ScrollArea className="flex flex-col gap-4 max-h-[80vh]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Firma</TableHead>
              <TableHead>Verrechnete Dienstleistungen</TableHead>
              <TableHead>ClickUp</TableHead>
              <TableHead>Bexio</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map((client) => {
              return (
                <TableRow key={client.id}>
                  <TableCell>{client.name}</TableCell>
                  <TableCell>{prettyPrintCurrency(client.services_amount)}</TableCell>
                  <TableCell>
                    <Link
                      href={`https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/f/${client.clickup_folder_id}/${process.env.CLICKUP_WORKSPACE_ID}`}
                      target="_blank"
                    >
                      <Button variant="outline" size="sm">
                        <IconExternalLink width={20} />
                      </Button>
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`https://office.bexio.com/index.php/kontakt/show/id/${client.bexio_contact_id}`}
                      target="_blank"
                    >
                      <Button variant="outline" size="sm">
                        <IconExternalLink width={20} />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </ScrollArea>
    </div>
  );
}
