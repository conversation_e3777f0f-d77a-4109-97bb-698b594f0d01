import { CustomSidebar } from '@/components/custom-sidebar';
import { ClientsList } from '@/app/clients/_components/clients-list';
import { getPermission } from '@/lib/employee';
import { Card, CardContent } from '@/components/ui/card';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function Clients() {
  const permission = await getPermission('clients');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  return (
    <div className="w-full flex justify-center">
      <Card className="w-fit mt-4">
        <CardContent>
          <ClientsList />
        </CardContent>
      </Card>
    </div>
  );
}

export default Clients;
