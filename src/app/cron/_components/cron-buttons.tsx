'use client';

import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useRunDetails } from '@trigger.dev/react';
import { checkNexBexioContacts, checkProjectClientOwnershipDifferences } from '@/server-actions/cron-buttons';

export function CronButtons() {
  const [jobId, setJobId] = useState<string>();
  const { isLoading, isError, data, error } = useRunDetails(jobId);

  const handleSyncClients = async () => {
    // const { id } = await checkNexBexioContacts();
    // setJobId(id);
  };

  const handleSyncClickUpLists = async () => {
    // const { id } = await checkProjectClientOwnershipDifferences();
    // setJobId(id);
  };

  const handleSyncTimeentries = async () => {
    // const { id } = await syncClickupTimeEntriesToBexioTimesheet();
    setJobId('');
  };

  return (
    <div className="flex gap-4 justify-center mt-12">
      <div className="flex flex-col gap-2">
        <p>Läuft jede Minute</p>
        <Button onClick={handleSyncClients} disabled={isLoading}>
          Bexio Contacts {'->'} ClickUp Folder
        </Button>

        <p>Läuft jede 5 Minuten</p>
        <Button onClick={handleSyncClickUpLists} disabled={isLoading}>
          Move Lists between Folders {'<->'} Move Bexio Projects between Clients
        </Button>

        <p>Läuft 1 mal am Tag (00:00)</p>
        <Button onClick={handleSyncTimeentries} disabled={isLoading}>
          Sync ClickUp Time Entries {'<->'} Bexio Timesheets
        </Button>

        {isLoading && <p>Loading...</p>}
        {isError && <p>Error: {error?.message}</p>}
        {(data?.status === 'EXECUTING' || data?.status.startsWith('WAITING')) && <p>Sync in progress...</p>}
        {data?.status === 'SUCCESS' && <p>Sync completed</p>}
        {data?.status === 'FAILURE' && <p>Sync failed</p>}
      </div>
    </div>
  );
}
