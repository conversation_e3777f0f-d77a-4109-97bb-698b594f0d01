import { CustomSidebar } from '@/components/custom-sidebar';
import { CronButtons } from '@/app/cron/_components/cron-buttons';
import { getPermission } from '@/lib/employee';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function CronPage() {
  const permission = await getPermission('cron');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  return (
    <main className="grid h-screen min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
      <CustomSidebar />
      <div>
        <CronButtons />
      </div>
    </main>
  );
}

export default CronPage;
