'use client';

import { Tables } from '@/types/gen/database-table';
import { PermissionSetting } from '@/app/employees/[id]/_components/permission-setting';
import { permissionsOptions } from '@/app/employees/[id]/_components/permissionsOptions';
import { usePermissions } from '@/app/employees/[id]/_components/use-permissions';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from '@radix-ui/react-icons';

type EmployeeSettingsProps = {
  user: Tables<'employees'>;
};

export function EmployeeSettings({ user }: EmployeeSettingsProps) {
  const { handlePermissionChange, submitPermissions, getSelectedPermission } = usePermissions(user);

  return (
    <div className="flex gap-4 justify-center mt-4">
      <div className="flex flex-col gap-6">
        <div className="flex gap-3 items-center">
          <Button variant="outline" size="sm" onClick={() => window.history.back()}>
            <ArrowLeftIcon />
          </Button>
          <p className="text-xl">Benutzerrechte für {user.name}</p>
        </div>
        <div className="flex gap-4  flex-wrap">
          {permissionsOptions.map((option) => (
            <PermissionSetting
              key={option.pageKey}
              pageKey={option.pageKey}
              options={option.options}
              activeSelection={getSelectedPermission(option.pageKey)}
              onChange={handlePermissionChange}
            />
          ))}
        </div>
        <div className="flex justify-end">
          <Button onClick={submitPermissions}>Speichern</Button>
        </div>
      </div>
    </div>
  );
}
