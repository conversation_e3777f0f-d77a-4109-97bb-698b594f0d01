import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { permissionPages } from '@/app/employees/[id]/_components/permissions';
import { Checkbox } from '@/components/ui/checkbox';

type PermissionSettingProps = {
  pageKey: string;
  options: {
    key: string;
    label: string;
  }[];
  onChange: (pageKey: string, option: string) => void;
  activeSelection: string;
};

export function PermissionSetting({ pageKey, options, onChange, activeSelection }: PermissionSettingProps) {
  const permission = permissionPages.find((pp) => pp.key == pageKey);

  if (!permission) {
    return <div>No permission found</div>;
  }

  return (
    <Card className="flex flex-col basis-1/6 grow min-w-[187px]">
      <CardHeader className="p-4 flex-row items-center gap-1">
        <permission.icon height={20} width={20} /> {permission?.label}
      </CardHeader>
      <CardContent className="p-4 pt-0 flex flex-col gap-2">
        {options.map((option) => (
          <div key={option.key} className="flex gap-1 items-center">
            <Checkbox
              id={option.key}
              key={option.key}
              name={option.key}
              value={option.key}
              onClick={() => onChange(pageKey, option.key)}
              checked={activeSelection === option.key}
            />
            <label
              htmlFor={option.key}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {option.label}
            </label>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
