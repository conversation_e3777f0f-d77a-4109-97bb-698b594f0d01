import {
  IconCalendarTime,
  IconClipboardCopy,
  IconDashboard,
  IconGauge,
  IconHome,
  IconPackage,
  IconReport,
  IconReportAnalytics,
  IconSettings,
  IconUsers,
  IconUserSquare,
} from '@tabler/icons-react';

export const permissionPages = [
  {
    key: 'home',
    label: 'Home',
    icon: IconHome,
  },
  {
    key: 'clients',
    label: 'Kunden',
    icon: IconUsers,
  },
  {
    key: 'client-reports',
    label: 'Kunden Reporting',
    icon: IconClipboardCopy,
  },
  {
    key: 'projects',
    label: 'Projekte',
    icon: IconPackage,
  },
  {
    key: 'project-reports',
    label: 'Projekt Reporting',
    icon: IconGauge,
  },
  {
    key: 'time-entries',
    label: 'Zeiteinträge',
    icon: IconReport,
  },
  {
    key: 'time-reporting',
    label: 'Time Reporting',
    icon: IconReportAnalytics,
  },
  {
    key: 'time-dashboard',
    label: 'Time Dashboard',
    icon: IconDashboard,
  },
  {
    key: 'employees',
    label: 'Mi<PERSON>beiter',
    icon: IconUserSquare,
  },
  {
    key: 'working-hours',
    label: 'Arbeitszeiten',
    icon: IconCalendarTime,
  },
  {
    key: 'recurring-tasks',
    label: 'Recurring Tasks',
    icon: IconClipboardCopy,
  },
  {
    key: 'settings',
    label: 'Einstellungen',
    icon: IconSettings,
  },
];
