import { Tables } from '@/types/gen/database-table';
import { useState } from 'react';
import { PermissionSelection, permissionsOptions } from '@/app/employees/[id]/_components/permissionsOptions';
import { updatePermissions } from '@/server-actions/permissions';
import { useToast } from '@/components/ui/use-toast';

type UsePermissionsProps = Tables<'employees'>;
export function usePermissions(user: UsePermissionsProps) {
  const defaultPermissions = permissionsOptions.map((option) => {
    return { pageKey: option.pageKey, option: 'inactive' };
  });
  const defaultUserPermissions = Array.isArray(user.access)
    ? (user.access as PermissionSelection[])
    : defaultPermissions;
  const [permissions, setPermissions] = useState<PermissionSelection[]>(defaultUserPermissions);
  const { toast } = useToast();

  const handlePermissionChange = (pageKey: string, option: string) => {
    setPermissions((prev) => {
      if (prev.some((p) => p.pageKey === pageKey)) {
        return prev.map((p) => {
          if (p.pageKey === pageKey) {
            return { pageKey, option };
          }
          return p;
        });
      }

      return prev.concat({ pageKey, option });
    });
  };

  const getPermission = (pageKey: string) => {
    const permission = permissions.find((p) => p.pageKey === pageKey);
    if (!permission) {
      return 'inactive';
    }
    return permission.option;
  };

  const submitPermissions = async () => {
    const { error } = await updatePermissions(user.user_id, permissions);
    if (!error) {
      toast({
        title: 'Berechtigungen aktualisiert',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Fehler beim Aktualisieren der Berechtigungen',
      });
      console.error('Fehler beim Aktualisieren der Berechtigungen', error);
    }
  };

  return {
    permissions,
    getSelectedPermission: getPermission,
    handlePermissionChange,
    submitPermissions,
  };
}
