import { CustomSidebar } from '@/components/custom-sidebar';
import { createClient } from '@/data/supabase-server';
import { EmployeeSettings } from '@/app/employees/[id]/_components/employee-settings';
import { Suspense } from 'react';
import { getPermission } from '@/lib/employee';

type EmployeePageParams = {
  params: {
    id: string;
  };
};

export default async function EmployeePage({ params: { id } }: EmployeePageParams) {
  const permission = await getPermission('employees');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  const supabase = createClient();
  const { data, error } = await supabase.from('employees').select('*').eq('user_id', id).single();

  if (error) {
    return <div>Error</div>;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EmployeeSettings user={data} />
    </Suspense>
  );
}
