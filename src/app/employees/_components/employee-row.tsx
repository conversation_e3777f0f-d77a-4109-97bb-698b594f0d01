import { TableCell, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  CalendarIcon,
  EyeClosedIcon,
  EyeOpenIcon,
  GearIcon,
  LockClosedIcon,
  OpenInNewWindowIcon,
} from '@radix-ui/react-icons';
import React, { useState } from 'react';
import { TimePicker } from '@/components/ui/time-picker';
import { createDateFromTimeString, createTimeStringFromDate } from '@/components/ui/time-picker-utils';
import { EmployeeActionType } from '@/app/employees/_components/employees-list';
import { IconCalendarOff } from '@tabler/icons-react';
import { SimpleTooltip } from '@/components/simple-tooltip';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

type EmployeeRowProps = {
  employee: {
    bexio_user_id: number | null;
    clickup_user_id: string | null;
    email: string;
    inactive: boolean;
    name: string;
    profile_picture_url: string | null;
    user_id: string;
    min_work_start: string;
    max_work_end: string;
    active_time_report: boolean;
    max_working_hours: number;
    allow_work_during_pto: boolean;
  };
  handleEmployeeAction: (employeeId: string, action: EmployeeActionType, payload?: any) => () => void;
};

export function EmployeeRow({ employee, handleEmployeeAction }: EmployeeRowProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const handleToggle = () => setIsOpen((prev) => !prev);

  const [minWorkStart, setMinWorkStart] = useState<Date>(new Date(createDateFromTimeString(employee.min_work_start)));
  const [maxWorkEnd, setMaxWorkEnd] = useState<Date>(new Date(createDateFromTimeString(employee.max_work_end)));
  const [maxWorkEndInHours, setMaxWorkEndInHours] = useState<string>(employee.max_working_hours.toString());
  const [allowWorkDuringPTO, setAllowWorkDuringPTO] = useState<boolean>(employee.allow_work_during_pto);

  const handleSave = async () => {
    handleEmployeeAction(String(employee.clickup_user_id), 'save', {
      min_work_start: createTimeStringFromDate(minWorkStart),
      max_work_end: createTimeStringFromDate(maxWorkEnd),
      max_working_hours: maxWorkEndInHours,
      allow_work_during_pto: allowWorkDuringPTO,
    })();
  };

  const handleRedirectUserPermission = (userId: string) => () => {
    router.push(`/employees/${userId}`);
  };

  return (
    <>
      <TableRow key={employee.user_id} className={cn(isOpen && 'border-none')}>
        <TableCell>
          <Avatar>
            <AvatarImage src={employee.profile_picture_url || ''} />
            <AvatarFallback>
              {employee.name?.split(' ')[0][0]}
              {employee.name?.split(' ')[1][0]}
            </AvatarFallback>
          </Avatar>
        </TableCell>
        <TableCell>
          <p className={cn(employee.inactive && 'text-gray-400')}>{employee.name}</p>
        </TableCell>
        <TableCell>
          <p className={cn(employee.inactive && 'text-gray-400')}>{employee.email}</p>
        </TableCell>
        <TableCell>
          <p className={cn(employee.inactive && 'text-gray-400')}>{employee.clickup_user_id}</p>
        </TableCell>
        <TableCell>
          <p className={cn(employee.inactive && 'text-gray-400')}>{employee.bexio_user_id}</p>
        </TableCell>
        <TableCell className="flex gap-2">
          {employee.inactive ? (
            <>
              <Button onClick={handleEmployeeAction(String(employee.clickup_user_id), 'activate')} variant="outline">
                <EyeOpenIcon />
              </Button>
              {employee.active_time_report ? (
                <SimpleTooltip content="Time Dashboard deaktivieren (Ist aktuell aktiv)">
                  <Button
                    onClick={handleEmployeeAction(String(employee.clickup_user_id), 'deactivate-time-report')}
                    variant="outline"
                  >
                    <IconCalendarOff className="h-4 w-4" />
                  </Button>
                </SimpleTooltip>
              ) : (
                <SimpleTooltip content="Time Dashbaord aktivieren (Ist aktuell nicht aktiv)">
                  <Button
                    onClick={handleEmployeeAction(String(employee.clickup_user_id), 'activate-time-report')}
                    variant="outline"
                  >
                    <CalendarIcon />
                  </Button>
                </SimpleTooltip>
              )}
            </>
          ) : (
            <Button onClick={handleEmployeeAction(String(employee.clickup_user_id), 'deactivate')} variant="outline">
              <EyeClosedIcon />
            </Button>
          )}
          <Button
            variant="outline"
            disabled={employee.inactive}
            onClick={handleRedirectUserPermission(employee.user_id)}
          >
            <LockClosedIcon />
            <OpenInNewWindowIcon />
          </Button>
          <Button variant="outline" disabled={employee.inactive} onClick={handleToggle}>
            <GearIcon />
          </Button>
        </TableCell>
      </TableRow>
      {isOpen && (
        <>
          <TableRow className="border-none">
            <TableCell>Arbeit Start</TableCell>
            <TableCell>
              <TimePicker date={minWorkStart} setDate={(date) => setMinWorkStart(date!)} />
            </TableCell>
            <TableCell></TableCell>
            <TableCell></TableCell>
            <TableCell className="flex justify-end">
              <Button onClick={handleSave}>Save</Button>
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell>Arbeit Ende</TableCell>
            <TableCell>
              <TimePicker date={maxWorkEnd} setDate={(date) => setMaxWorkEnd(date!)} />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Maximale Arbeitszeit (in Stunden)</TableCell>
            <TableCell>
              <Input
                name="max-work-hours"
                type="number"
                placeholder="Maximale Arbeitszeit in Stunden"
                value={maxWorkEndInHours}
                onChange={(event) => setMaxWorkEndInHours(event.target.value)}
              />
            </TableCell>
            <TableCell>Erlaubt Arbeitszeit während PTO?</TableCell>
            <TableCell>
              <Checkbox
                id="allow-work-during-pto"
                name="allow-work-during-pto"
                checked={allowWorkDuringPTO}
                onClick={() => setAllowWorkDuringPTO((prev) => !prev)}
              />
            </TableCell>
          </TableRow>
        </>
      )}
    </>
  );
}
