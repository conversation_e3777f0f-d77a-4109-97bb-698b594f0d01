'use client';

import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  activateEmployee,
  activateEmployeeTimeReport,
  deactivateEmployee,
  deactivateEmployeeTimeReport,
  getDomainEmployees,
  saveEmployeeStartEndTimes,
} from '@/server-actions/domain-employees';
import { SyncEmployees } from '@/app/employees/_components/sync-employees';
import useSWR from 'swr';
import { useToast } from '@/components/ui/use-toast';
import { EmployeeRow } from '@/app/employees/_components/employee-row';
import { Card, CardContent } from '@/components/ui/card';

export type EmployeeActionType = 'activate' | 'deactivate' | 'activate-time-report' | 'deactivate-time-report' | 'save';

export function EmployeesList() {
  const { isLoading, mutate, error, data: dataResponse } = useSWR('get-employees', () => getDomainEmployees(false));
  const { toast } = useToast();

  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (error || !dataResponse) {
    return <div>Error</div>;
  }

  const data = dataResponse.data;

  const handleEmployeeAction = (employeeId: string, action: EmployeeActionType, payload?: any) => async () => {
    let result;
    switch (action) {
      case 'activate':
        result = await activateEmployee(employeeId);
        break;
      case 'deactivate':
        result = await deactivateEmployee(employeeId);
        break;
      case 'activate-time-report':
        result = await activateEmployeeTimeReport(employeeId);
        break;
      case 'deactivate-time-report':
        result = await deactivateEmployeeTimeReport(employeeId);
        break;
      case 'save':
        result = await saveEmployeeStartEndTimes(employeeId, payload);
        break;
    }

    const { error } = result;
    if (!error) {
      await mutate();
      toast({
        title: 'Employee action',
        description: `Mitarbeiter aktualisiert`,
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Employee action',
        description: `Fehler beim Aktualisieren des Mitarbeiters.`,
      });
    }
  };

  return (
    <div className="flex gap-4 justify-center mt-4">
      <Card className="w-fit pt-4">
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead></TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>ClickUp ID</TableHead>
                <TableHead>Bexio ID</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map((employee) => {
                return (
                  <EmployeeRow key={employee.user_id} employee={employee} handleEmployeeAction={handleEmployeeAction} />
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="flex flex-col gap-2">
        <SyncEmployees />
      </div>
    </div>
  );
}
