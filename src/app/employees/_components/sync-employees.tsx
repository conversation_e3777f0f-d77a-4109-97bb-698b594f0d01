"use client";

import { But<PERSON> } from '@/components/ui/button';
import { syncEmployeesAction } from '@/server-actions/sync/employees';


export function SyncEmployees() {

  const handleSyncFolders = async () => {
    await syncEmployeesAction();
  };

  return (
    <div className='flex gap-4 justify-center'>
      <div className='flex flex-col gap-2'>
        <Button onClick={handleSyncFolders}>Sync Mitarbeiter</Button>
      </div>
    </div>
  );
}