import { CustomSidebar } from '@/components/custom-sidebar';
import { EmployeesList } from '@/app/employees/_components/employees-list';
import { getPermission } from '@/lib/employee';
import { ScrollArea } from '@/components/ui/scroll-area';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function Employees() {
  const permission = await getPermission('employees');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  return (
    <ScrollArea className="flex flex-col gap-4 max-h-[calc(100vh-120px)]">
      <EmployeesList />
    </ScrollArea>
  );
}

export default Employees;
