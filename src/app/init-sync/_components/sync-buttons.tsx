'use client';

import { Button } from '@/components/ui/button';
import {
  initImportClickUpLists,
  initImportClickUpTasks,
  initImportClickUpTimeEntries,
  initImportClients,
} from '@/server-actions/init-sync';
import { useState } from 'react';
import { useRunDetails } from '@trigger.dev/react';

export function SyncButtons() {
  const [jobId, setJobId] = useState<string>();
  const { isLoading, isError, data, error } = useRunDetails(jobId);

  const handleSyncClients = async () => {
    const { id } = await initImportClients();
    setJobId(id);
  };

  const handleSyncClickUpLists = async () => {
    const { id } = await initImportClickUpLists();
    setJobId(id);
  };

  const handleSyncClickUpTasks = async () => {
    const { id } = await initImportClickUpTasks();
    setJobId(id);
  };

  const handleSyncClickUpTimeEntries = async () => {
    const { id } = await initImportClickUpTimeEntries();
    setJobId(id);
  };

  return (
    <div className="flex gap-4 justify-center mt-12">
      <div className="flex flex-col gap-2">
        <Button onClick={handleSyncClients} disabled={isLoading}>
          Sync Bexio Contacts {'<->'} Clickup Folders
        </Button>

        <p>IMPORTANT: Clients need to be up to date before projects</p>
        <Button onClick={handleSyncClickUpLists} disabled={isLoading}>
          Sync ClickUp Lists {'<->'} Bexio Projects
        </Button>

        <p>IMPORTANT: Projects need to be up to date before tasks</p>
        <Button onClick={handleSyncClickUpTasks} disabled={isLoading}>
          Sync ClickUp Tasks {'<->'} Bexio Work Packages
        </Button>

        <p>IMPORTANT: Projects and Tasks need to be up to date before time entries</p>
        <Button onClick={handleSyncClickUpTimeEntries} disabled={isLoading}>
          Sync ClickUp Time Entries {'<->'} Bexio Timesheet
        </Button>

        {isLoading && <p>Loading...</p>}
        {isError && <p>Error: {error?.message}</p>}
        {(data?.status === 'EXECUTING' || data?.status.startsWith('WAITING')) && <p>Sync in progress...</p>}
        {data?.status === 'SUCCESS' && <p>Sync completed</p>}
        {data?.status === 'FAILURE' && <p>Sync failed</p>}
      </div>
    </div>
  );
}
