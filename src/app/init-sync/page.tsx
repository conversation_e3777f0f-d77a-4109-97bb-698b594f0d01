import { CustomSidebar } from '@/components/custom-sidebar';
import { SyncButtons } from '@/app/init-sync/_components/sync-buttons';
import { getPermission } from '@/lib/employee';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function InitSync() {
  const permission = await getPermission('init-sync');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  return (
    <main className="grid h-screen min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
      <CustomSidebar />
      <div>
        <SyncButtons />
      </div>
    </main>
  );
}

export default InitSync;
