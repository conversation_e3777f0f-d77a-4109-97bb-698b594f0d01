import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { headers } from 'next/headers';
import { AuthProvider } from '@/context/auth-context';
import { ReactNode } from 'react';
import { PermissionSelection } from '@/app/employees/[id]/_components/permissionsOptions';
import { Providers } from '@/components/layout/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'LUMEOS Internal',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const headersList = headers();
  const name = headersList.get('x-user-name');
  const email = headersList.get('x-user-email');
  const image = headersList.get('x-user-image');
  const access = JSON.parse(headersList.get('x-access') || '[]') as PermissionSelection[];

  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider name={name} email={email} image={image} access={access}>
          <Providers>{children}</Providers>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  );
}
