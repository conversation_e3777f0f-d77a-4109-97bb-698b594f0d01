'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useState } from 'react';
import { useRunDetails } from '@trigger.dev/react';
import { nukeClients, nukeProjects, nukeTasks, nukeTimeEntries } from '@/server-actions/nuke';

export function NukeButtons() {
  const [jobId, setJobId] = useState<string>();
  const { isLoading, isError, data, error } = useRunDetails(jobId);

  const handleNukeClients = async () => {
    const { id } = await nukeClients();
    setJobId(id);
  };

  const handleNukeProjects = async () => {
    const { id } = await nukeProjects();
    setJobId(id);
  };

  const handleNukeTasks = async () => {
    const { id } = await nukeTasks();
    setJobId(id);
  };

  const handleNukeTimeEntries = async () => {
    const { id } = await nukeTimeEntries();
    setJobId(id);
  };

  return (
    <div className="flex gap-4 justify-center mt-12">
      <div className="flex flex-col gap-2">
        <Button variant="destructive" onClick={handleNukeTimeEntries} disabled={isLoading}>
          Nuke Time Entries
        </Button>

        <p>IMPORTANT: Nuke Time Entries before Tasks to not mix up dependencies</p>
        <Button variant="destructive" onClick={handleNukeTasks} disabled={isLoading}>
          Nuke Tasks
        </Button>

        <p>IMPORTANT: Nuke Tasks before projects to not mix up dependencies</p>
        <Button variant="destructive" onClick={handleNukeProjects} disabled={isLoading}>
          Nuke Projects
        </Button>

        <p>IMPORTANT: Nuke Projects before Clients to not mix up dependencies</p>
        <Button variant="destructive" onClick={handleNukeClients} disabled={isLoading}>
          Nuke Clients
        </Button>

        {isLoading && <p>Loading...</p>}
        {isError && <p>Error: {error?.message}</p>}
        {(data?.status === 'EXECUTING' || data?.status.startsWith('WAITING')) && <p>Nuke in progress...</p>}
        {data?.status === 'SUCCESS' && <p>Nuke completed</p>}
        {data?.status === 'FAILURE' && <p>Nuke failed</p>}
      </div>
    </div>
  );
}
