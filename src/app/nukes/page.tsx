import { CustomSidebar } from '@/components/custom-sidebar';
import { NukeButtons } from '@/app/nukes/_components/nuke-buttons';
import { getPermission } from '@/lib/employee';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function NukesPage() {
  const permission = await getPermission('nukes');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  return (
    <main className="grid h-screen min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
      <CustomSidebar />
      <div>
        <NukeButtons />
      </div>
    </main>
  );
}

export default NukesPage;
