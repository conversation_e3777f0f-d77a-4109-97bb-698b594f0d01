import { CustomSidebar } from '@/components/custom-sidebar';
import { Suspense } from 'react';
import ClientCountWidget from '@/app/_components/client-count-widget';
import ProjectCountWidget from '@/app/_components/project-count-widget';
import TaskCountWidget from '@/app/_components/tasks-count-widget';
import TimeCountWidget from '@/app/_components/time-count-widget';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { WidgetLoader } from '@/app/_components/widget-loader';
import ForecastWidget from '@/app/_components/forecast-widget';
import { getPermission } from '@/lib/employee';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

export default async function Home() {
  const permission = await getPermission('home');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  const isDev = process.env.NODE_ENV === 'development';

  return (
    <main className="grid h-screen min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
      <CustomSidebar />
      <div className="flex flex-col w-full">
        <div className="flex gap-4 justify-center mt-12">
          <Suspense fallback={<WidgetLoader />}>
            <ClientCountWidget />
          </Suspense>
          <Suspense fallback={<WidgetLoader />}>
            <ProjectCountWidget />
          </Suspense>
          <Suspense fallback={<WidgetLoader />}>
            <TaskCountWidget />
          </Suspense>
          <Suspense fallback={<WidgetLoader />}>
            <TimeCountWidget />
          </Suspense>
        </div>
        <div className="flex gap-4 justify-center mt-12">
          <Suspense fallback={<WidgetLoader size="large" />}>
            <ForecastWidget />
          </Suspense>
        </div>
        {isDev && (
          <div className="flex justify-center gap-2 mt-12">
            <Link href="/cron">
              <Button>CRON</Button>
            </Link>
            <Link href="/init-sync">
              <Button>Initial Sync</Button>
            </Link>
            <Link href="/nukes">
              <Button variant="destructive">Nukes</Button>
            </Link>
          </div>
        )}
      </div>
    </main>
  );
}
