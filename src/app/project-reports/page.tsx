import { CustomSidebar } from '@/components/custom-sidebar';
import { getProjectReport, ProjectReportReturn } from '@/server-actions/projekt-reporting/simple-report';
import { ProjectReportingList } from '@/app/projects/components/project-reporting-list';
import { getPermission } from '@/lib/employee';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function ProjectsReports() {
  const permission = await getPermission('project-reports');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  let projectData: ProjectReportReturn;
  if (permission === 'limited') {
    const headersList = headers();
    const name = headersList.get('x-user-name');
    if (!name) return <div>You are not allowed to access this page</div>;
    projectData = await getProjectReport(name);
  } else {
    projectData = await getProjectReport();
  }

  if (!projectData) {
    return <div className="flex justify-center">Loading...</div>;
  }

  if ('error' in projectData) {
    return <div className="flex justify-center">There was an error fetching the time report</div>;
  }

  return <ProjectReportingList projectData={projectData} excludeDefaultFilters={false} excludeFileUpload={true} />;
}

export default ProjectsReports;
