'use client';

import { Tables } from '@/types/gen/database-table';
import { AdvancedDataTable } from '@/app/projects/components/table/advanced-data-table';
import { columns } from '@/app/projects/components/table/columns';

type ProjectReportData = Tables<'bexio_projects'>;

type ProjectReportingListProps = {
  projectData: ProjectReportData[];
  filtersData?: Tables<'custom_filters'>[];
  visibilityFilters?: Tables<'custom_filters'>[];
  excludeDefaultFilters?: boolean;
  excludeFileUpload?: boolean;
};

export function ProjectReportingList({
  projectData,
  filtersData,
  visibilityFilters,
  excludeDefaultFilters,
  excludeFileUpload,
}: ProjectReportingListProps) {
  return (
    <AdvancedDataTable
      columns={columns}
      data={projectData}
      customFilters={filtersData}
      visibilityFilters={visibilityFilters}
      excludeDefaultFilters={excludeDefaultFilters}
      excludeFileUpload={excludeFileUpload}
    />
  );
}
