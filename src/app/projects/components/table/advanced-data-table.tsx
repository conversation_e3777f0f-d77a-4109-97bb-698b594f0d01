'use client';

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';

import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useState } from 'react';
import { customGlobalFilter, SpecialFilters } from '@/app/projects/components/table/special-filters';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { UploadCsvReport } from '@/app/projects/components/upload-csv-report';
import { cn } from '@/lib/utils';
import CustomFilters from '@/app/projects/components/table/custom-filters';
import { Tables } from '@/types/gen/database.types';
import { getMinMaxValues } from '@/components/table/minmax';
import { IconColumns, IconDownload } from '@tabler/icons-react';
import { Button } from '@/components/ui/button';
import { useDownloadProjectReport } from '@/app/projects/components/table/use-download-project-report';
import { Multiselect } from '@/components/ui/multiselect';
import { useColumnVisibility } from '@/components/table/use-column-visibility';
import { VisibilityFilters } from '@/app/projects/components/table/visibility-filters';
import { ProjectReportingColumnType } from '@/app/projects/components/table/columns';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  customFilters?: Tables<'custom_filters'>[];
  visibilityFilters?: Tables<'custom_filters'>[];
  excludeDefaultFilters?: boolean;
  excludeFileUpload?: boolean;
}

export function AdvancedDataTable<TData, TValue>({
  columns,
  data,
  customFilters,
  visibilityFilters,
  excludeDefaultFilters,
  excludeFileUpload,
}: DataTableProps<ProjectReportingColumnType, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    enableGlobalFilter: true,
    globalFilterFn: customGlobalFilter,
    state: {
      sorting,
      columnFilters,
      getMinMaxValues,
    },
  });

  const { columnOptions, defaultVisibleColumns, selectedColumns, handleColumnVisibilityChange } =
    useColumnVisibility(table);
  const { downloadExcel } = useDownloadProjectReport(table);

  return (
    <div className="flex flex-col gap-2 items-center max-w-full">
      <div className="flex gap-4 items-center justify-between w-full">
        {!excludeDefaultFilters && <SpecialFilters table={table} />}
        <div className="flex gap-2 items-center">
          {customFilters && <CustomFilters table={table} customFilters={customFilters} />}
          {visibilityFilters && (
            <VisibilityFilters
              table={table}
              visibilityFilters={visibilityFilters}
              defaultVisibleColumns={defaultVisibleColumns}
              handleColumnVisibilityChange={handleColumnVisibilityChange}
            />
          )}
        </div>

        <div className="flex gap-2 items-baseline">
          <Multiselect
            label="Spalten"
            options={columnOptions}
            values={selectedColumns}
            onChange={handleColumnVisibilityChange}
            icon={<IconColumns className="h-4 w-4" />}
          />

          <div className="flex flex-col gap-2">
            {!excludeFileUpload && <UploadCsvReport />}
            <Button variant="outline" onClick={downloadExcel}>
              Export <IconDownload className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <ScrollArea className="flex flex-col gap-4 max-w-full max-h-[calc(100vh-230px)] rounded-md border pr-[8px] pb-[8px]">
        <Table>
          <TableHeader className="sticky top-0 bg-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        'whitespace-nowrap max-w-[300px]',
                        (header.id == 'client_name' || header.id == 'name') && 'min-w-[300px]',
                      )}
                    >
                      <div className="flex">
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cn(
                        'whitespace-nowrap',
                        (cell.column.id == 'client_name' || cell.column.id == 'name') &&
                          'w-[300px] whitespace-break-spaces',
                      )}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-12 text-center">
                  Keine Daten vorhanden.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            {table.getFooterGroups().map((footerGroup) => (
              <TableRow key={footerGroup.id}>
                {footerGroup.headers.map((header) => (
                  <TableCell key={header.id} className="whitespace-nowrap">
                    {flexRender(header.column.columnDef.footer, header.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableFooter>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
