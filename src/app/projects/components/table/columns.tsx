'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Tables } from '@/types/gen/database.types';
import { SliderFilter } from '@/components/table/slider-filter';
import { prettyPrintCurrency, prettyPrintPercentage } from '@/lib/utils';
import { ColumnSortButton } from '@/components/table/column-sort-button';
import { Multiselect } from '@/components/table/multiselect';
import { TextFilter } from '@/components/table/text-filter';
import { MinMaxTableState } from '@/components/table/minmax';
import { RowData } from '@tanstack/table-core';
import { formatDate } from 'date-fns';
import {
  calculateAverage,
  calculateSum,
  dateSort,
  multiSelectAndTextFilter,
  multiSelectFilter,
  numericalSort,
  rangeFilter,
  textFilter,
} from '@/components/table/filter-utils';

declare module '@tanstack/react-table' {
  interface TableState extends MinMaxTableState {}

  interface ColumnMeta<TData extends RowData, TValue> {
    readableName?: string;
  }
}

export type ProjectReportingColumnType = Tables<'bexio_projects'>;

export const columns: ColumnDef<ProjectReportingColumnType>[] = [
  {
    accessorKey: 'client_name',
    meta: {
      readableName: 'Kunde',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Kunde
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
    footer: 'SUMME',
  },
  {
    accessorKey: 'nr',
    meta: {
      readableName: 'Projektnummer',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Projektnummer
          <ColumnSortButton column={column} />
          <TextFilter column={column} />
        </div>
      );
    },
    filterFn: textFilter,
  },
  {
    accessorKey: 'name',
    meta: {
      readableName: 'Projekt',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Projekt
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
          <TextFilter column={column} />
        </div>
      );
    },
    filterFn: multiSelectAndTextFilter,
  },
  {
    accessorKey: 'status',
    meta: {
      readableName: 'Status',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Status
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
    enableGlobalFilter: true,
  },
  {
    accessorKey: 'substatus',
    meta: {
      readableName: 'Substatus',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Substatus
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
  },
  {
    accessorKey: 'contact_person',
    meta: {
      readableName: 'Ansprechpartner',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Ansprechpartner
          <ColumnSortButton column={column} />
          <Multiselect column={column} />
        </div>
      );
    },
    filterFn: multiSelectFilter,
  },
  {
    accessorKey: 'progress_percentage',
    meta: {
      readableName: 'Fortschritt %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Fortschritt %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintPercentage(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'progress_percentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'time_estimate',
    meta: {
      readableName: 'Zeitschätzung tot.',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Zeitschätzung tot.
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="h" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{Number(row.renderValue() ?? 0).toFixed(2)} h</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return calculateSum(rows, 'time_estimate').toFixed(2) + ' h';
    },
    filterFn: rangeFilter,
  },
  {
    accessorKey: 'time_estimate_open',
    meta: {
      readableName: 'Zeitschätzung offen',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Zeitschätzung offen
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="h" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{Number(row.renderValue() ?? 0).toFixed(2)} h</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return calculateSum(rows, 'time_estimate_open').toFixed(2) + ' h';
    },
    filterFn: rangeFilter,
  },
  {
    accessorKey: 'time_spent',
    meta: {
      readableName: 'Zeit geleistet',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Zeit geleistet
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="h" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{Number(row.renderValue() ?? 0).toFixed(2)} h</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return calculateSum(rows, 'time_spent').toFixed(2) + ' h';
    },
    filterFn: rangeFilter,
  },
  {
    accessorKey: 'difference_time',
    meta: {
      readableName: '∆ Zeit tot.',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Zeit tot.
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="h" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{Number(row.renderValue() ?? 0).toFixed(2)} h</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return calculateSum(rows, 'difference_time').toFixed(2) + ' h';
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'difference_time_percentage',
    meta: {
      readableName: '∆ Zeit tot. %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Zeit tot. %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintPercentage(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'difference_time_percentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'budget',
    meta: {
      readableName: 'Budget',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Budget
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'budget'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'estimated_cost',
    meta: {
      readableName: 'Erwartete Kosten',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Erwartete Kosten
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'estimated_cost'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'effective_cost',
    meta: {
      readableName: 'Effektive Kosten',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Effektive Kosten
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'effective_cost'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'factorized_amount',
    meta: {
      readableName: 'Fakturiert',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Fakturiert
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'factorized_amount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'services_amount',
    meta: {
      readableName: 'Dienstleistungserlös',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Dienstleistungserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'services_amount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'trades_amount',
    meta: {
      readableName: 'Handelserlös',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Handelserlös
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'trades_amount'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'difference_estimated_cost',
    meta: {
      readableName: '∆ Budget/erw. Kosten',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Budget/erw. Kosten
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'difference_estimated_cost'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'difference_estimated_cost_percentage',
    meta: {
      readableName: '∆ Budget/erw. Kosten %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Budget/erw. Kosten %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      const value = isNaN(Number(row.renderValue())) ? 0 : Number(row.renderValue());
      const isNotValid = isNaN(Number(row.renderValue())) || !isFinite(Number(row.renderValue()));
      return <div>{isNotValid ? '-' : `${value.toFixed(2)} %`}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'difference_estimated_cost_percentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'difference_budget',
    meta: {
      readableName: '∆ Budget/eff. Kosten',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Budget/eff. Kosten
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'difference_budget'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'difference_budget_percentage',
    meta: {
      readableName: '∆ Budget/eff. Kosten %',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          ∆ Budget/eff. Kosten %
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} type="%" />
        </div>
      );
    },
    cell: (row) => {
      const value = isNaN(Number(row.renderValue())) ? 0 : Number(row.renderValue());
      const isNotValid = isNaN(Number(row.renderValue())) || !isFinite(Number(row.renderValue()));
      return <div>{isNotValid ? '-' : `${value.toFixed(2)} %`}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintPercentage(calculateAverage(rows, 'difference_budget_percentage'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
  {
    accessorKey: 'start_date',
    meta: {
      readableName: 'Start Datum',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Start Datum
          <ColumnSortButton column={column} />
        </div>
      );
    },
    cell: (row) => {
      const value = row.renderValue();
      if (!value) {
        return <div>-</div>;
      }
      return <div>{formatDate(new Date(String(value)), 'dd.MM.yyyy')}</div>;
    },
    sortingFn: dateSort,
  },
  {
    accessorKey: 'end_date',
    meta: {
      readableName: 'End Datum',
    },
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          End Datum
          <ColumnSortButton column={column} />
        </div>
      );
    },
    cell: (row) => {
      const value = row.renderValue();
      if (!value) {
        return <div>-</div>;
      }
      return <div>{formatDate(new Date(String(value)), 'dd.MM.yyyy')}</div>;
    },
    sortingFn: dateSort,
  },
  {
    accessorKey: 'close_date',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Abschluss Datum
          <ColumnSortButton column={column} />
        </div>
      );
    },
    cell: (row) => {
      const value = row.renderValue();
      if (!value) {
        return <div>-</div>;
      }
      return <div>{formatDate(new Date(String(value)), 'dd.MM.yyyy')}</div>;
    },
    sortingFn: 'text',
  },
  {
    accessorKey: 'bexio_effective_cost',
    meta: {
      readableName: 'Effektive Kosten (Bexio)',
    },
    header: ({ column, table }) => {
      return (
        <div className="flex items-center">
          Effektive Kosten (Bexio)
          <ColumnSortButton column={column} />
          <SliderFilter column={column} table={table} />
        </div>
      );
    },
    cell: (row) => {
      return <div>{prettyPrintCurrency(row.renderValue())}</div>;
    },
    footer: ({ table }) => {
      const rows = table.getRowModel().rows;
      return prettyPrintCurrency(calculateSum(rows, 'bexio_effective_cost'));
    },
    filterFn: rangeFilter,
    sortingFn: numericalSort,
  },
];
