import { ProjectReportingColumnType } from '@/app/projects/components/table/columns';
import { Table } from '@tanstack/table-core';
import { Tables } from '@/types/gen/database.types';
import { useCallback, useEffect, useState } from 'react';
import { Combobox } from '@/components/ui/combobox';
import { Button } from '@/components/ui/button';
import { SaveIcon, TrashIcon } from 'lucide-react';
import { addNewFilterByType, deleteFilter } from '@/server-actions/projekt-reporting/filters';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';

type CustomFiltersProps = {
  table: Table<ProjectReportingColumnType>;
  customFilters: Tables<'custom_filters'>[];
};

export default function CustomFilters({ table, customFilters }: CustomFiltersProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [activeFilterId, setActiveFilterId] = useState<string | undefined>();
  const [newFilterName, setNewFilterName] = useState<string>('');

  useEffect(() => {
    if (table.getState().globalFilter && table.getState().globalFilter !== '') {
      setActiveFilterId(undefined);
    }
  }, [table.getState().globalFilter]);

  const handleFilterChange = useCallback(
    (newFilterId: string) => {
      if (activeFilterId == newFilterId) {
        table.setColumnFilters([]);
        setActiveFilterId(undefined);
        return;
      }

      const newFilter = customFilters.find((filter) => String(filter.id) === newFilterId);
      if (!newFilter || !newFilter.filter) return;
      setActiveFilterId(newFilterId);
      // @ts-ignore
      table.setColumnFilters(newFilter.filter);
      table.setGlobalFilter('');
    },
    [table, activeFilterId, customFilters],
  );

  const handleDelete = useCallback(async () => {
    handleFilterChange(String(activeFilterId));
    const { error } = await deleteFilter(Number(activeFilterId));

    if (!error) {
      toast({
        title: 'Filter gelöscht',
      });
      router.refresh();
      return;
    }

    console.error(error);
    toast({
      variant: 'destructive',
      title: 'Fehler beim Löschen des Filters',
    });
  }, [activeFilterId]);

  const handleSaveNewFilter = useCallback(async () => {
    const { data, error } = await addNewFilterByType(newFilterName, table.getState().columnFilters, 'custom');

    if (!error) {
      toast({
        title: 'Filter gespeichert',
      });

      router.refresh();
      setTimeout(() => {
        setActiveFilterId(String(data.id));
        setNewFilterName('');
      }, 500);
      return;
    }

    toast({
      variant: 'destructive',
      title: 'Fehler beim Speichern des Filters',
    });
  }, [newFilterName, table]);

  const options = customFilters.map((filter) => ({ value: String(filter.id), label: filter.name }));

  return (
    <div className="flex flex-wrap gap-4 py-4">
      <div>
        <p className="text-sm mb-1 ml-1">Custom Filter:</p>
        <div className="flex">
          <Combobox options={options} onChange={handleFilterChange} selectedValue={activeFilterId} />
          <Button variant="ghost" className="py-1 px-2 ml-2" disabled={!activeFilterId} onClick={handleDelete}>
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex mt-2">
          <Input
            name="new-filter"
            placeholder="Neuer Filter"
            value={newFilterName}
            onChange={(event) => setNewFilterName(event.target.value)}
          />
          <Button variant="ghost" className="py-1 px-2 ml-2" onClick={handleSaveNewFilter}>
            <SaveIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
