import { FilterMeta, Row, Table } from '@tanstack/table-core';
import { ProjectReportingColumnType } from '@/app/projects/components/table/columns';
import { useCallback, useEffect } from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

type SpecialFiltersProps<TData> = {
  table: Table<TData>;
};

const options = [
  { value: 'bad-prognosis', label: 'Schlechte Prognose' },
  { value: 'projects-no-conditions', label: 'Keine Konditionen' },
  { value: 'projects-wo-start-end', label: 'Kein Start-/Enddatum' },
  { value: 'projects-overdue', label: 'Überfällig' },
  { value: 'projects-overdrawn', label: 'Überzogene Projekte' },
];

export function SpecialFilters({ table }: SpecialFiltersProps<ProjectReportingColumnType>) {
  useEffect(() => {
    handleFilterChange('bad-prognosis');
  }, []);

  const handleFilterChange = useCallback(
    (newFilter: string) => {
      if (newFilter == table.getState().globalFilter) {
        table.setGlobalFilter('');
        table.setColumnFilters([]);
      } else {
        table.setGlobalFilter(newFilter);
        table.setColumnFilters([]);
      }
    },
    [table],
  );

  return (
    <div className="flex flex-wrap gap-4 py-4">
      <Tabs onValueChange={handleFilterChange} value={table.getState().globalFilter}>
        <TabsList>
          {options.map((option) => (
            <TabsTrigger key={option.value} value={option.value}>
              {option.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}

export function customGlobalFilter(
  row: Row<ProjectReportingColumnType>,
  columnId: string,
  filterValue: any,
  addMeta: (meta: FilterMeta) => void,
) {
  const substatus = String(row.getValue('substatus'));
  const status = String(row.getValue('status'));
  const openOrActive = status.includes('Offen') || status.includes('Aktiv');
  const budgetNotNull = Number(row.getValue('budget')) > 0;

  switch (filterValue) {
    case 'bad-prognosis':
      // Alle Listen die folgenden Kriterien entsprechen
      // Projektstatus = Offen oder Aktiv
      // Budget ist nicht gleich leer oder 0
      // ∆ Budget/erw. Kosten <=0
      const diffBudgetEstimatedCost = Number(row.getValue('difference_estimated_cost')) <= 0;
      return openOrActive && budgetNotNull && diffBudgetEstimatedCost;
    case 'projects-no-conditions':
      // Alle Listen die folgenden Kriterien entsprechen
      // Budget ist gleich leer
      // Projektstatus = Aktiv oder Offen
      // Projektsubstatus ist nicht gleich Akquirierung, Projekt verloren, Ablage, Offertphase
      const budgetEmpty = Number(row.getValue('budget')) === 0 || isNaN(Number(row.getValue('budget')));
      const correctSubstatusCond =
        !substatus.includes('Akquirierung') &&
        !substatus.includes('Projekt verloren') &&
        !substatus.includes('Ablage') &&
        !substatus.includes('Offertphase');
      return openOrActive && budgetEmpty && correctSubstatusCond;
    case 'projects-wo-start-end':
      // Alle Listen die folgenden Kriterien entsprechen
      // Projektsubstatus ist nicht gleich Akquirierung, Projekt verloren, Ablage, Offertphase
      // ohne Start und/oder Endzeit
      const correctSubstatusCond2 =
        !substatus.includes('Akquirierung') &&
        !substatus.includes('Projekt verloren') &&
        !substatus.includes('Ablage') &&
        !substatus.includes('Offertphase');
      const hasStartDate = row.getValue('start_date') !== null;
      const hasEndDate = row.getValue('end_date') !== null;
      return correctSubstatusCond2 && (!hasStartDate || !hasEndDate);
    case 'projects-overdrawn':
      // Alle Listen die folgenden Kriterien entsprechen
      // Projektstatus = Offen oder Aktiv
      // Budget ist nicht gleich leer oder 0
      // ∆ Budget/eff. Kosten <=0
      const diffEffectiveCost = Number(row.getValue('difference_budget_percentage')) <= 0;
      return openOrActive && budgetNotNull && diffEffectiveCost;
    case 'projects-overdue':
      // Alle Listen die folgenden Kriterien entsprechen
      // Projektstatus = Offen oder Aktiv
      // Projektendatum liegt in der Vergangenheit
      const endDateNotNull = row.getValue('end_date') !== null;
      const endDateInPast = new Date(row.getValue('end_date')) < new Date();
      return openOrActive && endDateNotNull && endDateInPast;
  }
  return true;
}
