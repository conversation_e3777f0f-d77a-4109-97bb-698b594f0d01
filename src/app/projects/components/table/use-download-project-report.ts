import * as XLSX from 'xlsx';
import { flexRender, Table } from '@tanstack/react-table';
import { ProjectReportingColumnType } from '@/app/projects/components/table/columns';
import React from 'react';
import ReactDOMServer from 'react-dom/server';

export function useDownloadProjectReport(table: Table<ProjectReportingColumnType>) {
  const downloadExcel = () => {
    // Get visible columns
    const visibleColumns = table.getVisibleFlatColumns();

    // Prepare headers
    let headers = visibleColumns.map((column) => {
      return String(column.columnDef.meta?.readableName);
    });

    // Append CHF/ h to headers if needed
    const chfColumns = [
      'Budget',
      'Erwartete Kosten',
      'Effektive Kosten',
      'Fakturiert',
      'Dienstleistungserlös',
      'Handelserlös',
      '∆ Budget/erw. Kosten',
      '∆ Budget/eff. Kosten',
    ];
    const hrsColumns = ['Zeitschätzung tot.', 'Zeitschätzung offen', 'Zeit geleistet', '∆ Zeit tot.'];
    headers = headers.map((header) => {
      if (chfColumns.includes(header)) {
        return `${header} (CHF)`;
      }

      if (hrsColumns.includes(header)) {
        return `${header} (h)`;
      }
      return header;
    });

    // Prepare data
    const excelData = table.getRowModel().rows.map((row) => {
      const rowData: Record<string, any> = {};
      visibleColumns.forEach((column, index) => {
        const cell = row.getAllCells().find((cell) => cell.column.id === column.id);
        if (!cell) {
          rowData[headers[index]] = '';
          return;
        }

        const cellContent = flexRender(column.columnDef.cell, cell.getContext());

        // Extract text content from the rendered cell
        let cellValue: string;
        if (typeof cellContent === 'string') {
          cellValue = cellContent;
        } else if (React.isValidElement(cellContent)) {
          // If it's a React element, we render it to a string
          cellValue = ReactDOMServer.renderToString(cellContent);
          // Remove HTML tags to get plain text
          cellValue = cellValue.replace(/<[^>]*>/g, '');
        } else {
          // Fallback to JSON stringification for other types
          cellValue = JSON.stringify(cellContent);
        }

        // Strip out everything that is not a number
        if (cellValue.startsWith('CHF')) cellValue = cellValue.replace('CHF ', '').replace('CHF ', '').replace('’', '');
        if (cellValue.endsWith('h')) cellValue = cellValue.replace(' h', '');
        if (cellValue.endsWith('%')) cellValue = cellValue.replace(' %', '').replace('%', '');

        if (!isNaN(Number(cellValue))) {
          rowData[headers[index]] = Number(cellValue);
        } else {
          rowData[headers[index]] = cellValue;
        }
      });
      return rowData;
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData, { header: headers });

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Project Report');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Trigger download
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'project_report.xlsx';
    link.click();
    URL.revokeObjectURL(url);
  };

  return {
    downloadExcel,
  };
}
