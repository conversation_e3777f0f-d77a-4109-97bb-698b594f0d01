import { Table } from '@tanstack/table-core';
import { ProjectReportingColumnType } from '@/app/projects/components/table/columns';
import { Tables } from '@/types/gen/database-table';
import { Combobox } from '@/components/ui/combobox';
import { Button } from '@/components/ui/button';
import { SaveIcon, TrashIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useCallback, useState } from 'react';
import { addNewFilterByType, deleteFilter } from '@/server-actions/projekt-reporting/filters';
import { toast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { VisibilityState } from '@tanstack/react-table';

type VisibilityFilterProps = {
  table: Table<ProjectReportingColumnType>;
  visibilityFilters: Tables<'custom_filters'>[];
  defaultVisibleColumns: string[];
  handleColumnVisibilityChange: (selectedOptions: string[]) => void;
};

export function VisibilityFilters({
  table,
  visibilityFilters,
  defaultVisibleColumns,
  handleColumnVisibilityChange,
}: VisibilityFilterProps) {
  const [activeFilterId, setActiveFilterId] = useState<string>();
  const [newFilterName, setNewFilterName] = useState<string>();
  const router = useRouter();

  const handleFilterChange = useCallback(
    (newFilterId: string) => {
      if (activeFilterId == newFilterId) {
        const newVisibility: VisibilityState = {};
        defaultVisibleColumns.forEach((column) => {
          newVisibility[column] = true;
        });
        table.setColumnVisibility(newVisibility);
        setActiveFilterId(undefined);
        return;
      }

      const newFilter = visibilityFilters.find((filter) => String(filter.id) === newFilterId);
      if (!newFilter || !newFilter.filter) return;
      setActiveFilterId(newFilterId);

      // @ts-ignore
      const newActiveColumns = Object.entries(newFilter.filter[0])
        .filter(([_, value]) => value)
        .map(([key]) => key);
      handleColumnVisibilityChange(newActiveColumns);
    },
    [table, activeFilterId, visibilityFilters],
  );

  const handleDelete = useCallback(async () => {
    handleFilterChange(String(activeFilterId));
    const { error } = await deleteFilter(Number(activeFilterId));

    if (!error) {
      toast({
        title: 'Filter gelöscht',
      });
      router.refresh();
      return;
    }

    console.error(error);
    toast({
      variant: 'destructive',
      title: 'Fehler beim Löschen des Filters',
    });
  }, [activeFilterId]);

  const handleSaveNewFilter = useCallback(async () => {
    if (!newFilterName) return;
    const currentVisibility = table.getState().columnVisibility;
    if (Object.keys(currentVisibility).length === 0) {
      defaultVisibleColumns.forEach((column) => {
        currentVisibility[column] = true;
      });
    }

    const { data, error } = await addNewFilterByType(newFilterName, [table.getState().columnVisibility], 'visibility');

    if (!error) {
      router.refresh();
      toast({
        title: 'Filter gespeichert',
      });

      router.refresh();
      setTimeout(() => {
        setActiveFilterId(String(data.id));
        setNewFilterName('');
      }, 500);
      return;
    }

    console.error(error);
    toast({
      variant: 'destructive',
      title: 'Fehler beim Speichern des Filters',
    });
  }, [newFilterName, table]);

  const options = Array.from(
    visibilityFilters.map((option) => ({ value: String(option.id), label: option.name })) || [],
  );

  return (
    <div className="flex">
      <div className="flex flex-col">
        <p className="text-sm mb-1 ml-1">Visibility Filter:</p>
        <div className="flex max-w-[300px]">
          <Combobox options={options} onChange={handleFilterChange} selectedValue={activeFilterId} />
          <Button variant="ghost" className="py-1 px-2 ml-2" disabled={!activeFilterId} onClick={handleDelete}>
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex mt-2">
          <Input
            name="new-filter"
            placeholder="Neuer Filter"
            value={newFilterName}
            onChange={(event) => setNewFilterName(event.target.value)}
          />
          <Button variant="ghost" className="py-1 px-2 ml-2" onClick={handleSaveNewFilter}>
            <SaveIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
