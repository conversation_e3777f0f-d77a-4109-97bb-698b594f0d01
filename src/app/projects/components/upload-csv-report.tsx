'use client';

import { Input } from '@/components/ui/input';
import <PERSON> from 'papaparse';
import { ChangeEvent, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { submitCSVData } from '@/server-actions/projekt-reporting/simple-report';
import { BexioCSVExportRow } from '@/data/types/bexio-csv-export.types';
import { useToast } from '@/components/ui/use-toast';
import * as XLSX from 'xlsx';

export function UploadCsvReport() {
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<string>('');
  const { toast } = useToast();

  useEffect(() => {
    parseAndSubmit();
  }, [csvData]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
    }
  };

  const parseXLSXFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (event: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(event.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const csv = XLSX.utils.sheet_to_csv(worksheet);
      setCsvData(csv);
    };
    reader.readAsArrayBuffer(file);
  };

  const handleInitFileParse = () => {
    if (file) {
      const fileName = file.name;
      const fileExtension = fileName.split('.').pop()?.toLowerCase();

      if (fileExtension !== 'xlsx' && fileExtension !== 'csv') {
        toast({
          variant: 'destructive',
          title: 'Please upload a valid .xlsx or .csv file.',
        });
        return;
      }

      if (fileExtension === 'xlsx') {
        parseXLSXFile(file);
        return;
      }

      parseAndSubmit();
    }
  };

  const parseAndSubmit = () => {
    if (file === null) {
      return;
    }

    const csv = csvData || file;
    Papa.parse(csv, {
      header: true,
      complete: async (result) => {
        await handleSubmit(result.data as BexioCSVExportRow[]);
        toast({
          title: 'File uploaded successfully.',
        });
        // wait 3 seconds before reloading the page
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      },
      error: (error) => {
        console.error(error);
        toast({
          variant: 'destructive',
          title: 'Error parsing file.',
        });
      },
    });
  };

  const handleSubmit = async (data: BexioCSVExportRow[]) => {
    const mapped = await submitCSVData(data);
    console.log(mapped);
  };

  return (
    <div>
      <div className="flex gap-2">
        <Input className="w-[98px] pl-[4px] pt-[6px]" type="file" onChange={handleChange} />
        <Button onClick={handleInitFileParse}>UP</Button>
      </div>
    </div>
  );
}
