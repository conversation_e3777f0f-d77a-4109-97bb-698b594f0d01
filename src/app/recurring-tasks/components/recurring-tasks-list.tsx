'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { IconEdit, IconCheck, IconX, IconSettings } from '@tabler/icons-react';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { updateRecurringTask, UpdateRecurringTaskData } from '@/server-actions/recurring-tasks/update-recurring-task';
import {
  bulkUpdateRecurringTasks,
  BulkUpdateRecurringTasksData,
} from '@/server-actions/recurring-tasks/bulk-update-recurring-tasks';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IgnoredListsModal } from './ignored-lists-modal';

interface RecurringTask {
  clickup_task_id: string;
  schedule: string | null;
  schedule_options: any;
  skip_weekends: boolean;
  recur_end: string | null;
  copy_task: boolean;
  tasks: {
    name: string;
    clickup_task_id: string | null;
    clickup_assignees: number[];
    assignees: {
      clickup_user_id: string | null;
      name: string;
      profile_picture_url: string | null;
    }[];
  } | null;
}

interface RecurringTasksListProps {
  tasks: RecurringTask[];
}

const scheduleOptions = [
  { value: 'daily', label: 'Täglich' },
  { value: 'weekly', label: 'Wöchentlich' },
  { value: 'monthly', label: 'Monatlich' },
  { value: 'yearly', label: 'Jährlich' },
  { value: 'custom', label: 'Custom' },
];

export function RecurringTasksList({ tasks }: RecurringTasksListProps) {
  const { toast } = useToast();
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false);
  const [bulkSettings, setBulkSettings] = useState({
    schedule: '',
    skipWeekends: false,
    copyTask: false,
    recurType: 'forever' as 'forever' | 'until',
    recurEnd: '',
    customSchedule: {
      interval: 1,
      unit: 'days' as 'days' | 'weeks' | 'months',
      weekdays: [] as string[],
      monthType: 'date' as 'date' | 'week',
      monthDate: 1 as number | 'last',
      monthWeek: 'first' as 'first' | 'second' | 'third' | 'fourth' | 'last',
      monthWeekday: 'monday',
    },
  });
  const [isBulkLoading, setIsBulkLoading] = useState(false);

  const [taskStates, setTaskStates] = useState<
    Record<
      string,
      {
        schedule: string;
        skipWeekends: boolean;
        copyTask: boolean;
        recurType: 'forever' | 'until';
        recurEnd: string;
        isLoading: boolean;
        isEditing: boolean;
        customSchedule: {
          interval: number;
          unit: 'days' | 'weeks' | 'months';
          weekdays: string[]; // ['monday', 'tuesday', etc.]
          monthType: 'date' | 'week'; // for months only
          monthDate: number | 'last'; // 1-28 or 'last'
          monthWeek: 'first' | 'second' | 'third' | 'fourth' | 'last'; // for week-based months
          monthWeekday: string; // weekday for week-based months
        };
      }
    >
  >(() => {
    const initialStates: Record<string, any> = {};
    tasks.forEach((task) => {
      initialStates[task.clickup_task_id] = {
        schedule: task.schedule || '',
        skipWeekends: task.skip_weekends || false,
        copyTask: task.copy_task || false,
        recurType: task.recur_end ? 'until' : 'forever',
        recurEnd: task.recur_end ? new Date(task.recur_end).toISOString().split('T')[0] : '',
        isLoading: false,
        isEditing: !task.schedule, // Auto-edit mode for tasks without schedule
        customSchedule:
          task.schedule === 'custom' && task.schedule_options
            ? {
                interval: task.schedule_options.interval || 1,
                unit: task.schedule_options.unit || 'days',
                weekdays: task.schedule_options.weekdays || [],
                monthType: task.schedule_options.monthType || 'date',
                monthDate: task.schedule_options.monthDate || 1,
                monthWeek: task.schedule_options.monthWeek || 'first',
                monthWeekday: task.schedule_options.monthWeekday || 'monday',
              }
            : {
                interval: 1,
                unit: 'days',
                weekdays: [],
                monthType: 'date',
                monthDate: 1,
                monthWeek: 'first',
                monthWeekday: 'monday',
              },
      };
    });
    return initialStates;
  });

  const updateTaskState = (taskId: string, updates: Partial<(typeof taskStates)[string]>) => {
    setTaskStates((prev) => ({
      ...prev,
      [taskId]: { ...prev[taskId], ...updates },
    }));
  };

  const startEditing = (taskId: string) => {
    updateTaskState(taskId, { isEditing: true });
  };

  const cancelEditing = (taskId: string, originalTask: RecurringTask) => {
    // Reset to original values
    updateTaskState(taskId, {
      schedule: originalTask.schedule || '',
      skipWeekends: originalTask.skip_weekends || false,
      copyTask: originalTask.copy_task || false,
      recurType: originalTask.recur_end ? 'until' : 'forever',
      recurEnd: originalTask.recur_end ? new Date(originalTask.recur_end).toISOString().split('T')[0] : '',
      customSchedule: {
        interval: 1,
        unit: 'days',
        weekdays: [],
        monthType: 'date',
        monthDate: 1,
        monthWeek: 'first',
        monthWeekday: 'monday',
      },
      isEditing: false,
    });
  };

  const getScheduleLabel = (schedule: string, customSchedule?: any) => {
    const option = scheduleOptions.find((opt) => opt.value === schedule);
    if (option && schedule !== 'custom') {
      return option.label;
    }

    if (schedule === 'custom' && customSchedule) {
      const { interval, unit, weekdays, monthType, monthDate, monthWeek, monthWeekday } = customSchedule;
      let label = `Every ${interval} ${unit}`;

      if (unit === 'weeks' && weekdays.length > 0) {
        label += ` on ${weekdays.join(', ')}`;
      } else if (unit === 'months') {
        if (monthType === 'date') {
          label += ` on the ${monthDate === 'last' ? 'last day' : `${monthDate}th`}`;
        } else if (monthType === 'week') {
          label += ` on the ${monthWeek} ${monthWeekday}`;
        }
      }

      return label;
    }

    return option ? option.label : schedule;
  };

  const handleSave = async (taskId: string) => {
    const state = taskStates[taskId];
    if (!state.schedule) {
      toast({
        variant: 'destructive',
        title: 'Bitte wählen Sie einen Zeitplan aus',
      });
      return;
    }

    updateTaskState(taskId, { isLoading: true });

    try {
      const updateData: UpdateRecurringTaskData = {
        clickup_task_id: taskId,
        schedule: state.schedule,
        skip_weekends: state.skipWeekends,
        copy_task: state.copyTask,
        recur_end: state.recurType === 'until' ? state.recurEnd : null,
        schedule_options: state.schedule === 'custom' ? state.customSchedule : null,
      };

      const result = await updateRecurringTask(updateData);

      if (result.error) {
        toast({
          variant: 'destructive',
          title: 'Fehler beim Aktualisieren',
          description: result.error,
        });
      } else {
        toast({
          title: 'Zeitplan erfolgreich aktualisiert',
        });
        updateTaskState(taskId, { isEditing: false });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Ein unerwarteter Fehler ist aufgetreten',
      });
    } finally {
      updateTaskState(taskId, { isLoading: false });
    }
  };

  // Bulk selection handlers
  const toggleTaskSelection = (taskId: string) => {
    const newSelected = new Set(selectedTasks);
    if (newSelected.has(taskId)) {
      newSelected.delete(taskId);
    } else {
      newSelected.add(taskId);
    }
    setSelectedTasks(newSelected);
  };

  const selectAllTasks = () => {
    setSelectedTasks(new Set(tasks.map((task) => task.clickup_task_id)));
  };

  const clearSelection = () => {
    setSelectedTasks(new Set());
  };

  const startBulkEditing = () => {
    if (selectedTasks.size === 0) {
      toast({
        variant: 'destructive',
        title: 'Keine Aufgaben ausgewählt',
        description: 'Bitte wählen Sie mindestens eine Aufgabe aus.',
      });
      return;
    }
    setBulkDialogOpen(true);
  };

  const cancelBulkEditing = () => {
    setBulkDialogOpen(false);
    setBulkSettings({
      schedule: '',
      skipWeekends: false,
      copyTask: false,
      recurType: 'forever',
      recurEnd: '',
      customSchedule: {
        interval: 1,
        unit: 'days',
        weekdays: [],
        monthType: 'date',
        monthDate: 1,
        monthWeek: 'first',
        monthWeekday: 'monday',
      },
    });
  };

  const handleBulkSave = async () => {
    if (!bulkSettings.schedule) {
      toast({
        variant: 'destructive',
        title: 'Bitte wählen Sie einen Zeitplan aus',
      });
      return;
    }

    setIsBulkLoading(true);

    try {
      const updateData: BulkUpdateRecurringTasksData = {
        task_ids: Array.from(selectedTasks),
        schedule: bulkSettings.schedule,
        skip_weekends: bulkSettings.skipWeekends,
        recur_end: bulkSettings.recurType === 'until' ? bulkSettings.recurEnd : null,
        schedule_options: bulkSettings.schedule === 'custom' ? bulkSettings.customSchedule : null,
        copy_task: bulkSettings.copyTask
      };

      const result = await bulkUpdateRecurringTasks(updateData);

      if (result.error) {
        toast({
          variant: 'destructive',
          title: 'Fehler beim Aktualisieren',
          description: result.error,
        });
      } else {
        toast({
          title: 'Zeitpläne erfolgreich aktualisiert',
          description: `${result.updated_count} Aufgaben wurden aktualisiert.`,
        });
        setBulkDialogOpen(false);
        setSelectedTasks(new Set());
        // Update local state for all affected tasks
        selectedTasks.forEach((taskId) => {
          updateTaskState(taskId, {
            schedule: bulkSettings.schedule,
            skipWeekends: bulkSettings.skipWeekends,
            copyTask: bulkSettings.copyTask,
            recurType: bulkSettings.recurType,
            recurEnd: bulkSettings.recurEnd,
            customSchedule: bulkSettings.customSchedule,
            isEditing: false,
          });
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Ein unerwarteter Fehler ist aufgetreten',
      });
    } finally {
      setIsBulkLoading(false);
    }
  };

  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="py-8">
          <p className="text-center text-muted-foreground">Keine wiederkehrenden Aufgaben gefunden.</p>
        </CardContent>
      </Card>
    );
  }

  // Sort tasks: missing schedule first, then by task name
  const sortedTasks = [...tasks].sort((a, b) => {
    // Tasks without schedule come first
    if (!a.schedule && b.schedule) return -1;
    if (a.schedule && !b.schedule) return 1;

    // Then sort by task name
    const nameA = a.tasks?.name || `Task ${a.clickup_task_id}`;
    const nameB = b.tasks?.name || `Task ${b.clickup_task_id}`;
    return nameA.localeCompare(nameB);
  });

  return (
    <div className="space-y-4">
      {/* Bulk Actions Bar */}
      <Card>
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedTasks.size === tasks.length && tasks.length > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      selectAllTasks();
                    } else {
                      clearSelection();
                    }
                  }}
                />
                <Label className="text-sm font-medium">
                  {selectedTasks.size === 0 ? 'Alle auswählen' : `${selectedTasks.size} von ${tasks.length} ausgewählt`}
                </Label>
              </div>
              {selectedTasks.size > 0 && (
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={clearSelection}>
                    Auswahl aufheben
                  </Button>
                  <Dialog open={bulkDialogOpen} onOpenChange={setBulkDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={startBulkEditing}
                        className="flex items-center gap-2"
                      >
                        <IconSettings className="h-4 w-4" />
                        Massenbearbeitung
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <IconSettings className="h-5 w-5" />
                          Massenbearbeitung - {selectedTasks.size} Aufgaben
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label className="text-sm">Zeitplan</Label>
                            <Select
                              value={bulkSettings.schedule}
                              onValueChange={(value) => setBulkSettings((prev) => ({ ...prev, schedule: value }))}
                            >
                              <SelectTrigger className="h-9">
                                <SelectValue placeholder="Zeitplan auswählen" />
                              </SelectTrigger>
                              <SelectContent>
                                {scheduleOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm">Optionen</Label>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  checked={bulkSettings.skipWeekends}
                                  onCheckedChange={(checked) =>
                                    setBulkSettings((prev) => ({ ...prev, skipWeekends: !!checked }))
                                  }
                                />
                                <Label className="text-sm">Wochenenden ausschließen</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  checked={bulkSettings.copyTask}
                                  onCheckedChange={(checked) =>
                                    setBulkSettings((prev) => ({ ...prev, copyTask: !!checked }))
                                  }
                                />
                                <Label className="text-sm">Task kopieren</Label>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Custom Schedule Configuration for Bulk */}
                        {bulkSettings.schedule === 'custom' && (
                          <div className="space-y-4 p-4 border rounded-lg bg-white">
                            <h4 className="font-medium text-sm">Benutzerdefinierte Zeitplan-Konfiguration</h4>

                            {/* Interval and Unit */}
                            <div className="flex items-center gap-2">
                              <Label className="text-sm">Alle</Label>
                              <Input
                                type="number"
                                min="1"
                                value={bulkSettings.customSchedule.interval}
                                onChange={(e) =>
                                  setBulkSettings((prev) => ({
                                    ...prev,
                                    customSchedule: { ...prev.customSchedule, interval: parseInt(e.target.value) || 1 },
                                  }))
                                }
                                className="w-20 h-9"
                              />
                              <Select
                                value={bulkSettings.customSchedule.unit}
                                onValueChange={(value: 'days' | 'weeks' | 'months') =>
                                  setBulkSettings((prev) => ({
                                    ...prev,
                                    customSchedule: { ...prev.customSchedule, unit: value, weekdays: [] },
                                  }))
                                }
                              >
                                <SelectTrigger className="w-32 h-9">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="days">Tage</SelectItem>
                                  <SelectItem value="weeks">Wochen</SelectItem>
                                  <SelectItem value="months">Monate</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            {/* Weekdays for weeks */}
                            {bulkSettings.customSchedule.unit === 'weeks' && (
                              <div className="space-y-2">
                                <Label className="text-sm">Wochentage</Label>
                                <div className="flex flex-wrap gap-2">
                                  {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
                                    (day) => (
                                      <Button
                                        key={day}
                                        variant={
                                          bulkSettings.customSchedule.weekdays.includes(day) ? 'default' : 'outline'
                                        }
                                        size="sm"
                                        onClick={() => {
                                          const weekdays = bulkSettings.customSchedule.weekdays.includes(day)
                                            ? bulkSettings.customSchedule.weekdays.filter((d) => d !== day)
                                            : [...bulkSettings.customSchedule.weekdays, day];
                                          setBulkSettings((prev) => ({
                                            ...prev,
                                            customSchedule: { ...prev.customSchedule, weekdays },
                                          }));
                                        }}
                                        className="text-xs"
                                      >
                                        {day.slice(0, 2).toUpperCase()}
                                      </Button>
                                    ),
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Month configuration */}
                            {bulkSettings.customSchedule.unit === 'months' && (
                              <div className="space-y-3">
                                <RadioGroup
                                  value={bulkSettings.customSchedule.monthType}
                                  onValueChange={(value: 'date' | 'week') =>
                                    setBulkSettings((prev) => ({
                                      ...prev,
                                      customSchedule: { ...prev.customSchedule, monthType: value },
                                    }))
                                  }
                                >
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="date" id="bulk-date" />
                                    <Label htmlFor="bulk-date" className="text-sm">
                                      am Datum
                                    </Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="week" id="bulk-week" />
                                    <Label htmlFor="bulk-week" className="text-sm">
                                      nach Woche
                                    </Label>
                                  </div>
                                </RadioGroup>

                                {/* Date selection */}
                                {bulkSettings.customSchedule.monthType === 'date' && (
                                  <div className="space-y-2">
                                    <Label className="text-sm">Datum</Label>
                                    <Select
                                      value={bulkSettings.customSchedule.monthDate.toString()}
                                      onValueChange={(value) =>
                                        setBulkSettings((prev) => ({
                                          ...prev,
                                          customSchedule: {
                                            ...prev.customSchedule,
                                            monthDate: value === 'last' ? 'last' : parseInt(value),
                                          },
                                        }))
                                      }
                                    >
                                      <SelectTrigger className="w-32 h-9">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {Array.from({ length: 28 }, (_, i) => i + 1).map((day) => (
                                          <SelectItem key={day} value={day.toString()}>
                                            {day}th
                                          </SelectItem>
                                        ))}
                                        <SelectItem value="last">letzter</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                )}

                                {/* Week-based selection */}
                                {bulkSettings.customSchedule.monthType === 'week' && (
                                  <div className="space-y-3">
                                    <div className="space-y-2">
                                      <Label className="text-sm">Wochentag</Label>
                                      <div className="flex flex-wrap gap-2">
                                        {[
                                          'monday',
                                          'tuesday',
                                          'wednesday',
                                          'thursday',
                                          'friday',
                                          'saturday',
                                          'sunday',
                                        ].map((day) => (
                                          <Button
                                            key={day}
                                            variant={
                                              bulkSettings.customSchedule.monthWeekday === day ? 'default' : 'outline'
                                            }
                                            size="sm"
                                            onClick={() => {
                                              setBulkSettings((prev) => ({
                                                ...prev,
                                                customSchedule: { ...prev.customSchedule, monthWeekday: day },
                                              }));
                                            }}
                                            className="text-xs"
                                          >
                                            {day.slice(0, 2).toUpperCase()}
                                          </Button>
                                        ))}
                                      </div>
                                    </div>
                                    <div className="space-y-2">
                                      <Label className="text-sm">Woche</Label>
                                      <Select
                                        value={bulkSettings.customSchedule.monthWeek}
                                        onValueChange={(value: 'first' | 'second' | 'third' | 'fourth' | 'last') =>
                                          setBulkSettings((prev) => ({
                                            ...prev,
                                            customSchedule: { ...prev.customSchedule, monthWeek: value },
                                          }))
                                        }
                                      >
                                        <SelectTrigger className="w-32 h-9">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="first">Erste</SelectItem>
                                          <SelectItem value="second">Zweite</SelectItem>
                                          <SelectItem value="third">Dritte</SelectItem>
                                          <SelectItem value="fourth">Vierte</SelectItem>
                                          <SelectItem value="last">Letzte</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}

                        <div className="space-y-2">
                          <Label className="text-sm">Wiederholung</Label>
                          <Select
                            value={bulkSettings.recurType}
                            onValueChange={(value: 'forever' | 'until') =>
                              setBulkSettings((prev) => ({ ...prev, recurType: value }))
                            }
                          >
                            <SelectTrigger className="h-9">
                              <SelectValue placeholder="Wiederholung auswählen" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="forever">Für immer ausführen</SelectItem>
                              <SelectItem value="until">Ausführen bis</SelectItem>
                            </SelectContent>
                          </Select>

                          {bulkSettings.recurType === 'until' && (
                            <div className="mt-2">
                              <Label className="text-sm">Enddatum</Label>
                              <Input
                                type="date"
                                value={bulkSettings.recurEnd}
                                onChange={(e) => setBulkSettings((prev) => ({ ...prev, recurEnd: e.target.value }))}
                                className="h-9 mt-1"
                              />
                            </div>
                          )}
                        </div>

                        <Separator />

                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={cancelBulkEditing} disabled={isBulkLoading}>
                            <IconX className="h-4 w-4 mr-2" />
                            Abbrechen
                          </Button>
                          <Button onClick={handleBulkSave} disabled={isBulkLoading}>
                            <IconCheck className="h-4 w-4 mr-2" />
                            {isBulkLoading ? 'Speichern...' : `${selectedTasks.size} Aufgaben aktualisieren`}
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </div>
            <div>
              <IgnoredListsModal />
            </div>
          </div>
        </CardContent>
      </Card>

      <ScrollArea className="h-full">
        <div className="space-y-4">
          {sortedTasks.map((task) => {
            const state = taskStates[task.clickup_task_id];
            if (!state) return null;

            return (
              <Card key={task.clickup_task_id}>
                <CardHeader className={state.isEditing ? '' : 'pb-3'}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={selectedTasks.has(task.clickup_task_id)}
                        onCheckedChange={() => toggleTaskSelection(task.clickup_task_id)}
                      />
                      <div>
                        <CardTitle className={state.isEditing ? 'text-lg' : 'text-base'}>
                          {task.tasks?.name || `Task ${task.clickup_task_id}`}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground">
                          ID:{' '}
                          <a
                            href={`https://app.clickup.com/t/${task.clickup_task_id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            {task.clickup_task_id}
                          </a>
                        </p>
                        {task.tasks?.assignees && task.tasks.assignees.length > 0 && (
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-sm text-muted-foreground">Assignees:</span>
                            <div className="flex items-center gap-2">
                              {task.tasks.assignees.filter(assignee => assignee.clickup_user_id).map((assignee) => (
                                <div key={assignee.clickup_user_id} className="flex items-center gap-1">
                                  {assignee.profile_picture_url ? (
                                    <img
                                      src={assignee.profile_picture_url}
                                      alt={assignee.name}
                                      className="w-6 h-6 rounded-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                                      {assignee.name.charAt(0).toUpperCase()}
                                    </div>
                                  )}
                                  <span className="text-sm text-gray-700">{assignee.name}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    {!state.isEditing && task.schedule && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => startEditing(task.clickup_task_id)}
                        className="flex items-center gap-2"
                      >
                        <IconEdit className="h-4 w-4" />
                        Bearbeiten
                      </Button>
                    )}
                  </div>
                </CardHeader>

                {state.isEditing ? (
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label htmlFor={`schedule-${task.clickup_task_id}`} className="text-sm">
                          Zeitplan
                        </Label>
                        <Select
                          value={state.schedule}
                          onValueChange={(value) => updateTaskState(task.clickup_task_id, { schedule: value })}
                        >
                          <SelectTrigger id={`schedule-${task.clickup_task_id}`} className="h-9">
                            <SelectValue placeholder="Zeitplan auswählen" />
                          </SelectTrigger>
                          <SelectContent>
                            {scheduleOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Custom Schedule Configuration */}
                      {state.schedule === 'custom' && (
                        <div className="col-span-full space-y-4 p-4 border rounded-lg bg-gray-50">
                          <h4 className="font-medium text-sm">Benutzerdefinierte Zeitplan-Konfiguration</h4>

                          {/* Interval and Unit */}
                          <div className="flex items-center gap-2">
                            <Label className="text-sm">Alle</Label>
                            <Input
                              type="number"
                              min="1"
                              value={state.customSchedule.interval}
                              onChange={(e) =>
                                updateTaskState(task.clickup_task_id, {
                                  customSchedule: { ...state.customSchedule, interval: parseInt(e.target.value) || 1 },
                                })
                              }
                              className="w-20 h-9"
                            />
                            <Select
                              value={state.customSchedule.unit}
                              onValueChange={(value: 'days' | 'weeks' | 'months') =>
                                updateTaskState(task.clickup_task_id, {
                                  customSchedule: { ...state.customSchedule, unit: value, weekdays: [] },
                                })
                              }
                            >
                              <SelectTrigger className="w-32 h-9">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="days">Tage</SelectItem>
                                <SelectItem value="weeks">Wochen</SelectItem>
                                <SelectItem value="months">Monate</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* Weekdays for weeks */}
                          {state.customSchedule.unit === 'weeks' && (
                            <div className="space-y-2">
                              <Label className="text-sm">Wochentage</Label>
                              <div className="flex flex-wrap gap-2">
                                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(
                                  (day) => (
                                    <Button
                                      key={day}
                                      variant={state.customSchedule.weekdays.includes(day) ? 'default' : 'outline'}
                                      size="sm"
                                      onClick={() => {
                                        const weekdays = state.customSchedule.weekdays.includes(day)
                                          ? state.customSchedule.weekdays.filter((d) => d !== day)
                                          : [...state.customSchedule.weekdays, day];
                                        updateTaskState(task.clickup_task_id, {
                                          customSchedule: { ...state.customSchedule, weekdays },
                                        });
                                      }}
                                      className="text-xs"
                                    >
                                      {day.slice(0, 2).toUpperCase()}
                                    </Button>
                                  ),
                                )}
                              </div>
                            </div>
                          )}

                          {/* Month configuration */}
                          {state.customSchedule.unit === 'months' && (
                            <div className="space-y-3">
                              <RadioGroup
                                value={state.customSchedule.monthType}
                                onValueChange={(value: 'date' | 'week') =>
                                  updateTaskState(task.clickup_task_id, {
                                    customSchedule: { ...state.customSchedule, monthType: value },
                                  })
                                }
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="date" id={`date-${task.clickup_task_id}`} />
                                  <Label htmlFor={`date-${task.clickup_task_id}`} className="text-sm">
                                    am Datum
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="week" id={`week-${task.clickup_task_id}`} />
                                  <Label htmlFor={`week-${task.clickup_task_id}`} className="text-sm">
                                    nach Woche
                                  </Label>
                                </div>
                              </RadioGroup>

                              {/* Date selection */}
                              {state.customSchedule.monthType === 'date' && (
                                <div className="space-y-2">
                                  <Label className="text-sm">Datum</Label>
                                  <Select
                                    value={state.customSchedule.monthDate.toString()}
                                    onValueChange={(value) =>
                                      updateTaskState(task.clickup_task_id, {
                                        customSchedule: {
                                          ...state.customSchedule,
                                          monthDate: value === 'last' ? 'last' : parseInt(value),
                                        },
                                      })
                                    }
                                  >
                                    <SelectTrigger className="w-32 h-9">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {Array.from({ length: 28 }, (_, i) => i + 1).map((day) => (
                                        <SelectItem key={day} value={day.toString()}>
                                          {day}th
                                        </SelectItem>
                                      ))}
                                      <SelectItem value="last">letzter</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              )}

                              {/* Week-based selection */}
                              {state.customSchedule.monthType === 'week' && (
                                <div className="space-y-3">
                                  <div className="space-y-2">
                                    <Label className="text-sm">Wochentag</Label>
                                    <div className="flex flex-wrap gap-2">
                                      {[
                                        'monday',
                                        'tuesday',
                                        'wednesday',
                                        'thursday',
                                        'friday',
                                        'saturday',
                                        'sunday',
                                      ].map((day) => (
                                        <Button
                                          key={day}
                                          variant={state.customSchedule.monthWeekday === day ? 'default' : 'outline'}
                                          size="sm"
                                          onClick={() => {
                                            updateTaskState(task.clickup_task_id, {
                                              customSchedule: { ...state.customSchedule, monthWeekday: day },
                                            });
                                          }}
                                          className="text-xs"
                                        >
                                          {day.slice(0, 2).toUpperCase()}
                                        </Button>
                                      ))}
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <Label className="text-sm">Woche</Label>
                                    <Select
                                      value={state.customSchedule.monthWeek}
                                      onValueChange={(value: 'first' | 'second' | 'third' | 'fourth' | 'last') =>
                                        updateTaskState(task.clickup_task_id, {
                                          customSchedule: { ...state.customSchedule, monthWeek: value },
                                        })
                                      }
                                    >
                                      <SelectTrigger className="w-32 h-9">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="first">Erste</SelectItem>
                                        <SelectItem value="second">Zweite</SelectItem>
                                        <SelectItem value="third">Dritte</SelectItem>
                                        <SelectItem value="fourth">Vierte</SelectItem>
                                        <SelectItem value="last">Letzte</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label className="text-sm">Optionen</Label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`weekends-${task.clickup_task_id}`}
                              checked={state.skipWeekends}
                              onCheckedChange={(checked) =>
                                updateTaskState(task.clickup_task_id, { skipWeekends: !!checked })
                              }
                            />
                            <Label htmlFor={`weekends-${task.clickup_task_id}`} className="text-sm">
                              Wochenenden ausschließen
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`copy-task-${task.clickup_task_id}`}
                              checked={state.copyTask}
                              onCheckedChange={(checked) =>
                                updateTaskState(task.clickup_task_id, { copyTask: !!checked })
                              }
                            />
                            <Label htmlFor={`copy-task-${task.clickup_task_id}`} className="text-sm">
                              Task kopieren
                            </Label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`recurrence-${task.clickup_task_id}`} className="text-sm">
                        Wiederholung
                      </Label>
                      <Select
                        value={state.recurType}
                        onValueChange={(value: 'forever' | 'until') =>
                          updateTaskState(task.clickup_task_id, { recurType: value })
                        }
                      >
                        <SelectTrigger id={`recurrence-${task.clickup_task_id}`} className="h-9">
                          <SelectValue placeholder="Wiederholung auswählen" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="forever">Für immer ausführen</SelectItem>
                          <SelectItem value="until">Ausführen bis</SelectItem>
                        </SelectContent>
                      </Select>

                      {state.recurType === 'until' && (
                        <div className="mt-2">
                          <Label htmlFor={`end-date-${task.clickup_task_id}`} className="text-sm">
                            Enddatum
                          </Label>
                          <Input
                            id={`end-date-${task.clickup_task_id}`}
                            type="date"
                            value={state.recurEnd}
                            onChange={(e) => updateTaskState(task.clickup_task_id, { recurEnd: e.target.value })}
                            className="h-9 mt-1"
                          />
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end gap-2 pt-2">
                      {task.schedule && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => cancelEditing(task.clickup_task_id, task)}
                          disabled={state.isLoading}
                          className="flex items-center gap-2"
                        >
                          <IconX className="h-4 w-4" />
                          Abbrechen
                        </Button>
                      )}
                      <Button
                        size="sm"
                        onClick={() => handleSave(task.clickup_task_id)}
                        disabled={state.isLoading}
                        className="flex items-center gap-2"
                      >
                        <IconCheck className="h-4 w-4" />
                        {state.isLoading ? 'Speichern...' : 'Speichern'}
                      </Button>
                    </div>
                  </CardContent>
                ) : (
                  <CardContent className="pt-0">
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Zeitplan:</span>
                        <span className="font-medium">{getScheduleLabel(state.schedule, state.customSchedule)}</span>
                      </div>
                      {state.skipWeekends && (
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">Wochenenden:</span>
                          <span className="font-medium">Ausgeschlossen</span>
                        </div>
                      )}
                      {state.copyTask && (
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">Task kopieren:</span>
                          <span className="font-medium">Aktiviert</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Wiederholung:</span>
                        <span className="font-medium">
                          {state.recurType === 'forever' ? 'Für immer' : `Bis ${state.recurEnd}`}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
}
