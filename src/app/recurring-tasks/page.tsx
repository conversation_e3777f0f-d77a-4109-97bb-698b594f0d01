import { getRecurringTasks } from '@/server-actions/recurring-tasks/get-recurring-tasks';
import { RecurringTasksList } from './components/recurring-tasks-list';
import { getPermission } from '@/lib/employee';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function RecurringTasksPage() {
  const permission = await getPermission('recurring-tasks');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  const result = await getRecurringTasks();

  if ('error' in result) {
    return <div className="flex justify-center">Error loading recurring tasks: {result.error}</div>;
  }

  return (
    <div className="py-6">
      <RecurringTasksList tasks={result.data || []} />
    </div>
  );
}

export default RecurringTasksPage;
