'use client';

import { getGlobalSettings, saveNewFalseTimeEntriesListIdAction } from '@/server-actions/globals/get-global-settings';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import useSWR from 'swr';
import { useToast } from '@/components/ui/use-toast';

export function GeneralSettings() {
  const { toast } = useToast();
  const { mutate, data: globalSettingsData, error } = useSWR('global-settings', getGlobalSettings);
  const globalSettings = globalSettingsData?.data;

  if (error) {
    return <h1>Error {JSON.stringify(error)}</h1>;
  }

  const handleSubmit = async (formData: FormData) => {
    const res = await saveNewFalseTimeEntriesListIdAction(formData);
    if (!res.error) {
      await mutate();
      toast({ title: 'List IDs erfolgreich gespeichert' });
    } else {
      toast({ variant: 'destructive', title: '<PERSON><PERSON> beim Speichern der List IDs' });
      console.error('<PERSON><PERSON> beim Speichern der List IDs', res.error);
    }
  };

  return (
    <form action={handleSubmit}>
      <div className="flex flex-col gap-4 pt-4">
        <div className="flex flex-col w-full gap-1">
          <div>
            <Label htmlFor="false-timeentries-list-id">False Time-Entries ClickUp List ID</Label>
            <Input
              name="false-timeentries-list-id"
              id="false-timeentries-list-id"
              type="text"
              defaultValue={globalSettings?.false_timeentries_list_id || undefined}
            />
          </div>
        </div>
        <div className="flex flex-col w-full gap-1">
          <Label htmlFor="pto-calendar-list-id">PTO Kalender ClickUp List ID</Label>
          <Input
            name="pto-calendar-list-id"
            id="pto-calendar-list-id"
            type="text"
            defaultValue={globalSettings?.pto_list_id || undefined}
          />
        </div>
        <div className="flex flex-col w-full gap-1">
          <Label htmlFor="pm-list-id">Projektmanagement ClickUp List ID</Label>
          <Input name="pm-list-id" id="pm-list-id" type="text" defaultValue={globalSettings?.pm_list_id || undefined} />
        </div>
        <div className="flex flex-col w-full gap-1">
          <Label htmlFor="hourly-rate">Stundensatz</Label>
          <Input name="hourly-rate" id="hourly-rate" type="number" defaultValue={globalSettings?.general_hourly_rate} />
        </div>
        <Button type="submit">Speichern</Button>
      </div>
    </form>
  );
}
