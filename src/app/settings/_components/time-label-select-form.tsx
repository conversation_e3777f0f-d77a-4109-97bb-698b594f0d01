'use client';

import { ChangeEvent, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BexioBusinessActivity } from '@/data/types/bexio.types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { updateTimeLabels } from '@/server-actions/globals/get-time-labels';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { Tables } from '@/types/gen/database.types';

type TimeLabelsFormProps = {
  timeLabelMappings: Tables<'business_activities'>[];
  bexioBusinessActivities: BexioBusinessActivity[];
};

export function TimeLabelSelectForm({ timeLabelMappings, bexioBusinessActivities }: TimeLabelsFormProps) {
  const initialMapping = timeLabelMappings.reduce((acc: Map<string, BexioBusinessActivity>, curr: any) => {
    const ba = bexioBusinessActivities.find((ba) => ba.id === curr.bexio_business_activity_id);
    if (!ba) return acc;
    return acc.set(curr.clickup_task_tag, ba);
  }, new Map());
  const initialBillableAmountMapping = timeLabelMappings.reduce((acc: Map<string, number>, curr) => {
    return acc.set(curr.clickup_task_tag, curr.hourly_rate);
  }, new Map());
  const [mapping, setMapping] = useState<Map<string, BexioBusinessActivity>>(initialMapping);
  const [billableAmountMapping, setBillableAmountMapping] = useState<Map<string, number>>(initialBillableAmountMapping);
  const [newTag, setNewTag] = useState<string>('');
  const [newTagColor, setNewTagColor] = useState<string>('#000000');
  const [newTagHourlyRate, setNewTagHourlyRate] = useState<number>(145);
  const { toast } = useToast();
  const router = useRouter();

  const handleSelect = (clickupTag: string) => (bexioActivityId: string) => {
    const ba = bexioBusinessActivities.find((ba) => String(ba.id) === bexioActivityId);
    if (!ba) return;
    setMapping((prev) => {
      return new Map(prev).set(clickupTag, ba);
    });
  };

  const handleUpdateHourlyRate = (clickupTag: string, hourlyRate: number) => {
    setBillableAmountMapping((prev) => {
      const ba = prev.get(clickupTag);
      if (!ba) return prev;
      return new Map(prev).set(clickupTag, hourlyRate);
    });
  };

  const handleSubmit = async () => {
    const upsert = Array.from(mapping.entries())
      .filter(([ct, _]) => ct !== 'new-tag')
      .map(([clickupTag, bexioActivity]) => {
        return {
          bexio_business_activity_id: bexioActivity.id,
          clickup_task_tag: clickupTag,
          bexio_business_activity_name: bexioActivity.name,
          hourly_rate: billableAmountMapping.get(clickupTag),
        };
      });

    const res = await updateTimeLabels(upsert);

    if (!res.error) {
      router.refresh();
      toast({ title: 'Zeitlabels erfolgreich aktualisiert' });
    } else {
      toast({ variant: 'destructive', title: 'Fehler beim Aktualisieren der Zeitlabels' });
      console.error('Fehler beim Aktualisieren der Zeitlabels', res.error);
    }
  };

  const handleSubmitNewTag = async () => {
    if (newTag && mapping.get('new-tag')?.id) {
      const ba = mapping.get('new-tag')!;
      const upsert = [
        {
          bexio_business_activity_id: ba.id,
          clickup_task_tag: newTag,
          bexio_business_activity_name: ba.name,
          clickup_tag_fg: newTagColor,
          clickup_tag_bg: newTagColor,
          hourly_rate: newTagHourlyRate,
        },
      ];

      const res = await updateTimeLabels(upsert);

      if (!res.error) {
        router.refresh();
        toast({ title: 'Zeitlabel erfolgreich hinzugefügt' });
      } else {
        toast({ variant: 'destructive', title: 'Fehler beim hinzufügen des Zeitlabels' });
        console.error('Fehler beim hinzufügen des Zeitlabels', res.error);
      }
    }
  };

  const handleChangeNewTag = (e: ChangeEvent<HTMLInputElement>) => {
    setNewTag(e.target.value);
  };

  const handleChangeColorCode = (e: ChangeEvent<HTMLInputElement>) => {
    setNewTagColor(e.target.value);
  };

  return (
    <div className="flex flex-col gap-4 mt-2 items-center">
      <div className="flex flex-col gap-2 bg-white p-4 rounded-lg shadow-md min-w-[450px]">
        <h2 className="text-lg font-semibold">Time Labels / Tags</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ClickUp Label</TableHead>
              <TableHead>Bexio Tätigkeit</TableHead>
              <TableHead>Farbcode</TableHead>
              <TableHead>Stundensatz</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {timeLabelMappings.map((tag, index) => {
              return (
                <TableRow key={index}>
                  <TableCell>{tag.clickup_task_tag == 'null' ? 'Ohne Label' : tag.clickup_task_tag}</TableCell>
                  <TableCell>
                    <Select
                      defaultValue={String(mapping.get(tag.clickup_task_tag)?.id)}
                      onValueChange={handleSelect(tag.clickup_task_tag)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Bexio Tätigkeit wählen" />
                      </SelectTrigger>
                      <SelectContent>
                        {bexioBusinessActivities.map((ba) => (
                          <SelectItem key={ba.id} value={String(ba.id)}>
                            {ba.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {tag.clickup_tag_bg}
                      <div
                        className="size-[10px] rounded-full"
                        style={{ backgroundColor: String(tag.clickup_tag_bg) }}
                      ></div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Input
                      name={`hourlyRate-${tag.clickup_task_tag}`}
                      placeholder="Stundensatz"
                      defaultValue={tag.hourly_rate}
                      onChange={(event) => handleUpdateHourlyRate(tag.clickup_task_tag, Number(event.target.value))}
                    />
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <Button onClick={handleSubmit}>Speichern</Button>
      </div>
      <div className="flex flex-col bg-white p-4 rounded-lg shadow-md w-[450px]">
        <div className="mb-1">
          <h2 className="text-lg font-semibold">Neuen Tag hinzufügen</h2>
          <p>
            Achtung: Muss zuerst hier hinzugefügt werden und dann nochmals genau gleich auf ClickUp, inklusive der
            gleichen Farbcodes.
          </p>
        </div>
        <div className="flex gap-2 flex-col">
          <div className="flex gap-2">
            <Input name="new-tag" placeholder="Neuer Tag" onChange={handleChangeNewTag} />
            <Input name="new-tag-color" placeholder="#fff" onChange={handleChangeColorCode} />
            <div className="flex items-center">
              <div className="size-[20px] rounded-full" style={{ backgroundColor: newTagColor }}></div>
            </div>
          </div>
          <div className="flex gap-2">
            <Select onValueChange={handleSelect('new-tag')}>
              <SelectTrigger>
                <SelectValue placeholder="Bexio Tätigkeit wählen" />
              </SelectTrigger>
              <SelectContent>
                {bexioBusinessActivities.map((ba) => (
                  <SelectItem key={ba.id} value={String(ba.id)}>
                    {ba.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              name="hourlyRate"
              placeholder="Stundensatz"
              defaultValue={145}
              onChange={(event) => setNewTagHourlyRate(Number(event.target.value))}
            />
            <Button onClick={handleSubmitNewTag}>Speichern</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
