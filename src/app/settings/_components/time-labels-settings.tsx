import {
  getBexioBusinessActivities,
  getClickUpTimeEntriesTags,
  getTimeLabels,
} from '@/server-actions/globals/get-time-labels';
import { isBexioError } from '@/data/types/bexio.types';
import { isClickUpError } from '@/data/types/clickup.types';
import { TimeLabelSelectForm } from '@/app/settings/_components/time-label-select-form';
import { Suspense } from 'react';

export async function TimeLabelsSettings() {
  const { error: timeLabelError, data: timeLabelData } = await getTimeLabels();
  const bexioBusinessActivities = await getBexioBusinessActivities();
  const clickUpTimeEntriesTags = await getClickUpTimeEntriesTags();

  if (timeLabelError || isBexioError(bexioBusinessActivities) || isClickUpError(clickUpTimeEntriesTags)) {
    return <div>Error</div>;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TimeLabelSelectForm timeLabelMappings={timeLabelData} bexioBusinessActivities={bexioBusinessActivities} />
    </Suspense>
  );
}
