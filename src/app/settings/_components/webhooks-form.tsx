'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ClickUpSpace, ClickUpWebhook } from '@/data/types/clickup.types';
import { Button } from '@/components/ui/button';
import { createWebhookAction, deleteWebhook } from '@/server-actions/webhooks';
import { useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';

type WebhooksFormProps = {
  webhooks: ClickUpWebhook[];
  spaces: ClickUpSpace[];
};

export function WebhooksForm({ webhooks, spaces }: WebhooksFormProps) {
  const [spaceId, setSpaceId] = useState<string>();
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!spaceId) return;
    await createWebhookAction(spaceId);
    toast({ title: 'Webhook erstellt' });
  };

  const handleDelete = (webhookId: string) => async () => {
    await deleteWebhook(webhookId);
    toast({ variant: 'destructive', title: 'Webhook gelöscht' });
  };

  return (
    <div className="flex flex-col gap-2">
      <ScrollArea className="flex flex-col gap-4 max-h-[80vh]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Space</TableHead>
              <TableHead>Events</TableHead>
              <TableHead>Heath</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {webhooks.map((wh) => {
              return (
                <TableRow key={wh.id}>
                  <TableCell>{spaces.find((s) => s.id == wh.space_id)?.name}</TableCell>
                  <TableCell>{wh.events.join(', ')}</TableCell>
                  <TableCell className="min-w-[100px]">
                    {wh.health.fail_count} / {wh.health.status}
                  </TableCell>
                  <TableCell>
                    <Button onClick={handleDelete(wh.id)}>Löschen</Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </ScrollArea>
      <div className="flex gap-2">
        <Select onValueChange={setSpaceId}>
          <SelectTrigger>
            <SelectValue placeholder="Space wählen" />
          </SelectTrigger>
          <SelectContent>
            {spaces.map((s) => (
              <SelectItem key={s.id} value={String(s.id)}>
                {s.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={handleSubmit}>Hinzufügen</Button>
      </div>
    </div>
  );
}
