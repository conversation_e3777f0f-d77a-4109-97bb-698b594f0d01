import { Suspense } from 'react';
import { getWebhooksAction } from '@/server-actions/webhooks';
import { getSpacesAction } from '@/server-actions/spaces';
import { WebhooksForm } from '@/app/settings/_components/webhooks-form';

export async function WebhooksSettings() {
  const { error: webhooksError, data: webhooksData } = await getWebhooksAction();
  const { error: spacesError, data: spacesData } = await getSpacesAction();

  if (webhooksError || spacesError) {
    return <div>Error</div>;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <WebhooksForm webhooks={webhooksData} spaces={spacesData} />
    </Suspense>
  );
}
