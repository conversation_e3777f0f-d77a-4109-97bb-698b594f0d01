import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

type WidgetLoaderProps = {
  size?: 'small' | 'medium' | 'large' | 'xl';
};

export function CardLoader({ size = 'small' }: WidgetLoaderProps) {
  return (
    <Card
      className={cn(
        'flex flex-col w-[450px]',
        size === 'small' ? 'h-[250px]' : 'h-[500px]',
        size === 'xl' && 'w-[650px] h-[800px]',
      )}
    >
      <CardHeader>
        <Skeleton className="h-4" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4 mb-4 mt-4" />
        <Skeleton className="h-4 mb-4 mt-4" />
        <Skeleton className="h-4 mb-4" />
        <Skeleton className="h-4 mb-4" />

        {size !== 'small' ? (
          <>
            <Skeleton className="h-4 mb-4" />
            <Skeleton className="h-4 mb-4" />
            <Skeleton className="h-4 mb-4" />
            <Skeleton className="h-4 mb-4" />
            <Skeleton className="h-4 mb-4" />
            <Skeleton className="h-4 mb-4" />
          </>
        ) : null}
      </CardContent>
    </Card>
  );
}
