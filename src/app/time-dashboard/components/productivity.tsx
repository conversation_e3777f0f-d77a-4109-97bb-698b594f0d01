import { getProductivityReport } from '@/server-actions/time-reporting/productivity-report';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { isGenericError, msToHours } from '@/lib/utils';

type ProductivityProps = {
  result: Awaited<ReturnType<typeof getProductivityReport>>;
};

export function Productivity({ result }: ProductivityProps) {
  if (isGenericError(result)) {
    return (
      <Card className="w-[460px]">
        <CardHeader>Produktivität</CardHeader>
        <CardContent>
          <p>{result.error.message || 'Fehler'}</p>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="w-[460px]">
        <CardHeader>Produktivität</CardHeader>
        <CardContent>
          <p>Missing data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[460px]">
      <CardHeader>Produktivität</CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Kategorie</TableHead>
              <TableHead>Stunden</TableHead>
              <TableHead>Prozente</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>Abrechenbare Stunden</TableCell>
              <TableCell>{msToHours(result.data.billableTime).toFixed(2)}</TableCell>
              <TableCell>{((result.data.billableTime / result.data.timeSpent) * 100).toFixed(2)}%</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Nicht abrechenbare Stunden</TableCell>
              <TableCell>{msToHours(result.data.nonBillableTime).toFixed(2)}</TableCell>
              <TableCell>{((result.data.nonBillableTime / result.data.timeSpent) * 100).toFixed(2)}%</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Gesamtstunden</TableCell>
              <TableCell>{msToHours(result.data.timeSpent).toFixed(2)}</TableCell>
              <TableCell>100%</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
