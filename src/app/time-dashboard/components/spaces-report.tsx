'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { getSpacesReport } from '@/server-actions/time-reporting/spaces-report';
import { isGenericError, msToHours } from '@/lib/utils';
import { top10ColumnDef } from '@/app/time-dashboard/components/top10-column-def';
import { DataTable } from '@/app/time-dashboard/components/data-table';

type SpacesReportProps = {
  result: Awaited<ReturnType<typeof getSpacesReport>>;
};

export function SpacesReport({ result }: SpacesReportProps) {
  if (isGenericError(result)) {
    return (
      <Card className="w-[460px]">
        <CardHeader>Spaces Nutzung</CardHeader>
        <CardContent>
          <p>{result.error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="w-[460px]">
        <CardHeader>Spaces Nutzung</CardHeader>
        <CardContent>
          <p>Missing data</p>
        </CardContent>
      </Card>
    );
  }

  const tableData = Object.entries(result.data.spaces).map(([_, space]) => {
    return {
      name: space.name,
      timeInHours: msToHours(space.timeSpent),
      timeInHoursPercentage: (space.timeSpent / result.data.totalTimeSpent) * 100,
    };
  });

  return (
    <Card className="w-[460px]">
      <CardHeader>Spaces Nutzung</CardHeader>
      <CardContent>
        <DataTable columns={top10ColumnDef} data={tableData} />
      </CardContent>
    </Card>
  );
}
