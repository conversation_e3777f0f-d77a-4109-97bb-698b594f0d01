import { Productivity } from '@/app/time-dashboard/components/productivity';
import { Top10Global } from '@/app/time-dashboard/components/top-10-global';
import { Top10Clients } from '@/app/time-dashboard/components/top-10-clients';
import { Top10NonClients } from '@/app/time-dashboard/components/top-10-non-clients';
import { Top30Tasks } from '@/app/time-dashboard/components/top-30-tasks';
import { SpacesReport } from '@/app/time-dashboard/components/spaces-report';
import { TimeLabelsReport } from '@/app/time-dashboard/components/time-labels-report';
import { getProductivityReport } from '@/server-actions/time-reporting/productivity-report';
import { getSpacesReport } from '@/server-actions/time-reporting/spaces-report';
import { getTimeLabelsReport } from '@/server-actions/time-reporting/time-labels-report';
import { getTop10GlobalReport } from '@/server-actions/time-reporting/top-10-reports';
import { getTop30TasksReport } from '@/server-actions/time-reporting/top-30-tasks';

type TimeDashboardContentProps = {
  start: Date;
  end: Date;
  employeeIds: string[];
};

export async function TimeDashboardContent({ start, end, employeeIds }: TimeDashboardContentProps) {
  const [
    productivityReport,
    spacesReport,
    timeLabelsReport,
    top10GlobalReport,
    top10ClientsReport,
    top10NonClientsReport,
    top30TasksReport,
  ] = await Promise.all([
    getProductivityReport(start, end, employeeIds),
    getSpacesReport(start, end, employeeIds),
    getTimeLabelsReport(start, end, employeeIds),
    getTop10GlobalReport(start, end, employeeIds),
    getTop10GlobalReport(start, end, employeeIds, true),
    getTop10GlobalReport(start, end, employeeIds, false),
    getTop30TasksReport(start, end, employeeIds),
  ]);

  return (
    <>
      <div className="flex gap-2 w-[100%]">
        <Productivity result={productivityReport} />
        <SpacesReport result={spacesReport} />
        <TimeLabelsReport result={timeLabelsReport} />
      </div>

      <div className="flex gap-2 w-[100%]">
        <Top10Global result={top10GlobalReport} />
        <Top10Clients result={top10ClientsReport} />
        <Top10NonClients result={top10NonClientsReport} />
      </div>

      <div className="flex justify-center gap-2 w-[100%]">
        <Top30Tasks result={top30TasksReport} />
      </div>
    </>
  );
}
