'use client';

import { useEffect, useState } from 'react';
import { Multiselect } from '@/components/ui/multiselect';
import { CustomTimeRange } from '@/components/custom-time-range';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { TimeDashboardContentLoader } from '@/app/time-dashboard/components/time-dashboard-content-loader';
import { getISODateString } from '@/lib/utils';

type TimeDashboardProps = {
  employees: { name: string; clickup_user_id: string | null }[];
  selectedEmployeeIds: string[] | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
  children?: React.ReactNode;
};

export function TimeDashboard({ employees, startDate, selectedEmployeeIds, endDate, children }: TimeDashboardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [hiddenDateRange, setHiddenDateRange] = useState<{ start: Date; end: Date }>({
    start: startDate ? new Date(startDate) : new Date('2024-01-01'),
    end: endDate ? new Date(endDate) : new Date(),
  });
  const router = useRouter();
  const setDateRange = (start: Date, end: Date) => {
    setHiddenDateRange({ start, end });
  };
  const employeesOptions =
    employees.map((employee) => ({ key: String(employee.clickup_user_id), label: employee.name })) || [];
  const [hiddenSelectedEmployeeIds, setHiddenSelectedEmployeeIds] = useState<string[]>(
    selectedEmployeeIds || employeesOptions.map((option) => option.key),
  );

  const handleGo = async () => {
    const searchParams = new URLSearchParams();
    searchParams.set('startDate', getISODateString(hiddenDateRange.start));
    searchParams.set('endDate', getISODateString(hiddenDateRange.end));
    searchParams.set('employeeIds', hiddenSelectedEmployeeIds.join(','));
    router.push('time-dashboard?' + searchParams.toString(), {});
    setIsLoading(true);
  };
  useEffect(() => {
    setIsLoading(false);
  }, [children]);

  return (
    <div className="flex gap-4 justify-center mt-4 pb-8 max-w-full">
      <div className="flex flex-col gap-4 items-center max-w-full">
        <div className="flex gap-2 w-full">
          <CustomTimeRange
            start={hiddenDateRange.start}
            end={hiddenDateRange.end}
            onChange={setDateRange}
            defaultValue="this-year"
          />
          <Multiselect
            label="Mitarbeiter"
            options={employeesOptions}
            onChange={setHiddenSelectedEmployeeIds}
            defaultValues={hiddenSelectedEmployeeIds}
            canSelectAll={true}
          />
          <Button onClick={handleGo}>GO!</Button>
        </div>

        {isLoading ? <TimeDashboardContentLoader /> : children}
      </div>
    </div>
  );
}
