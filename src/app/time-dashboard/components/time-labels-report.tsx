'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getTimeLabelsReport } from '@/server-actions/time-reporting/time-labels-report';
import { isGenericError, msToHours } from '@/lib/utils';
import { top10ColumnDef } from '@/app/time-dashboard/components/top10-column-def';
import { DataTable } from '@/app/time-dashboard/components/data-table';

type TimeLabelsReportProps = {
  result: Awaited<ReturnType<typeof getTimeLabelsReport>>;
};

export function TimeLabelsReport({ result }: TimeLabelsReportProps) {
  if (isGenericError(result)) {
    return (
      <Card className="w-[450px]">
        <CardHeader>Time Labels Nutzung</CardHeader>
        <CardContent>
          <p>{result.error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="w-[450px]">
        <CardHeader>Time Labels Nutzung</CardHeader>
        <CardContent>
          <p>Missing data</p>
        </CardContent>
      </Card>
    );
  }

  const tableData = Object.entries(result.data.labels).map(([_, label]) => {
    return {
      name: label.name,
      timeInHours: msToHours(label.timeSpent),
      timeInHoursPercentage: (label.timeSpent / result.data.totalTimeSpent) * 100,
    };
  });

  return (
    <Card className="w-[450px]">
      <CardHeader>Time Labels Nutzung</CardHeader>
      <CardContent>
        <DataTable columns={top10ColumnDef} data={tableData} />
      </CardContent>
    </Card>
  );
}
