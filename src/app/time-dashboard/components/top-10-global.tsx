'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getTop10GlobalReport } from '@/server-actions/time-reporting/top-10-reports';
import { isGenericError } from '@/lib/utils';
import { top10ColumnDef } from '@/app/time-dashboard/components/top10-column-def';
import { DataTable } from '@/app/time-dashboard/components/data-table';

type ProductivityProps = {
  result: Awaited<ReturnType<typeof getTop10GlobalReport>>;
};

export function Top10Global({ result }: ProductivityProps) {
  if (isGenericError(result)) {
    return (
      <Card className="w-[450px]">
        <CardHeader>Top 10 Global</CardHeader>
        <CardContent>
          <p>{result.error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="w-[450px]">
        <CardHeader>Top 10 Global</CardHeader>
        <CardContent>
          <p>Missing data</p>
        </CardContent>
      </Card>
    );
  }

  const tableData = result.data.projects.map((project) => {
    return {
      name: project.name,
      timeInHours: project.timeInHours,
      timeInHoursPercentage: (project.timeInHours / result.data.totalTimeInHours) * 100,
    };
  });

  return (
    <Card className="w-[460px]">
      <CardHeader>Top 10 Global</CardHeader>
      <CardContent>
        <DataTable columns={top10ColumnDef} data={tableData} />
      </CardContent>
    </Card>
  );
}
