'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getTop30TasksReport } from '@/server-actions/time-reporting/top-30-tasks';
import { isGenericError } from '@/lib/utils';
import { DataTable } from '@/app/time-dashboard/components/data-table';
import { top10ColumnDef } from '@/app/time-dashboard/components/top10-column-def';

type Top10TasksProps = {
  result: Awaited<ReturnType<typeof getTop30TasksReport>>;
};

export function Top30Tasks({ result }: Top10TasksProps) {
  if (isGenericError(result)) {
    return (
      <Card className="w-[650px]">
        <CardHeader>Top 30 Tasks</CardHeader>
        <CardContent>
          <p>{result.error.message}</p>
        </CardContent>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="w-[650px]">
        <CardHeader>Top 30 Tasks</CardHeader>
        <CardContent>
          <p>Missing data</p>
        </CardContent>
      </Card>
    );
  }

  const tableData = result.data.tasks?.map((tasks) => {
    return {
      name: tasks.name,
      timeInHours: tasks.timeInHours,
      timeInHoursPercentage: (tasks.timeInHours / result.data.totalTimeInHours) * 100,
    };
  });

  return (
    <Card className="w-[650px]">
      <CardHeader>Top 30 Tasks</CardHeader>
      <CardContent>
        <DataTable columns={top10ColumnDef} data={tableData} />
      </CardContent>
    </Card>
  );
}
