import { ColumnDef } from '@tanstack/react-table';
import { ColumnSortButton } from '@/components/table/column-sort-button';

export const top10ColumnDef: ColumnDef<any>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Name
          <ColumnSortButton column={column} />
        </div>
      );
    },
  },
  {
    accessorKey: 'timeInHours',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Stunden
          <ColumnSortButton column={column} />
        </div>
      );
    },
    cell: (ctx) => {
      return <div>{Number(ctx.renderValue()).toFixed(2)}</div>;
    },
  },
  {
    accessorKey: 'timeInHoursPercentage',
    header: ({ column }) => {
      return (
        <div className="flex items-center">
          Prozent
          <ColumnSortButton column={column} />
        </div>
      );
    },
    cell: (ctx) => {
      return <div>{Number(ctx.renderValue()).toFixed(2)}%</div>;
    },
  },
];
