import { TimeDashboard } from '@/app/time-dashboard/components/time-dashboard';
import { getDomainEmployees } from '@/server-actions/domain-employees';
import { getPermission } from '@/lib/employee';
import { TimeDashboardContent } from '@/app/time-dashboard/components/time-dashboard-content';
import { Suspense } from 'react';
import { TimeDashboardContentLoader } from '@/app/time-dashboard/components/time-dashboard-content-loader';

export const maxDuration = 300;
export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

type TimeDashboardPageProps = {
  searchParams: {
    startDate: string | undefined;
    endDate: string | undefined;
    employeeIds: string | string[] | undefined;
  };
};

async function TimeDashboardPage({ searchParams }: TimeDashboardPageProps) {
  const permission = await getPermission('time-dashboard');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  const { data: employees, error } = await getDomainEmployees(true, true);

  if (error) {
    return <div>Error</div>;
  }

  const employeeIds = searchParams.employeeIds
    ? Array.isArray(searchParams.employeeIds)
      ? searchParams.employeeIds
      : searchParams.employeeIds.split(',')
    : employees.map((employee) => employee.clickup_user_id!);
  if (!searchParams.startDate || !searchParams.endDate) {
    return (
      <TimeDashboard
        employees={employees}
        selectedEmployeeIds={employeeIds}
        startDate={searchParams.startDate}
        endDate={searchParams.endDate}
      />
    );
  }

  const start = searchParams.startDate ? new Date(searchParams.startDate) : new Date('2024-01-01');
  const end = searchParams.endDate ? new Date(searchParams.endDate) : new Date();

  return (
    <TimeDashboard
      employees={employees}
      selectedEmployeeIds={employeeIds}
      startDate={searchParams.startDate}
      endDate={searchParams.endDate}
    >
      <Suspense fallback={<TimeDashboardContentLoader />}>
        <TimeDashboardContent start={start} end={end} employeeIds={employeeIds} />
      </Suspense>
    </TimeDashboard>
  );
}

export default TimeDashboardPage;
