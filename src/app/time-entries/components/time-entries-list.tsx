'use client';

import { <PERSON><PERSON>A<PERSON> } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { convertUNIXTimestampToDateTimeString, prettyPrintTime } from '@/lib/utils';
import Link from 'next/link';
import { IconExternalLink } from '@tabler/icons-react';
import { getFaultyTagsEntriesAction } from '@/server-actions/time-reporting/faults-tags-entries';
import useSWR from 'swr';
import { DateRange } from 'react-day-picker';
import { TrashIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { deleteTimeEntry } from '@/server-actions/time/time-actions';
import { isClickUpError } from '@/data/types/clickup.types';
import { Card, CardContent } from '@/components/ui/card';

type TimeEntriesListProps = {
  userIds: string[];
  filterType: string;
  dateRange: DateRange | undefined;
};

export function TimeEntriesList({ userIds, filterType, dateRange }: TimeEntriesListProps) {
  const { toast } = useToast();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const { data, error, mutate } = useSWR(['get-faulty-time-entries', userIds, filterType, dateRange], () =>
    getFaultyTagsEntriesAction(userIds, filterType, dateRange),
  );

  if (error || (data && 'error' in data)) {
    return (
      <>
        <div>There was an error fetching the time entries {JSON.stringify(error)}</div>
      </>
    );
  }

  if (!data) {
    return <div>Loading...</div>;
  }

  const handleDelete = (entryId: string | null) => async () => {
    const res = await deleteTimeEntry(String(entryId));
    const isError = isClickUpError(res) || ('error' in res && res.error);
    if (!isError) {
      await mutate();
      toast({
        title: 'Eintrag gelöscht.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Fehler beim Löschen des Eintrags.',
      });
      console.error('Fehler beim Löschen des Eintrags', res);
    }
    setDeleteModalOpen(false);
  };

  return (
    <Card className="w-fit">
      <CardContent className="flex flex-col gap-4 items-center mt-4">
        <ScrollArea className="flex flex-col gap-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mitarbeiter</TableHead>
                <TableHead>Start</TableHead>
                <TableHead>Ende</TableHead>
                <TableHead>Dauer</TableHead>
                <TableHead>Problem(e)</TableHead>
                <TableHead>Link (Task)</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.faultyEntries.map((te) => {
                return (
                  <TableRow key={te.id}>
                    <TableCell>{te.employees?.name || '-'}</TableCell>
                    <TableCell>{convertUNIXTimestampToDateTimeString(Number(te.clickup_start))}</TableCell>
                    <TableCell>{convertUNIXTimestampToDateTimeString(Number(te.clickup_end))}</TableCell>
                    <TableCell>{prettyPrintTime(Number(te.clickup_duration))}</TableCell>
                    <TableCell>
                      <p>{te.clickup_task_tag === 'null' && 'Kein Label'}</p>
                      <p>{!te.clickup_task_id && 'Kein Task'}</p>
                    </TableCell>
                    <TableCell>
                      {te.clickup_task_id && (
                        <Link href={`https://app.clickup.com/t/${te.clickup_task_id}`} target="_blank">
                          <IconExternalLink width={20} />
                        </Link>
                      )}
                    </TableCell>
                    <TableCell>
                      <Dialog open={deleteModalOpen}>
                        <DialogTrigger asChild>
                          <Button variant="ghost" onClick={() => setDeleteModalOpen(true)}>
                            <TrashIcon className="h-4 w-4 text-red-500" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                          <DialogHeader>
                            <DialogTitle>Eintrag löschen</DialogTitle>
                            <DialogDescription>
                              Bist du sicher, dass du diesen Eintrag löschen möchtest?
                            </DialogDescription>
                            <DialogFooter>
                              <Button variant="secondary" onClick={() => setDeleteModalOpen(false)}>
                                Abbrechen
                              </Button>
                              <Button variant="destructive" onClick={handleDelete(te.clickup_time_entry_id)}>
                                Löschen
                              </Button>
                            </DialogFooter>
                          </DialogHeader>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                );
              })}
              {data.overlappingEntries.map((overlap) => {
                return (
                  <TableRow key={overlap.entry1.id}>
                    <TableCell>{overlap.entry1.employees?.name}</TableCell>
                    <TableCell colSpan={3}>
                      <p>
                        {convertUNIXTimestampToDateTimeString(Number(overlap.entry1.clickup_start))} -{' '}
                        {convertUNIXTimestampToDateTimeString(Number(overlap.entry1.clickup_end))}
                      </p>
                      <p>
                        {convertUNIXTimestampToDateTimeString(Number(overlap.entry2.clickup_start))} -{' '}
                        {convertUNIXTimestampToDateTimeString(Number(overlap.entry2.clickup_end))}
                      </p>
                    </TableCell>
                    <TableCell>Überlappender Zeiteintrag</TableCell>
                    <TableCell>
                      <Link href={`https://app.clickup.com/t/${overlap.entry1.clickup_task_id}`} target="_blank">
                        <IconExternalLink width={20} />
                      </Link>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
