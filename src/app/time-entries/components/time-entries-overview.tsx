'use client';

import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TimeEntriesList } from '@/app/time-entries/components/time-entries-list';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CalendarIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { DateRange } from 'react-day-picker';
import { Multiselect } from '@/components/ui/multiselect';

type TimeEntriesOverviewProps = {
  employees: { name: string; clickup_user_id: string | null }[];
};

export function TimeEntriesOverview({ employees }: TimeEntriesOverviewProps) {
  const [activeUserIds, setActiveUserIds] = useState<string[]>(
    employees.map((employee) => String(employee.clickup_user_id)),
  );
  const [filterType, setFilterType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date('2024-04-01'),
    to: new Date(),
  });

  const employeesOptions =
    employees.map((employee) => ({ key: String(employee.clickup_user_id), label: employee.name })) || [];

  return (
    <div className="flex flex-col gap-4 mt-4 items-center">
      <form className="flex flex-col gap-4">
        <div className="flex gap-2">
          <Multiselect
            label="Mitarbeiter"
            options={employeesOptions}
            onChange={setActiveUserIds}
            defaultValues={activeUserIds}
            canSelectAll={true}
          />
          <Select name="filterType" onValueChange={setFilterType} defaultValue={filterType}>
            <SelectTrigger>
              <SelectValue placeholder="Filter Wählen" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem key="all" value="all">
                Alle Typen
              </SelectItem>
              <SelectItem key="missing-labels" value="missing-labels">
                Ohne Label
              </SelectItem>
              <SelectItem key="missing-task" value="missing-task">
                Ohne Task
              </SelectItem>
              <SelectItem key="overlapping" value="overlapping">
                Überlappende Zeiteinträge
              </SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant={'outline'}
                className={cn('justify-start text-left font-normal', !dateRange && 'text-muted-foreground')}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, 'LLL dd, y')} - {format(dateRange.to, 'LLL dd, y')}
                    </>
                  ) : (
                    format(dateRange.from, 'LLL dd, y')
                  )
                ) : (
                  <span>Zeitspanne Wählen</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>
        <TimeEntriesList userIds={activeUserIds} filterType={filterType} dateRange={dateRange} />
      </form>
    </div>
  );
}
