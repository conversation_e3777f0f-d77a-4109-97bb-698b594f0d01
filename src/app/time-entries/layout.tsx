'use client';

import { useSidebar } from '@/components/ui/sidebar';
import { Header } from '@/components/app-header';
import { cn } from '@/lib/utils';
import { CustomSidebar } from '@/components/custom-sidebar';

export default function Layout({ children }: { children: React.ReactNode }) {
  const { open } = useSidebar();

  return (
    <>
      <CustomSidebar />
      <main
        className={cn('w-screen', open && 'w-[calc(100vw - 256px)]')}
        style={{
          width: open ? 'calc(100vw - 256px)' : '100vw',
        }}
      >
        <Header />
        <div className="px-4 max-w-full">{children}</div>
      </main>
    </>
  );
}
