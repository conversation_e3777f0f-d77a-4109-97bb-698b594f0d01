import { CustomSidebar } from '@/components/custom-sidebar';
import { TimeEntriesOverview } from '@/app/time-entries/components/time-entries-overview';
import { getDomainEmployees } from '@/server-actions/domain-employees';
import { getPermission } from '@/lib/employee';
import { headers } from 'next/headers';

export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function TimeEntriesPage() {
  const permission = await getPermission('time-entries');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  let { error, data } = await getDomainEmployees(true);

  if (error) {
    return <div>Error</div>;
  }

  if (permission === 'limited') {
    const headersList = headers();
    const name = headersList.get('x-user-name');
    if (!name) return <div>You are not allowed to access this page</div>;
    data = data?.filter((employee) => employee.name === name) || [];
  }

  return <TimeEntriesOverview employees={data || []} />;
}

export default TimeEntriesPage;
