import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

export function TableSkeleton() {
  return (
    <ScrollArea className="flex flex-col rounded-md border gap-4 max-w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="whitespace-nowrap">Name</TableHead>
            <TableHead className="whitespace-nowrap">Zeit Soll</TableHead>
            <TableHead className="whitespace-nowrap">Gesamte Zeit</TableHead>
            <TableHead className="whitespace-nowrap">ClickUp Zeit</TableHead>
            <TableHead className="whitespace-nowrap">Überstunden</TableHead>
            <TableHead className="whitespace-nowrap">Überstunden Saldo</TableHead>
            <TableHead className="whitespace-nowrap">davon <PERSON><PERSON>zeit Saldo</TableHead>
            <TableHead className="whitespace-nowrap">Produktivität</TableHead>
            <TableHead className="whitespace-nowrap"><PERSON><PERSON> bezogen</TableHead>
            <TableHead className="whitespace-nowrap">Ferien Guthaben Saldo</TableHead>
            <TableHead className="whitespace-nowrap">Ferien Geplant Saldo</TableHead>
            <TableHead className="whitespace-nowrap">Krankheit</TableHead>
            <TableHead className="whitespace-nowrap">Unfall</TableHead>
            <TableHead className="whitespace-nowrap">Militär</TableHead>
            <TableHead className="whitespace-nowrap">Elternzeit</TableHead>
            <TableHead className="whitespace-nowrap">Weiterbildung</TableHead>
            <TableHead className="whitespace-nowrap">Feiertage</TableHead>
            <TableHead className="whitespace-nowrap">Sonstige PTO</TableHead>
            <TableHead className="whitespace-nowrap">Leere Stunden</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 3 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[120px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[150px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
              <TableCell className="whitespace-nowrap">
                <Skeleton className="h-3 w-[100px]" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  );
}
