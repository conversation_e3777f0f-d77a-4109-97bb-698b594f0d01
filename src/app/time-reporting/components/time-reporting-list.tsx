'use client';

import { getSimpleTimeReportAction } from '@/server-actions/time-reporting/simple-report';
import { ScrollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useMemo, useState } from 'react';
import useSWR from 'swr';
import { getISODateString, prettyPrintNumber } from '@/lib/utils';
import { Multiselect } from '@/components/ui/multiselect';
import { useEmployeeSelection } from '@/app/time-reporting/components/use-employee-selection';
import { CustomTimeRange } from '@/components/custom-time-range';
import { TableSkeleton } from '@/app/time-reporting/components/table-skelleton';
import { useDownloadTimeReporting } from '@/app/time-reporting/components/use-download-time-reporting';
import { IconDownload } from '@tabler/icons-react';

type TimeEntriesListState = {
  employees: { name: string; clickup_user_id: string | null }[];
};

export function TimeReportingList({ employees }: TimeEntriesListState) {
  const { employeesOptions, activeUserIds, setActiveUserIds } = useEmployeeSelection(employees);
  const [startDate, setStartDate] = useState<Date>(new Date('2024-01-01'));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [dateKey, setDateKey] = useState<string>('start');
  const [count, setCount] = useState(0);
  const [customTimeRange, setCustomTimeRange] = useState<string>('this-year');
  const setDateRange = (start: Date, end: Date, range?: string) => {
    console.log('setDateRange', start, end, range);
    console.log('setDateRangeTS', start.getTime(), end.getTime());
    setStartDate(start);
    setEndDate(end);
    if (range) {
      setCustomTimeRange(range);
    }
  };

  const [start, end, startTime, endTime] = useMemo(() => {
    let start = `${new Date().getFullYear()}-01-01`;
    let end = getISODateString(new Date());
    let startTime = new Date(new Date(start).setHours(0, 0, 0, 0));
    let endTime = new Date(new Date(end).setHours(23, 59, 59, 0));

    if (startDate) {
      start = getISODateString(startDate);
      startTime = new Date(new Date(start).setHours(0, 0, 0, 0));
    }
    if (endDate) {
      end = getISODateString(endDate);
      endTime = new Date(new Date(end).setHours(23, 59, 59, 0));
    }
    return [start, end, startTime, endTime];
  }, [startDate, endDate]);
  const {
    isLoading,
    data: timeData,
    error,
    mutate,
  } = useSWR(
    ['time-reporting-list', dateKey, count],
    () => getSimpleTimeReportAction(start, end, startTime, endTime, activeUserIds),
    {
      revalidateOnMount: false,
      refreshWhenHidden: false,
      revalidateOnFocus: false,
    },
  );

  const { downloadExcel } = useDownloadTimeReporting(timeData?.data || []);

  const handleGo = async () => {
    setCount((prev) => prev + 1);
    setDateKey(startDate?.toISOString() + '#' + endDate?.toISOString());
    await mutate();
  };

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 justify-center mt-4 max-w-full">
        <div className="flex gap-2 w-[100%]">
          <CustomTimeRange start={startDate} end={endDate} onChange={setDateRange} defaultValue={customTimeRange} />
          <Multiselect
            label="Mitarbeiter"
            options={employeesOptions}
            onChange={setActiveUserIds}
            defaultValues={activeUserIds}
            canSelectAll={true}
          />
          <Button onClick={handleGo}>GO!</Button>
        </div>
        <TableSkeleton />
      </div>
    );
  }

  if (!timeData && error) {
    console.log('ERROR TIME DATA', error);
    return <div className="flex justify-center">There was an error fetching the time report</div>;
  }

  const data = timeData?.data || [];

  return (
    <div className="flex flex-col gap-4 justify-center mt-4 max-w-full">
      <div className="flex w-[100%] justify-between">
        <div className="flex gap-2">
          <CustomTimeRange start={startDate} end={endDate} onChange={setDateRange} defaultValue={customTimeRange} />
          <Multiselect
            label="Mitarbeiter"
            options={employeesOptions}
            onChange={setActiveUserIds}
            defaultValues={activeUserIds}
            canSelectAll={true}
          />
          <Button onClick={handleGo}>GO!</Button>
        </div>
        <Button variant="outline" onClick={downloadExcel}>
          Export <IconDownload className="ml-2 h-4 w-4" />
        </Button>
      </div>
      <ScrollArea className="flex flex-col rounded-md border gap-4 max-w-full">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="whitespace-nowrap">Name</TableHead>
              <TableHead className="whitespace-nowrap">Zeit Soll</TableHead>
              <TableHead className="whitespace-nowrap">Gesamte Zeit</TableHead>
              <TableHead className="whitespace-nowrap">ClickUp Zeit</TableHead>
              <TableHead className="whitespace-nowrap">Überstunden</TableHead>
              <TableHead className="whitespace-nowrap">Überstunden Saldo</TableHead>
              <TableHead className="whitespace-nowrap">davon Überzeit Saldo</TableHead>
              <TableHead className="whitespace-nowrap">Produktivität</TableHead>
              <TableHead className="whitespace-nowrap">Ferien bezogen</TableHead>
              <TableHead className="whitespace-nowrap">Ferien Guthaben Saldo</TableHead>
              <TableHead className="whitespace-nowrap">Ferien Geplant Saldo</TableHead>
              <TableHead className="whitespace-nowrap">Krankheit</TableHead>
              <TableHead className="whitespace-nowrap">Unfall</TableHead>
              <TableHead className="whitespace-nowrap">Militär</TableHead>
              <TableHead className="whitespace-nowrap">Elternzeit</TableHead>
              <TableHead className="whitespace-nowrap">Weiterbildung</TableHead>
              <TableHead className="whitespace-nowrap">Feiertage</TableHead>
              <TableHead className="whitespace-nowrap">Sonstige PTO</TableHead>
              <TableHead className="whitespace-nowrap">Leere Stunden</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map((employee, index) => {
              return (
                <TableRow key={index}>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.name)}</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.workingHours)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.totalHours)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.clickupHours)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.overHours)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.overHoursSaldo)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.overtime)} h</TableCell>
                  <TableCell className="whitespace-nowrap">{`${employee.productivity}%`}</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.usedPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.ptoCreditsSaldo)} d</TableCell>
                  <TableCell className="whitespace-nowrap">
                    {prettyPrintNumber(employee.estimatedNotUsedPTO)} d
                  </TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.sickPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.accidentPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.militaryPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.parentPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.educationPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.publicHolidayPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.otherPTO)} d</TableCell>
                  <TableCell className="whitespace-nowrap">{prettyPrintNumber(employee.emptyHours)} h</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
