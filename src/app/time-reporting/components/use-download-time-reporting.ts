import { prettyPrintNumber } from '@/lib/utils';
import * as XLSX from 'xlsx';

export function useDownloadTimeReporting(data: any) {
  const downloadExcel = () => {
    // Define headers based on the TableHead content
    const headers = [
      'Name',
      'Zeit Soll (h)',
      'Gesamte Zeit (h)',
      'ClickUp Zeit (h)',
      'Überstunden (h)',
      'Überstunden Saldo (h)',
      'davon <PERSON>ze<PERSON> Saldo (h)',
      'Produktivität (%)',
      '<PERSON><PERSON>zo<PERSON> (d)',
      '<PERSON><PERSON> (d)',
      '<PERSON><PERSON> (d)',
      'Krankheit (d)',
      'Unfall (d)',
      '<PERSON><PERSON><PERSON><PERSON> (d)',
      'Elternzeit (d)',
      'Weiterbildung (d)',
      'Feiertage (d)',
      'Sonstige PTO (d)',
      '<PERSON>re Stunden (h)',
    ];

    // Prepare data for Excel
    const excelData = data.map((employee: any) => ({
      Name: employee.name,
      '<PERSON><PERSON> (h)': `${prettyPrintNumber(employee.workingHours)}`,
      'Gesamte Zeit (h)': `${prettyPrintNumber(employee.totalHours)}`,
      'ClickUp Zeit (h)': `${prettyPrintNumber(employee.clickupHours)}`,
      'Überstunden (h)': `${prettyPrintNumber(employee.overHours)}`,
      'Überstunden Saldo (h)': `${prettyPrintNumber(employee.overHoursSaldo)}`,
      'davon Überzeit Saldo (h)': `${prettyPrintNumber(employee.overtime)}`,
      'Produktivität (%)': `${employee.productivity}`,
      'Ferien bezogen (d)': `${prettyPrintNumber(employee.usedPTO)}`,
      'Ferien Guthaben Saldo (d)': `${prettyPrintNumber(employee.ptoCreditsSaldo)}`,
      'Ferien Geplant Saldo (d)': `${prettyPrintNumber(employee.estimatedNotUsedPTO)}`,
      'Krankheit (d)': `${prettyPrintNumber(employee.sickPTO)}`,
      'Unfall (d)': `${prettyPrintNumber(employee.accidentPTO)}`,
      'Militär (d)': `${prettyPrintNumber(employee.militaryPTO)}`,
      'Elternzeit (d)': `${prettyPrintNumber(employee.parentPTO)}`,
      'Weiterbildung (d)': `${prettyPrintNumber(employee.educationPTO)}`,
      'Feiertage (d)': `${prettyPrintNumber(employee.publicHolidayPTO)}`,
      'Sonstige PTO (d)': `${prettyPrintNumber(employee.otherPTO)}`,
      'Leere Stunden (h)': `${prettyPrintNumber(employee.emptyHours)}`,
    }));

    const worksheet = XLSX.utils.json_to_sheet(excelData, { header: headers });
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Time Report');

    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Trigger download
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'time_report.xlsx';
    link.click();
    URL.revokeObjectURL(url);
  };

  return {
    downloadExcel,
  };
}
