import { useState } from 'react';

type UseEmployeeSelectionProps = { name: string; clickup_user_id: string | null }[];

export function useEmployeeSelection(employees: UseEmployeeSelectionProps) {
  const [activeUserIds, setActiveUserIds] = useState<string[]>(
    employees.map((employee) => String(employee.clickup_user_id)),
  );

  const employeesOptions =
    employees.map((employee) => ({ key: String(employee.clickup_user_id), label: employee.name })) || [];

  return {
    activeUserIds,
    setActiveUserIds,
    employeesOptions,
  };
}
