import { CustomSidebar } from '@/components/custom-sidebar';
import { TimeReportingList } from '@/app/time-reporting/components/time-reporting-list';
import { getDomainEmployees } from '@/server-actions/domain-employees';
import { getPermission } from '@/lib/employee';
import { headers } from 'next/headers';

export const maxDuration = 60;
export const dynamic = 'force-dynamic';
process.env.TZ = 'Europe/Zurich';

async function TimeReportingPage() {
  const permission = await getPermission('time-reporting');
  if (permission === 'inactive') {
    return <div>You are not allowed to access this page</div>;
  }

  let { data: employees, error } = await getDomainEmployees(true);

  if (error) {
    return <div>Error</div>;
  }

  if (permission === 'limited') {
    const headersList = headers();
    const name = headersList.get('x-user-name');
    if (!name) return <div>You are not allowed to access this page</div>;
    employees = employees?.filter((employee) => employee.name === name) || [];
  }

  return <TimeReportingList employees={employees || []} />;
}

export default TimeReportingPage;
