'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import {
  getEmployeeCapacitySettings,
  getAllEmployees,
  saveEmployeeCapacitySettings,
} from '@/server-actions/employee-capacity/get-employee-capacity-settings';
import useSWR from 'swr';

type EmployeeCapacitySetting = {
  id: string;
  employeeName: string;
  overcapacity_day_with_target_hours_h: number;
  overcapacity_day_without_target_hours_h: number;
  overcapacity_week_h: number;
  warning_day_active: boolean;
  warning_week_active: boolean;
  overcapacity_day_with_target_hours_and_pto_vacation_h: number;
  overcapacity_day_with_target_hours_and_pto_sick_h: number;
  overcapacity_day_with_target_hours_and_pto_holiday_h: number;
  overcapacity_day_with_target_hours_and_pto_freetime_h: number;
  overcapacity_day_with_target_hours_and_pto_compensation_h: number;
  overcapacity_day_with_target_hours_and_pto_training_h: number;
  overcapacity_day_with_target_hours_and_pto_military_cs_h: number;
  overcapacity_day_with_target_hours_and_pto_parental_leave_h: number;
  overcapacity_day_with_target_hours_and_pto_accident_h: number;
  overcapacity_day_with_target_hours_and_pto_other_h: number;
};

export function EmployeeCapacitySettings() {
  const { toast } = useToast();
  const [settings, setSettings] = useState<EmployeeCapacitySetting[]>([]);
  const [availableEmployees, setAvailableEmployees] = useState<{ user_id: string; name: string }[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [expandedCards, setExpandedCards] = useState<Record<string, boolean>>({});

  const { data: capacityData, mutate } = useSWR('employee-capacity-settings', getEmployeeCapacitySettings);
  const { data: employeesData } = useSWR('all-employees', getAllEmployees);

  useEffect(() => {
    if (capacityData?.data && employeesData?.data) {
      const formattedSettings = capacityData.data.map((setting) => ({
        id: setting.id,
        employeeName: setting.employees?.name || 'Unknown',
        overcapacity_day_with_target_hours_h: setting.overcapacity_day_with_target_hours_h,
        overcapacity_day_without_target_hours_h: setting.overcapacity_day_without_target_hours_h,
        overcapacity_week_h: setting.overcapacity_week_h,
        warning_day_active: setting.warning_day_active,
        warning_week_active: setting.warning_week_active,
        overcapacity_day_with_target_hours_and_pto_vacation_h:
          setting.overcapacity_day_with_target_hours_and_pto_vacation_h,
        overcapacity_day_with_target_hours_and_pto_sick_h: setting.overcapacity_day_with_target_hours_and_pto_sick_h,
        overcapacity_day_with_target_hours_and_pto_holiday_h:
          setting.overcapacity_day_with_target_hours_and_pto_holiday_h,
        overcapacity_day_with_target_hours_and_pto_freetime_h:
          setting.overcapacity_day_with_target_hours_and_pto_freetime_h,
        overcapacity_day_with_target_hours_and_pto_compensation_h:
          setting.overcapacity_day_with_target_hours_and_pto_compensation_h,
        overcapacity_day_with_target_hours_and_pto_training_h:
          setting.overcapacity_day_with_target_hours_and_pto_training_h,
        overcapacity_day_with_target_hours_and_pto_military_cs_h:
          setting.overcapacity_day_with_target_hours_and_pto_military_cs_h,
        overcapacity_day_with_target_hours_and_pto_parental_leave_h:
          setting.overcapacity_day_with_target_hours_and_pto_parental_leave_h,
        overcapacity_day_with_target_hours_and_pto_accident_h:
          setting.overcapacity_day_with_target_hours_and_pto_accident_h,
        overcapacity_day_with_target_hours_and_pto_other_h: setting.overcapacity_day_with_target_hours_and_pto_other_h,
      }));
      setSettings(formattedSettings);
      setAvailableEmployees(employeesData.data);

      // Set all cards to be expanded by default
      const initialExpandedState = formattedSettings.reduce(
        (acc, setting) => {
          acc[setting.id] = true;
          return acc;
        },
        {} as Record<string, boolean>,
      );
      setExpandedCards(initialExpandedState);

      setIsLoading(false);
    }
  }, [capacityData, employeesData]);

  const updateSetting = (index: number, field: keyof EmployeeCapacitySetting, value: any) => {
    const newSettings = [...settings];
    newSettings[index] = { ...newSettings[index], [field]: value };
    setSettings(newSettings);
  };

  const addEmployee = (employeeId: string) => {
    const employee = availableEmployees.find((emp) => emp.user_id === employeeId);
    if (!employee) return;

    const newSetting: EmployeeCapacitySetting = {
      id: employeeId,
      employeeName: employee.name,
      overcapacity_day_with_target_hours_h: 2,
      overcapacity_day_without_target_hours_h: 0,
      overcapacity_week_h: 8,
      warning_day_active: true,
      warning_week_active: true,
      overcapacity_day_with_target_hours_and_pto_vacation_h: 0,
      overcapacity_day_with_target_hours_and_pto_sick_h: 0,
      overcapacity_day_with_target_hours_and_pto_holiday_h: 0,
      overcapacity_day_with_target_hours_and_pto_freetime_h: 0,
      overcapacity_day_with_target_hours_and_pto_compensation_h: 0,
      overcapacity_day_with_target_hours_and_pto_training_h: 0,
      overcapacity_day_with_target_hours_and_pto_military_cs_h: 0,
      overcapacity_day_with_target_hours_and_pto_parental_leave_h: 0,
      overcapacity_day_with_target_hours_and_pto_accident_h: 0,
      overcapacity_day_with_target_hours_and_pto_other_h: 0,
    };
    setSettings([...settings, newSetting]);
    // Expand the newly added employee card by default
    setExpandedCards((prev) => ({ ...prev, [employeeId]: true }));
  };

  const removeSetting = (index: number) => {
    const newSettings = settings.filter((_, i) => i !== index);
    setSettings(newSettings);
  };

  const toggleCardExpansion = (employeeId: string) => {
    setExpandedCards((prev) => ({
      ...prev,
      [employeeId]: !prev[employeeId],
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const settingsToSave = settings.map((setting) => ({
        id: setting.id,
        overcapacity_day_with_target_hours_h: setting.overcapacity_day_with_target_hours_h,
        overcapacity_day_without_target_hours_h: setting.overcapacity_day_without_target_hours_h,
        overcapacity_week_h: setting.overcapacity_week_h,
        warning_day_active: setting.warning_day_active,
        warning_week_active: setting.warning_week_active,
        overcapacity_day_with_target_hours_and_pto_vacation_h:
          setting.overcapacity_day_with_target_hours_and_pto_vacation_h,
        overcapacity_day_with_target_hours_and_pto_sick_h: setting.overcapacity_day_with_target_hours_and_pto_sick_h,
        overcapacity_day_with_target_hours_and_pto_holiday_h:
          setting.overcapacity_day_with_target_hours_and_pto_holiday_h,
        overcapacity_day_with_target_hours_and_pto_freetime_h:
          setting.overcapacity_day_with_target_hours_and_pto_freetime_h,
        overcapacity_day_with_target_hours_and_pto_compensation_h:
          setting.overcapacity_day_with_target_hours_and_pto_compensation_h,
        overcapacity_day_with_target_hours_and_pto_training_h:
          setting.overcapacity_day_with_target_hours_and_pto_training_h,
        overcapacity_day_with_target_hours_and_pto_military_cs_h:
          setting.overcapacity_day_with_target_hours_and_pto_military_cs_h,
        overcapacity_day_with_target_hours_and_pto_parental_leave_h:
          setting.overcapacity_day_with_target_hours_and_pto_parental_leave_h,
        overcapacity_day_with_target_hours_and_pto_accident_h:
          setting.overcapacity_day_with_target_hours_and_pto_accident_h,
        overcapacity_day_with_target_hours_and_pto_other_h: setting.overcapacity_day_with_target_hours_and_pto_other_h,
      }));

      const result = await saveEmployeeCapacitySettings(settingsToSave);
      if (!result.error) {
        await mutate();
        toast({ title: 'Mitarbeiter-Kapazitätseinstellungen erfolgreich gespeichert' });
      } else {
        toast({ variant: 'destructive', title: 'Fehler beim Speichern der Einstellungen' });
        console.error('Error saving settings:', result.error);
      }
    } catch (error) {
      toast({ variant: 'destructive', title: 'Unerwarteter Fehler beim Speichern' });
      console.error('Unexpected error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getAvailableEmployeesForSelect = () => {
    const usedEmployeeIds = settings.map((s) => s.id);
    return availableEmployees.filter((emp) => !usedEmployeeIds.includes(emp.user_id));
  };

  if (isLoading) {
    return <div className="p-4">Lade Mitarbeiter-Kapazitätseinstellungen...</div>;
  }

  return (
    <div className="flex flex-col gap-6 py-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Mitarbeiter-Kapazitätseinstellungen</h2>
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? 'Speichern...' : 'Speichern'}
        </Button>
      </div>

      {/* Add new employee */}
      {getAvailableEmployeesForSelect().length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Mitarbeiter hinzufügen</CardTitle>
          </CardHeader>
          <CardContent>
            <Select onValueChange={addEmployee}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Mitarbeiter auswählen..." />
              </SelectTrigger>
              <SelectContent>
                {getAvailableEmployeesForSelect().map((employee) => (
                  <SelectItem key={employee.user_id} value={employee.user_id}>
                    {employee.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Employee settings list */}
      <div className="space-y-4">
        {settings.map((setting, index) => (
          <Card key={setting.id}>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 ${expandedCards[setting.id] ? 'pb-2' : 'pb-4'}`}
            >
              <div className="flex items-center gap-2 min-h-[2rem]">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleCardExpansion(setting.id)}
                  className="h-8 w-8 p-0 flex items-center justify-center"
                >
                  {expandedCards[setting.id] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
                <CardTitle className="text-base flex items-center">{setting.employeeName}</CardTitle>
              </div>
              <Button variant="outline" size="sm" onClick={() => removeSetting(index)} className="h-8 w-8 p-0">
                <Trash2 className="h-4 w-4" />
              </Button>
            </CardHeader>
            {expandedCards[setting.id] && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor={`day-target-${index}`}>Überkapazität Tag (mit Sollstunden) [h]</Label>
                    <Input
                      id={`day-target-${index}`}
                      type="number"
                      step="0.1"
                      value={setting.overcapacity_day_with_target_hours_h}
                      onChange={(e) =>
                        updateSetting(index, 'overcapacity_day_with_target_hours_h', parseFloat(e.target.value) || 0)
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor={`day-no-target-${index}`}>Überkapazität Tag (ohne Sollstunden) [h]</Label>
                    <Input
                      id={`day-no-target-${index}`}
                      type="number"
                      step="0.1"
                      value={setting.overcapacity_day_without_target_hours_h}
                      onChange={(e) =>
                        updateSetting(index, 'overcapacity_day_without_target_hours_h', parseFloat(e.target.value) || 0)
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor={`week-${index}`}>Überkapazität Woche [h]</Label>
                    <Input
                      id={`week-${index}`}
                      type="number"
                      step="0.1"
                      value={setting.overcapacity_week_h}
                      onChange={(e) => updateSetting(index, 'overcapacity_week_h', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`warning-day-${index}`}
                      checked={setting.warning_day_active}
                      onCheckedChange={(checked) => updateSetting(index, 'warning_day_active', checked)}
                    />
                    <Label htmlFor={`warning-day-${index}`}>Tageswarnung aktiv</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`warning-week-${index}`}
                      checked={setting.warning_week_active}
                      onCheckedChange={(checked) => updateSetting(index, 'warning_week_active', checked)}
                    />
                    <Label htmlFor={`warning-week-${index}`}>Wochenwarnung aktiv</Label>
                  </div>
                </div>

                {/* PTO Settings */}
                <div className="border-t pt-4">
                  <h4 className="text-sm font-medium mb-3">PTO-spezifische Überkapazitätseinstellungen [h]</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor={`pto-vacation-${index}`}>Ferien</Label>
                      <Input
                        id={`pto-vacation-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_vacation_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_vacation_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-sick-${index}`}>Krankheit</Label>
                      <Input
                        id={`pto-sick-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_sick_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_sick_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-holiday-${index}`}>Feiertag</Label>
                      <Input
                        id={`pto-holiday-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_holiday_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_holiday_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-freetime-${index}`}>Freizeit</Label>
                      <Input
                        id={`pto-freetime-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_freetime_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_freetime_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-compensation-${index}`}>Kompensation</Label>
                      <Input
                        id={`pto-compensation-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_compensation_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_compensation_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-training-${index}`}>Weiterbildung</Label>
                      <Input
                        id={`pto-training-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_training_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_training_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-military-${index}`}>Militär/ZS</Label>
                      <Input
                        id={`pto-military-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_military_cs_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_military_cs_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-parental-${index}`}>Mutterschaft/Vaterschaft</Label>
                      <Input
                        id={`pto-parental-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_parental_leave_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_parental_leave_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-accident-${index}`}>Unfall</Label>
                      <Input
                        id={`pto-accident-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_accident_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_accident_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor={`pto-other-${index}`}>Sonstiges</Label>
                      <Input
                        id={`pto-other-${index}`}
                        type="number"
                        step="0.1"
                        value={setting.overcapacity_day_with_target_hours_and_pto_other_h}
                        onChange={(e) =>
                          updateSetting(
                            index,
                            'overcapacity_day_with_target_hours_and_pto_other_h',
                            parseFloat(e.target.value) || 0,
                          )
                        }
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {settings.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          Keine Mitarbeiter-Kapazitätseinstellungen vorhanden. Fügen einen Mitarbeiter hinzu, um zu beginnen.
        </div>
      )}
    </div>
  );
}
