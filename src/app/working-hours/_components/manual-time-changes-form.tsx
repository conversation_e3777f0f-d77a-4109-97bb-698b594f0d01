'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { saveManualTimeChange } from '@/server-actions/time-reporting/manual-time-changes';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn, getISODateString } from '@/lib/utils';
import { CalendarIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { useToast } from '@/components/ui/use-toast';
import { useSWRConfig } from 'swr';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Tables } from '@/types/gen/database-table';

export type MTCFSubmission = {
  id?: number;
  user_id: string;
  date: string;
  hours: number;
  is_pto: boolean;
  note?: string;
};

type ManualTimeChangesFormProps = {
  employees: { name: string; user_id: string }[];
  defaultValues?: Tables<'time_corrections'>;
  submitCallback?: () => void;
};

export function ManualTimeChangesForm({ employees, defaultValues, submitCallback }: ManualTimeChangesFormProps) {
  const { toast } = useToast();
  const { mutate } = useSWRConfig();
  const [selectedEmployee, setSelectedEmployee] = useState<string>(defaultValues?.user_id || employees[0].user_id);
  const [date, setDate] = useState<Date>(defaultValues?.date ? new Date(defaultValues?.date) : new Date());
  const [hours, setHours] = useState<number | undefined>(defaultValues?.hours);
  const [isPTOTime, setIsPTOTime] = useState<boolean>(defaultValues?.is_pto || false);
  const [isOvertime, setIsOvertime] = useState<boolean>(defaultValues?.is_overtime || false);
  const [note, setNote] = useState<string>(defaultValues?.note || '');

  const handleSubmit = async (event: any) => {
    event.preventDefault();

    if (date.getMinutes() === 0) {
      date.setHours(date.getHours() - date.getTimezoneOffset() / 60);
      date.setMinutes(1);
    }

    if (isOvertime && hours && hours > 0) {
      toast({
        variant: 'destructive',
        title: 'Fehler: Stunden dürfen für Überzeit nicht negativ sein.',
      });
      return;
    }

    const submission = {
      id: defaultValues?.id,
      user_id: selectedEmployee,
      date: getISODateString(date),
      hours,
      is_pto: isPTOTime,
      is_overtime: isOvertime,
      note,
    } as MTCFSubmission;

    const { error } = await saveManualTimeChange(submission);
    if (!error) {
      await mutate(['time-corr', selectedEmployee]);
      toast({
        title: 'Eintrag gespeichert.',
      });
      if (submitCallback) {
        submitCallback();
      }
    } else {
      toast({
        variant: 'destructive',
        title: 'Fehler beim Speichern.',
      });
      console.log(error);
    }
  };

  const handleTogglePTO = () => {
    setIsPTOTime((prev) => !prev);
  };

  const handleToggleOvertime = () => {
    setIsOvertime((prev) => !prev);
  };

  const handleChangeNote = (event: any) => {
    setNote(event.currentTarget.value);
  };

  const handleChangeHours = (event: any) => {
    setHours(Number(event.currentTarget.value));
  };

  return (
    <form onSubmit={handleSubmit} className="flex gap-3 w-full">
      <div className="flex flex-col gap-3 max-w-[400px]">
        <Select name="userId" defaultValue={selectedEmployee} onValueChange={setSelectedEmployee}>
          <SelectTrigger>
            <SelectValue placeholder="Mitarbeiter Wählen" />
          </SelectTrigger>
          <SelectContent>
            {employees?.map((employee) => (
              <SelectItem key={employee.user_id} value={employee.user_id}>
                {employee.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn('w-[240px] justify-start text-left font-normal', !date && 'text-muted-foreground')}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, 'PPP') : <span>Datum auswählen</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start" side="bottom">
              {/* @ts-ignore */}
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
            </PopoverContent>
          </Popover>

          <Input
            type="number"
            step="0.001"
            max={isOvertime ? 0 : 10000}
            name="hours"
            placeholder="Stunden"
            defaultValue={hours}
            onChange={handleChangeHours}
          />
          <div className="flex flex-col">
            <div className="flex items-center gap-1">
              <Checkbox id="pto" name="pto" checked={isPTOTime} onClick={handleTogglePTO} />
              <label htmlFor="pto">PTO?</label>
            </div>
            <div className="flex items-center gap-1">
              <Checkbox id="pto" name="overtime" checked={isOvertime} onClick={handleToggleOvertime} />
              <label htmlFor="pto">OT?</label>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-3 flex-grow items-end">
        <Textarea placeholder="Notiz" value={note} onChange={handleChangeNote} />
        <Button type="submit" className="w-fit">
          Speichern
        </Button>
      </div>
    </form>
  );
}
