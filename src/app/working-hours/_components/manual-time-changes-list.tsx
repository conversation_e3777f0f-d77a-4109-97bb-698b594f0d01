'use client';

import { But<PERSON> } from '@/components/ui/button';
import { deleteManualTimeChange, getManualTimeChanges } from '@/server-actions/time-reporting/manual-time-changes';
import { format } from 'date-fns';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import useSWR from 'swr';
import { useToast } from '@/components/ui/use-toast';
import { ManualTimeChangesForm } from '@/app/working-hours/_components/manual-time-changes-form';

type ManualTimeChangesFormProps = {
  employees: { name: string; user_id: string }[];
};

export function ManualTimeChangesList({ employees }: ManualTimeChangesFormProps) {
  const { toast } = useToast();
  const [activeUserId, setActiveUserId] = useState<string>(employees[0].user_id);
  const [activeEditEntryId, setActiveEditEntryId] = useState<number | null>(null);
  const { data: manualTimeChanges, mutate } = useSWR(['time-corr', activeUserId], () =>
    getManualTimeChanges(activeUserId),
  );

  const data = manualTimeChanges?.data;

  const handleDelete = (entryId: number) => async (event: any) => {
    event.preventDefault();
    await deleteManualTimeChange(entryId);
    await mutate();
    toast({
      variant: 'destructive',
      title: 'Eintrag gelöscht.',
    });
  };

  const handleToggleEdit = (entryId: number) => async (event: any) => {
    event.preventDefault();
    setActiveEditEntryId(entryId);
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md w-[800px]">
      <div className="flex justify-between mb-4">
        <h2 className="text-lg font-semibold items-center">Korrigierte Einträge</h2>
        <Select name="userId" onValueChange={setActiveUserId}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Mitarbeiter Wählen" />
          </SelectTrigger>
          <SelectContent>
            {employees?.map((employee) => (
              <SelectItem key={employee.user_id} value={employee.user_id}>
                {employee.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <ScrollArea className="h-[350px] mt-4">
        {data?.map((entry) => (
          <div key={entry.id} className="flex justify-between items-center py-2 mr-2 border-b">
            {activeEditEntryId === entry.id ? (
              <ManualTimeChangesForm
                employees={employees}
                defaultValues={entry}
                submitCallback={() => setActiveEditEntryId(null)}
              />
            ) : (
              <div>
                <p>
                  {entry.employees?.name} {format(entry.date, 'dd.MM.yyyy')}
                </p>
                <p>
                  {entry.hours} Stunden{entry.is_pto ? ' | PTO' : ''}
                  {entry.is_overtime ? ' | Überzeit' : ''}
                </p>
                <p>{entry.note}</p>
              </div>
            )}

            {activeEditEntryId !== entry.id && (
              <div className="flex flex-col gap-2">
                <Button variant="destructive" onClick={handleDelete(entry.id)}>
                  Löschen
                </Button>
                <Button variant="outline" onClick={handleToggleEdit(entry.id)}>
                  Bearbeiten
                </Button>
              </div>
            )}
          </div>
        ))}
      </ScrollArea>
    </div>
  );
}
