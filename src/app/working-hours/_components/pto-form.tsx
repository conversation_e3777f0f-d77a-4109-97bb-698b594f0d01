'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { savePTO } from '@/server-actions/working-hours/save-pto';
import { useToast } from '@/components/ui/use-toast';
import { useSWRConfig } from 'swr';

export type PTOSubmission = {
  year: number;
  user_id: string;
  hours: number;
  days: number;
};

type PTOProps = {
  employees: { name: string; user_id: string }[];
};

export function PTOForm({ employees }: PTOProps) {
  const [selectedEmployee, setSelectedEmployee] = useState<string>(employees[0].user_id);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [hours, setHours] = useState<number>(0);
  const [days, setDays] = useState<number>(0);
  const { toast } = useToast();
  const { mutate } = useSWRConfig();

  // list of years from 2023 to current year and current to + 5 years
  const from = 2023;
  const to = new Date().getFullYear() + 5;
  const years = Array.from({ length: to - from + 1 }, (_, i) => from + i);

  const handleSubmit = async (event: any) => {
    event.preventDefault();

    const submission = {
      user_id: selectedEmployee,
      year: selectedYear,
      hours,
      days,
    } as PTOSubmission;

    const { error } = await savePTO(submission);
    if (!error) {
      await mutate('pto-credits');
      toast({
        title: 'Eintrag gespeichert.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Fehler beim Speichern.',
      });
      console.log(error);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md w-[400px]">
      <h2 className="text-lg font-semibold mb-4">Ferien Guthaben Eintragen</h2>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <Select name="userId" defaultValue={selectedEmployee} onValueChange={setSelectedEmployee}>
          <SelectTrigger>
            <SelectValue placeholder="Mitarbeiter Wählen" />
          </SelectTrigger>
          <SelectContent>
            {employees?.map((employee) => (
              <SelectItem key={employee.user_id} value={employee.user_id}>
                {employee.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select name="year" defaultValue={String(selectedYear)} onValueChange={(val) => setSelectedYear(Number(val))}>
          <SelectTrigger>
            <SelectValue placeholder="Jahr Wählen" />
          </SelectTrigger>
          <SelectContent>
            {years?.map((year) => (
              <SelectItem key={year} value={String(year)}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Input
          type="number"
          step="0.001"
          name="hours"
          placeholder="Stunden"
          onChange={(event) => setHours(Number(event.currentTarget.value))}
        />
        <Input
          type="number"
          step="0.001"
          name="days"
          placeholder="Tage"
          onChange={(event) => setDays(Number(event.currentTarget.value))}
        />
        <Button type="submit">Speichern</Button>
      </form>
    </div>
  );
}
