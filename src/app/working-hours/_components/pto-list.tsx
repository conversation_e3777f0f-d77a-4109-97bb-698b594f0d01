'use client';

import { useMemo, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getPTO } from '@/server-actions/working-hours/get-pto';
import useSWR from 'swr';

export function PTOList() {
  const { data: ptoCreditsData } = useSWR('pto-credits', getPTO);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const ptoCredits = ptoCreditsData?.data;

  const ptoData = useMemo(() => {
    return ptoCredits?.filter((pto) => pto.employees?.name && pto.year === selectedYear && pto.hours > 0);
  }, [selectedYear, ptoCredits]);

  const years = useMemo(() => {
    return (Array.from(new Set(ptoCredits?.map((pto) => pto.year))) || []).toSorted();
  }, [ptoCredits]);

  return (
    <div className="fbg-white p-4 rounded-lg shadow-md w-[400px] flex flex-col gap-2">
      <h2 className="text-lg font-semibold mb-2">Ferienguthaben</h2>
      <Select name="year" defaultValue={String(selectedYear)} onValueChange={(val) => setSelectedYear(Number(val))}>
        <SelectTrigger>
          <SelectValue placeholder="Jahr Wählen" />
        </SelectTrigger>
        <SelectContent>
          {years?.map((year) => (
            <SelectItem key={year} value={String(year)}>
              {year}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex flex-col gap-2 mt-2">
        {ptoData?.map((pto) => (
          <div key={pto.user_id} className="flex gap-2">
            <div>{pto.employees?.name}</div>
            <div>{(pto.hours / 8.6).toFixed(2)} Tage</div>
          </div>
        ))}
      </div>
    </div>
  );
}
