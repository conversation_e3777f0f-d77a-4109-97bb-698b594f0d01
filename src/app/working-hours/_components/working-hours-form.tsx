'use client';

import { useState } from 'react';
import { CalendarIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { saveWorkingHoursAction } from '@/server-actions/working-hours/save-working-hours';
import { useToast } from '@/components/ui/use-toast';
import { useSWRConfig } from 'swr';

export type WorkingHoursSubmission = {
  user_id: string;
  hours: string;
  weekdays: number[];
  start: Date;
  end: Date;
};

type WorkingHoursOverviewProps = {
  employees: { name: string; user_id: string }[];
};

export function WorkingHoursForm({ employees }: WorkingHoursOverviewProps) {
  const [date, setDate] = useState<DateRange | undefined>();
  const { toast } = useToast();
  const { mutate } = useSWRConfig();

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    if (!date || !date.from || !date.to) {
      console.error('No date selected');
      return;
    }

    const data = new FormData(event.target);
    const submission = {
      user_id: data.get('userId'),
      hours: data.get('hours'),
      weekdays: [
        data.get('mo'),
        data.get('tue'),
        data.get('wed'),
        data.get('thu'),
        data.get('fri'),
        data.get('sat'),
        data.get('sun'),
      ]
        .filter(Boolean)
        .map(Number),

      start: new Date(Date.UTC(date.from.getFullYear(), date.from.getMonth(), date.from.getDate(), 12, 0, 0)),
      end: new Date(Date.UTC(date.to.getFullYear(), date.to.getMonth(), date.to.getDate(), 20, 0, 0)),
    } as WorkingHoursSubmission;

    const { error } = await saveWorkingHoursAction(submission);
    if (!error) {
      await mutate(['working-hours', submission.user_id]);
      toast({
        title: 'Eintrag gespeichert.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Fehler beim Speichern.',
      });
      console.log(error);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md w-[400px]">
      <h2 className="text-lg font-semibold mb-4">Arbeitszeit Eintragen</h2>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <Select name="userId">
          <SelectTrigger>
            <SelectValue placeholder="Mitarbeiter Wählen" />
          </SelectTrigger>
          <SelectContent>
            {employees?.map((employee) => (
              <SelectItem key={employee.user_id} value={employee.user_id}>
                {employee.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={'outline'}
              className={cn('justify-start text-left font-normal', !date && 'text-muted-foreground')}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, 'LLL dd, y')} - {format(date.to, 'LLL dd, y')}
                  </>
                ) : (
                  format(date.from, 'LLL dd, y')
                )
              ) : (
                <span>Zeitspanne Wählen</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={setDate}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
        <div className="flex justify-center items-center space-x-2">
          <Checkbox id="mo" name="mo" value={1} />
          <label htmlFor="mo">Mo</label>
          <Checkbox id="tue" name="tue" value={2} />
          <label htmlFor="tue">Di</label>
          <Checkbox id="wed" name="wed" value={3} />
          <label htmlFor="wed">Mi</label>
          <Checkbox id="thu" name="thu" value={4} />
          <label htmlFor="thu">Do</label>
          <Checkbox id="fri" name="fri" value={5} />
          <label htmlFor="fri">Fr</label>
          <Checkbox id="sat" name="sat" value={6} />
          <label htmlFor="sat">Sa</label>
          <Checkbox id="sun" name="sun" value={0} />
          <label htmlFor="sun">So</label>
        </div>
        <Input type="number" step="0.001" name="hours" placeholder="Stunden" />
        <Button type="submit">Speichern</Button>
      </form>
    </div>
  );
}
