'use client';

import useS<PERSON> from 'swr';
import { getWorkingHours } from '@/server-actions/working-hours/get-working-hours';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { ScrollArea } from '@/components/ui/scroll-area';

type WorkingHoursListProps = {
  userId: string;
};

export function WorkingHoursList({ userId }: WorkingHoursListProps) {
  const { isLoading, data, error } = useSWR(['working-hours', userId], () => getWorkingHours(userId));

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error || !(data instanceof Map)) {
    return <div>Error</div>;
  }

  const years = Array.from(new Set(Array.from(data.keys()).map((val) => val.substring(0, 4)))).sort();

  return (
    <ScrollArea className="h-[250px]">
      {years.map((year) => (
        <Accordion key={year} type="single" collapsible className="w-full">
          <AccordionItem value={year}>
            <AccordionTrigger className="mr-2">{year}</AccordionTrigger>
            <AccordionContent>
              <Accordion type="single" collapsible className="w-full">
                {Array.from(data.keys())
                  .sort((a, b) => Number(a.split('-')[1]) - Number(b.split('-')[1]))
                  .filter((key) => key.startsWith(year))
                  .map((key) => {
                    const value = data.get(key);
                    const sortedValue = value?.sort((a, b) => String(a.date).localeCompare(String(b.date)));
                    return (
                      <AccordionItem value={key} key={key}>
                        <AccordionTrigger className="mr-2">{key}</AccordionTrigger>
                        <AccordionContent>
                          {sortedValue
                            ?.filter((wh) => wh.hours)
                            .map((wh) => (
                              <div key={wh.date}>
                                {new Date(wh.date).toLocaleDateString('de', {
                                  weekday: 'short',
                                  day: 'numeric',
                                  month: 'numeric',
                                })}{' '}
                                {wh.hours} Stunden
                              </div>
                            ))}
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
              </Accordion>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      ))}
    </ScrollArea>
  );
}
