'use client';

import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { WorkingHoursList } from '@/app/working-hours/_components/working-hours-list';

type WorkingHoursOverviewProps = {
  employees: { name: string; user_id: string }[];
};
export function WorkingHoursOverview({ employees }: WorkingHoursOverviewProps) {
  const [activeUserId, setActiveUserId] = useState<string>(employees[0].user_id);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md w-[400px]">
      <h2 className="text-lg font-semibold mb-4">Arbeitszeiten</h2>
      <form className="flex flex-col gap-4">
        <Select name="userId" onValueChange={setActiveUserId}>
          <SelectTrigger>
            <SelectValue placeholder="Mitarbeiter Wählen" />
          </SelectTrigger>
          <SelectContent>
            {employees?.map((employee) => (
              <SelectItem key={employee.user_id} value={employee.user_id}>
                {employee.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <WorkingHoursList userId={activeUserId} />
      </form>
    </div>
  );
}
