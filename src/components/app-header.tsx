'use client';

import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import React from 'react';
import { useAuth } from '@/context/auth-context';
import { AppSidebarTrigger } from '@/components/app-sidebar-trigger';

export function Header() {
  const { access, image, name, email } = useAuth();

  return (
    <header className="flex h-16 items-center gap-4 border-b bg-background px-4 w-full">
      <AppSidebarTrigger />
      <div className="flex flex-1 items-center gap-4">
        <form className="flex-1 ">
          <div className="relative hidden">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full appearance-none bg-background pl-8 md:w-2/3 lg:w-1/3"
            />
          </div>
        </form>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar>
                <AvatarImage src={image || 'https://github.com/shadcn.png'} />
                <AvatarFallback>
                  {name?.split(' ')[0][0]}
                  {name?.split(' ')[1][0]}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              <p>{name}</p>
              <p className="font-medium">{email}</p>
            </DropdownMenuLabel>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
