import { IconLayoutSidebar } from '@tabler/icons-react';
import * as React from 'react';
import { Button } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';

export function AppSidebarTrigger() {
  const { toggleSidebar } = useSidebar();

  return (
    <Button data-sidebar="trigger" variant="ghost" size="icon" className="h-6 w-6" onClick={toggleSidebar}>
      <IconLayoutSidebar className="!h-5 !w-5" />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  );
}
