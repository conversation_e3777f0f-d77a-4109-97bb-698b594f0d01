'use client';

import React, { useState } from 'react';
import {
  ChevronDown,
  LayoutDashboard,
  Users,
  FileText,
  Briefcase,
  Calendar,
  Clock,
  Settings,
  ChevronRight,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarRail,
  SidebarHeader,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import Image from 'next/image';

const navItems = [
  {
    title: 'CRM',
    icon: Users,
    items: [
      { title: 'Dashboard', href: '/crm', icon: LayoutDashboard },
      { title: 'Clients', href: '/crm/clients', icon: Users },
      { title: 'Reports', href: '/crm/reports', icon: FileText },
    ],
  },
  {
    title: 'Services',
    icon: Briefcase,
    items: [
      { title: 'Dashboard', href: '/services', icon: LayoutDashboard },
      { title: 'Projects', href: '/services/projects', icon: Briefcase },
      { title: 'Subscriptions', href: '/services/subscriptions', icon: Calendar },
      { title: 'Reports', href: '/services/reports', icon: FileText },
      { title: 'Settings', href: '/services/settings', icon: Settings },
    ],
  },
  {
    title: 'HR',
    icon: Users,
    items: [
      { title: 'Employees', href: '/hr/employees', icon: Users },
      { title: 'Timesheets', href: '/hr/timesheets', icon: Clock },
      { title: 'Payroll', href: '/hr/payroll', icon: FileText },
      { title: 'Working Hours', href: '/hr/working-hours', icon: Clock },
      { title: 'PTO', href: '/hr/pto', icon: Calendar },
      { title: 'Settings', href: '/hr/settings', icon: Settings },
    ],
  },
  {
    title: 'Global Settings',
    icon: Settings,
    href: '/settings',
  },
];

export function AppSidebar() {
  const pathname = usePathname();
  const [openSections, setOpenSections] = useState<string[]>([]);

  const handleToggleSection = (title: string) => () => {
    setOpenSections((prev) => (prev.includes(title) ? prev.filter((section) => section !== title) : [...prev, title]));
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex h-[60px] items-center px-2">
          <Link className="flex gap-2 font-semibold text-gray-900" href="/public">
            <Image src="/signet.png" alt="Lumeos Signet" width={13} height={10} />
            <div className="flex gap-1.5 items-baseline pt-1 -mb-1">
              <span className="font-bold text-xl tracking-tighter">LUMEOS</span>
              <span className="">Internal</span>
            </div>
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2">
        {navItems.map((section) => (
          <SidebarMenu key={section.title}>
            {section.items ? (
              <Collapsible defaultOpen className="group/collapsible">
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild onClick={handleToggleSection(section.title)}>
                    <SidebarMenuButton className="flex justify-between">
                      <span className="flex items-center gap-2">
                        <section.icon className="h-4 w-4" />
                        {section.title}
                      </span>
                      {openSections.includes(section.title) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {section.items.map((item) => (
                        <SidebarMenuButton key={item.href} asChild isActive={pathname === item.href}>
                          <Link href={item.href} className="flex items-center gap-2">
                            <item.icon className="h-4 w-4" />
                            {item.title}
                          </Link>
                        </SidebarMenuButton>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ) : (
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild isActive={pathname === section.href}>
                      <Link href={section.href} className="flex items-center gap-2">
                        <section.icon className="h-4 w-4" />
                        {section.title}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            )}
          </SidebarMenu>
        ))}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
