'use client';

import React, { HTMLAttributes, useMemo } from 'react';
import Link from 'next/link';
import {
  IconCalendarTime,
  IconClipboardCopy,
  IconDashboard,
  IconGauge,
  IconHome,
  IconPackage,
  IconReport,
  IconReportAnalytics,
  IconSettings,
  IconUsers,
  IconUserSquare,
} from '@tabler/icons-react';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { useAuth } from '@/context/auth-context';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export const menuItems = [
  {
    name: 'Home',
    path: '/',
    icon: IconHome,
  },
  {
    name: 'Kunden',
    path: '/clients',
    icon: IconUsers,
  },
  {
    name: 'Kunden Reporting',
    path: '/client-reports',
    icon: IconClipboardCopy,
  },
  {
    name: 'Projekt<PERSON>',
    path: '/projects',
    icon: IconPackage,
  },
  {
    name: 'Projekt Reporting',
    path: '/project-reports',
    icon: IconGauge,
  },
  {
    name: 'Zeit Einträge',
    path: '/time-entries',
    icon: IconReport,
  },
  {
    name: 'Time Reporting',
    path: '/time-reporting',
    icon: IconReportAnalytics,
  },
  {
    name: 'Time Dashboard',
    path: '/time-dashboard',
    icon: IconDashboard,
  },
  {
    name: 'Mitarbeiter',
    path: '/employees',
    icon: IconUserSquare,
  },
  {
    name: 'Arbeitszeiten',
    path: '/working-hours',
    icon: IconCalendarTime,
  },
  {
    name: 'Settings',
    path: '/settings',
    icon: IconSettings,
  },
];

export function CustomSidebar({ className }: HTMLAttributes<HTMLDivElement>) {
  const pathname = usePathname();
  const currentRoute = pathname.includes('employees') ? '/employees' : pathname;
  const { access, image, name } = useAuth();

  const visiblePaths = useMemo(() => {
    if (!access) return [];
    return menuItems
      .filter((item) => access.some((access) => access.option != 'inactive' && item.path == '/' + access.pageKey))
      .map((item) => item.path);
  }, [access]);

  const visibleMenuItems = menuItems.filter((item) => visiblePaths.includes(item.path));

  return (
    <>
      <div className="hidden border-r bg-blue-100/40 lg:block dark:bg-blue-800/40">
        <div className="flex flex-col gap-2 h-[100%]">
          <div className="flex h-[60px] items-center px-6">
            <Link className="flex items-center gap-2 font-semibold text-gray-900" href="/public">
              <Image src="/signet.png" alt="Lumeos Signet" width={15} height={20} />
              <span className="">Lumeos Dashboard</span>
            </Link>
          </div>
          <div className="flex flex-1 flex-col justify-between">
            <nav className="grid items-start px-4 text-sm font-medium gap-1">
              {visibleMenuItems.map((item) => (
                <Link
                  key={item.path}
                  className={cn(
                    'flex items-center gap-3 rounded-lg hover:bg-blue-100 px-3 py-2 text-gray-900 transition-all',
                    currentRoute == item.path ? 'bg-blue-100' : '',
                  )}
                  href={item.path}
                >
                  <item.icon className="h-5 w-5 text-gray-900" stroke="1.7" />
                  {item.name}
                </Link>
              ))}
            </nav>
            <div className="px-3 py-2">
              <div className="flex gap-2 items-center">
                <Avatar>
                  <AvatarImage src={image || 'https://github.com/shadcn.png'} />
                  <AvatarFallback>
                    {name?.split(' ')[0][0]}
                    {name?.split(' ')[1][0]}
                  </AvatarFallback>
                </Avatar>
                {name}
              </div>
              {/*<p>{process.env.APP_VERSION}</p>*/}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
