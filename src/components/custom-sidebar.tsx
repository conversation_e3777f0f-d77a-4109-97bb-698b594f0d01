'use client';

import React, { useMemo, useState } from 'react';
import { ChevronDown, Users, Briefcase, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarRail,
  SidebarHeader,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import Image from 'next/image';
import {
  IconCalendarTime,
  IconClipboardCopy,
  IconDashboard,
  IconGauge,
  IconPackage,
  IconReport,
  IconReportAnalytics,
  IconSettings,
  IconUsers,
  IconUserSquare,
} from '@tabler/icons-react';
import { useAuth } from '@/context/auth-context';

const navItems = [
  {
    title: 'CRM',
    icon: Users,
    items: [
      { title: 'Kunden', href: '/clients', icon: IconUsers },
      { title: 'Berichte', href: '/client-reports', icon: IconClipboardCopy },
    ],
  },
  {
    title: 'Projekte',
    icon: Briefcase,
    items: [
      { title: 'Projekte', href: '/projects', icon: IconPackage },
      { title: 'Berichte', href: '/project-reports', icon: IconGauge },
      { title: 'Zeit Einträge', href: '/time-entries', icon: IconReport },
      { title: 'Zeit Dashboard', href: '/time-dashboard', icon: IconDashboard },
    ],
  },
  {
    title: 'HR',
    icon: Users,
    items: [
      { title: 'Employees', href: '/employees', icon: IconUserSquare },
      { title: 'Time Reporting', href: '/time-reporting', icon: IconReportAnalytics },
      { title: 'Arbeitszeiten', href: '/working-hours', icon: IconCalendarTime },
      { title: 'Recurring Tasks', href: '/recurring-tasks', icon: IconClipboardCopy },
    ],
  },
  {
    title: 'Global Settings',
    icon: IconSettings,
    href: '/settings',
  },
];

export function CustomSidebar() {
  const pathname = usePathname();
  const currentRoute = pathname.includes('employees') ? '/employees' : pathname;
  const { access } = useAuth();
  const [openSections, setOpenSections] = useState<string[]>([]);

  const handleToggleSection = (title: string) => () => {
    setOpenSections((prev) => (prev.includes(title) ? prev.filter((section) => section !== title) : [...prev, title]));
  };

  const visiblePaths = useMemo(() => {
    if (!access) return [];
    return navItems
      .flatMap((item) => {
        if (!item.items) return item.href;
        return item.items?.map((subitem) => subitem.href).concat(item.href || []) || item.href || [];
      })
      .filter((path) => access.some((access) => access.option != 'inactive' && path == '/' + access.pageKey));
  }, [access]);

  const visibleMenuItems = navItems
    .map((group) => {
      const items = group.items?.filter((item) => visiblePaths.includes(item.href));
      const currVisible = items?.length || (group.href && visiblePaths.includes(group.href));
      if (!currVisible) {
        return null;
      }
      return { ...group, items };
    })
    .filter((group) => !!group)
    .map((group) => group!);

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex h-[60px] items-center px-2">
          <Link className="flex gap-2 font-semibold text-gray-900" href="/public">
            <div className="flex gap-1.5 items-baseline">
              <Image src="/LUMEOS_Logo_Standard_Black_web.png" alt="Lumeos Schriftzug" width={100} height={30} />
              <span className="">Internal</span>
            </div>
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2">
        {visibleMenuItems.map((section) => (
          <SidebarMenu key={section.title}>
            {section.items ? (
              <Collapsible defaultOpen className="group/collapsible">
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild onClick={handleToggleSection(section.title)}>
                    <SidebarMenuButton className="flex justify-between">
                      <span className="flex items-center gap-2">
                        <section.icon className="h-4 w-4" />
                        {section.title}
                      </span>
                      {openSections.includes(section.title) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {section.items.map((item) => (
                        <SidebarMenuButton key={item.href} asChild isActive={currentRoute == item.href}>
                          <Link href={item.href} className="flex items-center gap-2">
                            <item.icon className="h-4 w-4" />
                            {item.title}
                          </Link>
                        </SidebarMenuButton>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ) : (
              section.href && (
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild isActive={pathname === section.href}>
                        <Link href={section.href} className="flex items-center gap-2">
                          <section.icon className="h-4 w-4" />
                          {section.title}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              )
            )}
          </SidebarMenu>
        ))}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
