import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/datepicker';
import { useState } from 'react';

type CustomTimeRangeProps = {
  start: Date;
  end: Date;
  onChange: (start: Date, end: Date, range?: string) => void;
  earliestDate?: Date;
  defaultValue?: string;
};

export function CustomTimeRange({
  start,
  end,
  onChange,
  earliestDate = new Date('2023-01-01'),
  defaultValue,
}: CustomTimeRangeProps) {
  const [selectedValue, setSelectedValue] = useState<string>(defaultValue || 'from-start');

  const handleSelect = (value: string) => {
    setSelectedValue(value);

    switch (value) {
      case 'from-start':
        onChange(earliestDate, new Date(), value);
        break;
      case 'from-start-yesterday':
        const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
        onChange(earliestDate, new Date(yesterday), value);
        break;
      case 'last-week':
        const mondayLastWeek = new Date(new Date().setDate(new Date().getDate() - new Date().getDay() - 6)).setHours(
          0,
          0,
          0,
          0,
        );
        const sundayLastWeek = new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).setHours(
          23,
          59,
          59,
          999,
        );
        onChange(new Date(mondayLastWeek), new Date(sundayLastWeek), value);
        break;
      case 'this-week':
        const mondayThisWeek = new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 1)).setHours(
          0,
          0,
          0,
          0,
        );
        const thisWeekYesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
        onChange(new Date(mondayThisWeek), new Date(thisWeekYesterday), value);
        break;
      case 'last-month':
        const firstDayLastMonth = new Date(new Date(new Date().setDate(1) - 24 * 60 * 60 * 1000).setDate(1)).setHours(
          0,
          0,
          0,
          0,
        );
        const lastDayLastMonth = new Date(new Date().setDate(0)).setHours(23, 59, 59, 999);
        onChange(new Date(firstDayLastMonth), new Date(lastDayLastMonth), value);
        break;
      case 'this-month':
        const firstDayThisMonth = new Date(new Date().setDate(1)).setHours(0, 0, 0, 0);
        const yesterdayThisMonth = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
        onChange(new Date(firstDayThisMonth), new Date(yesterdayThisMonth), value);
        break;
      case 'last-year':
        const firstDayLastYear = new Date(new Date().getFullYear() - 1, 0, 1);
        const lastDayLastYear = new Date(new Date().getFullYear() - 1, 11, 31, 23, 59, 59, 999);
        onChange(firstDayLastYear, lastDayLastYear, value);
        break;
      case 'this-year':
        const firstDayThisYear = new Date(new Date().getFullYear(), 0, 1);
        const yesterdayThisYear = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
        onChange(firstDayThisYear, new Date(yesterdayThisYear), value);
        break;
      case 'custom':
        onChange(new Date(`${new Date().getFullYear()}-01-01`), new Date(), value);
        break;
    }
  };

  const handleChangeStart = (newStart: Date) => {
    const start = new Date(newStart.setHours(0, 0, 0, 0));
    onChange(start, end);
  };

  const handleChangeEnd = (newEnd: Date) => {
    const end = new Date(newEnd.setHours(23, 59, 59, 999));
    onChange(start, end);
  };

  return (
    <div className="flex gap-2 items-center">
      <Select onValueChange={handleSelect} value={selectedValue}>
        <SelectTrigger className="w-[220px]">
          <SelectValue placeholder="Wähle eine Zeitspanne" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="from-start">Anfang bis heute</SelectItem>
            <SelectItem value="from-start-yesterday">Anfang bis gestern</SelectItem>
            <SelectItem value="last-week">Letzte Woche</SelectItem>
            <SelectItem value="this-week">Diese Woche (bis gestern)</SelectItem>
            <SelectItem value="last-month">Letzter Monat</SelectItem>
            <SelectItem value="this-month">Dieser Monat (bis gestern)</SelectItem>
            <SelectItem value="last-year">Letztes Jahr</SelectItem>
            <SelectItem value="this-year">Dieses Jahr (bis gestern)</SelectItem>
            <SelectItem value="custom">Individuell</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
      {selectedValue === 'custom' && (
        <>
          <DatePicker label="Startdatum" value={start} onChange={handleChangeStart} />
          <DatePicker label="Enddatum" value={end} onChange={handleChangeEnd} />
        </>
      )}
    </div>
  );
}
