'use client';

import { SidebarProvider } from '@/components/ui/sidebar';
import { TriggerProvider } from '@trigger.dev/react';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <TriggerProvider
      publicApiKey={process.env.NEXT_PUBLIC_TRIGGER_PUBLIC_API_KEY!}
      apiUrl={process.env.NEXT_PUBLIC_TRIGGER_API_URL}
    >
      <SidebarProvider>{children}</SidebarProvider>
    </TriggerProvider>
  );
}
