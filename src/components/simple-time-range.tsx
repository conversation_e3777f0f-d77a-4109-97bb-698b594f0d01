import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CalendarIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';

type SimpleTimeRangeProps = {
  defaultStart?: Date;
  defaultEnd?: Date;
  onChange: (start: Date, end: Date) => void;
};

export function SimpleTimeRange({ defaultStart, defaultEnd, onChange }: SimpleTimeRangeProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(defaultStart || `${new Date().getFullYear()}-01-01`),
    to: new Date(defaultEnd || `${new Date().getFullYear()}-12-31`),
  });

  const handleChange = (newDateRange: DateRange | undefined) => {
    if (newDateRange?.from && newDateRange?.to) {
      onChange(newDateRange.from, newDateRange.to);
    }
    setDateRange(newDateRange);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          id="date"
          variant={'outline'}
          className={cn('justify-start text-left font-normal', !dateRange && 'text-muted-foreground')}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {dateRange?.from ? (
            dateRange.to ? (
              <>
                {format(dateRange.from, 'LLL dd, y')} - {format(dateRange.to, 'LLL dd, y')}
              </>
            ) : (
              format(dateRange.from, 'LLL dd, y')
            )
          ) : (
            <span>Zeitspanne Wählen</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={dateRange?.from}
          selected={dateRange}
          onSelect={handleChange}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  );
}
