import { Tooltip, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function SimpleTooltip({ content, children }: { content: string; children: React.ReactNode }) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>{children}</TooltipTrigger>
        <TooltipContent>
          <p>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
