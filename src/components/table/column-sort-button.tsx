import { <PERSON>Down, <PERSON>U<PERSON>, <PERSON>UpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Column } from '@tanstack/table-core';

type ColumnSortButtonProps = {
  column: Column<any, unknown>;
};

export function ColumnSortButton({ column }: ColumnSortButtonProps) {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      className="ml-2 py-1 px-2"
    >
      {!column.getIsSorted() && <ArrowUpDown className="h-4 w-4" />}
      {column.getIsSorted() === 'asc' && <ArrowUp className="h-4 w-4" />}
      {column.getIsSorted() === 'desc' && <ArrowDown className="h-4 w-4" />}
    </Button>
  );
}
