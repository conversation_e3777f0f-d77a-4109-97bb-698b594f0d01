'use client';

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';

import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useState } from 'react';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { getMinMaxValues } from '@/components/table/minmax';
import { IconColumns, IconDownload } from '@tabler/icons-react';
import { Multiselect } from '@/components/ui/multiselect';
import { useColumnVisibility } from '@/components/table/use-column-visibility';
import { useDownloadTable } from '@/components/table/use-download-table';
import { Button } from '@/components/ui/button';

interface DataTableProps<TData, TValue> {
  title?: string;
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  download?: boolean;
}

export function DataTable<TData, TValue>({ title, columns, data, download = true }: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    enableGlobalFilter: true,
    state: {
      sorting,
      columnFilters,
      getMinMaxValues,
    },
  });

  const { columnOptions, selectedColumns, handleColumnVisibilityChange } = useColumnVisibility(table);
  const { downloadExcel } = useDownloadTable({ table, name: title || 'Report' });

  return (
    <div className="flex flex-col gap-4 items-center max-w-full">
      <div className="flex gap-4 items-center justify-between w-full">
        <h2 className="ml-1">{title}</h2>
        <div className="flex gap-2 items-baseline">
          {download && (
            <Button variant="outline" onClick={downloadExcel}>
              Export <IconDownload className="ml-2 h-4 w-4" />
            </Button>
          )}
          <Multiselect
            label="Spalten"
            options={columnOptions}
            values={selectedColumns}
            onChange={handleColumnVisibilityChange}
            icon={<IconColumns className="h-4 w-4" />}
          />
        </div>
      </div>
      <ScrollArea className="flex flex-col gap-4 max-h-[80vh] pr-[8px] pb-[8px] w-full">
        <Table>
          <TableHeader className="sticky top-0 bg-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className={cn('whitespace-nowrap')}>
                      <div className="flex">
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cn(
                        'whitespace-nowrap',
                        (cell.column.id == 'client' || cell.column.id == 'name') && 'w-[300px] whitespace-break-spaces',
                      )}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-12 text-center">
                  Keine Daten vorhanden.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          <TableFooter>
            {table.getFooterGroups().map((footerGroup) => (
              <TableRow key={footerGroup.id}>
                {footerGroup.headers.map((header) => (
                  <TableCell key={header.id} className="whitespace-nowrap">
                    {flexRender(header.column.columnDef.footer, header.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableFooter>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
