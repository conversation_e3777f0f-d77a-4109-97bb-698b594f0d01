import { Row } from '@tanstack/react-table';

export const calculateSum = (rows: Row<any>[], id: string) => {
  return rows.reduce((sum, row) => {
    const isNotValid = isNaN(Number(row.getValue(id))) || !isFinite(Number(row.getValue(id)));
    return sum + (isNotValid ? 0 : Number(row.getValue(id)));
  }, 0);
};

export const calculateAverage = (rows: Row<any>[], id: string) => {
  const [sum, count] = rows.reduce(
    (sum, row) => {
      const isNotValid = isNaN(Number(row.getValue(id))) || !isFinite(Number(row.getValue(id)));
      return [sum[0] + (isNotValid ? 0 : Number(row.getValue(id))), isNotValid ? sum[1] : sum[1] + 1];
    },
    [0, 0],
  );
  return sum / count;
};

export const textFilter = (row: Row<any>, id: string, value: any) => {
  return value?.text ? String(row.getValue(id)).toLowerCase().includes(String(value?.text).toLowerCase()) : true;
};

export const multiSelectFilter = (row: Row<any>, id: string, value: any) => {
  return value?.select ? value?.select?.includes(row.getValue(id)) : true;
};

export const numericalSort = (rowA: Row<any>, rowB: Row<any>, id: string) => {
  const valueA = rowA.getValue(id);
  const valueB = rowB.getValue(id);
  const isANotValid = isNaN(Number(rowA.renderValue(id))) || !isFinite(Number(rowA.renderValue(id)));
  const isBNotValid = isNaN(Number(rowB.renderValue(id))) || !isFinite(Number(rowB.renderValue(id)));
  if (valueA === valueB || Number(valueA) === Number(valueB)) {
    return 0;
  } else if (!isANotValid && isBNotValid) {
    return 1;
  } else if (isANotValid && !isBNotValid) {
    return -1;
  }
  return Number(valueA) > Number(valueB) ? 1 : -1;
};

export const dateSort = (rowA: Row<any>, rowB: Row<any>, id: string) => {
  const valueA = rowA.getValue(id);
  const valueB = rowB.getValue(id);

  if (!valueA && !valueB) return 0;
  if (!valueA) return 1;
  if (!valueB) return -1;

  const dateA = new Date(String(valueA));
  const dateB = new Date(String(valueB));
  return dateA.getTime() - dateB.getTime();
};

export const multiSelectAndTextFilter = (row: Row<any>, id: string, value: any) => {
  const isSelected = value?.select ? value?.select?.includes(row.getValue(id)) : true;
  const isTextSelected = value?.text
    ? String(row.getValue(id)).toLowerCase().includes(String(value?.text).toLowerCase())
    : true;
  return isSelected && isTextSelected;
};

export const rangeFilter = (row: Row<any>, id: string, value: any) => {
  const range = row.getValue(id);
  return Number(range) >= value[0] && Number(range) <= value[1];
};
