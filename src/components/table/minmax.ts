import { Row } from '@tanstack/table-core';

export interface MinMaxTableState {
  getMinMaxValues: typeof getMinMaxValues;
}

export function getMinMaxValues<TData extends object>(data: Row<TData>[]) {
  return (columnId: string) => {
    const min = data.reduce((acc: number, row) => {
      const value = row.getValue(columnId);
      if (isNaN(Number(value)) || !isFinite(Number(value))) {
        return acc;
      }

      if (Number(value) < acc) {
        return Number(value);
      }
      return acc;
    }, 0);

    const max = data.reduce((acc: number, row) => {
      const value = row.getValue(columnId);
      if (isNaN(Number(value)) || !isFinite(Number(value))) {
        return acc;
      }

      if (Number(value) > acc) {
        return Number(value);
      }
      return acc;
    }, 0);

    return [min, max];
  };
}
