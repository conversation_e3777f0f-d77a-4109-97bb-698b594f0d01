import { FilterIcon } from 'lucide-react';
import { CheckIcon } from '@radix-ui/react-icons';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import useDebounce from '@/hooks/use-debounce';
import { Column } from '@tanstack/table-core';

type MultiselectProps = {
  column: Column<any>;
};

export const Multiselect = ({ column }: MultiselectProps) => {
  const options = Array.from(column.getFacetedUniqueValues().keys()).sort();
  const onChange = (selectedOptions: any[]) =>
    column.setFilterValue((prev: any) => {
      return { ...prev, select: selectedOptions };
    });

  const [open, setOpen] = useState<boolean>(false);
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const debouncedValue = useDebounce(selectedValues, 200);

  useEffect(() => {
    if (debouncedValue.length == 0) {
      // @ts-ignore
      onChange(undefined);
    } else {
      onChange(debouncedValue);
    }
  }, [debouncedValue]);

  const toggleValue = (value: string) => {
    if (selectedValues.includes(value)) {
      setSelectedValues(selectedValues.filter((v) => v !== value));
    } else {
      setSelectedValues([...selectedValues, value]);
    }
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" role="combobox" className="py-1 px-2 relative">
            <FilterIcon className="h-4 w-4" />
            {selectedValues && selectedValues.length ? (
              <div className="size-[5px] rounded-full bg-black absolute bottom-[10px] right-[6px]"></div>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0">
          <Command>
            <CommandInput placeholder="Suche..." />
            <CommandList>
              <CommandEmpty>Keine Einträge gefunden.</CommandEmpty>
              {options &&
                Array.isArray(options) &&
                options?.map((option, idx) => (
                  <CommandItem key={idx} value={option} onSelect={toggleValue}>
                    <CheckIcon
                      className={cn('mr-2 h-4 w-4', selectedValues?.includes(option) ? 'opacity-100' : 'opacity-0')}
                    />
                    {option == '' ? 'Leer' : option}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
};
