import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { FilterIcon } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Column, Table } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';

type SliderFilterProps<TData> = {
  column: Column<TData, unknown>;
  table: Table<TData>;
  type?: 'CHF' | '%' | 'h';
};

export function SliderFilter<TData>({ column, table, type = 'CHF' }: SliderFilterProps<TData>) {
  const [minRaw, setMinRaw] = useState<number>(0);
  const [maxRaw, setMaxRaw] = useState<number>(0);

  useEffect(() => {
    if (table && table.getState) {
      // @ts-ignore
      const [min, max] = table.getState().getMinMaxValues(table.getCoreRowModel().flatRows)(column.id);
      setMinRaw(min);
      setMaxRaw(max);
    }
  }, [table, column.id]);

  const min = isNaN(Number(minRaw)) ? 0 : Number(minRaw.toFixed(2));
  const max = isNaN(Number(maxRaw)) ? 100 : Number(maxRaw.toFixed(2));

  // @ts-ignore
  const isFiltered: boolean =
    // @ts-ignores
    column.getFilterValue() && (column.getFilterValue()[0] !== min || column.getFilterValue()[1] !== max);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="py-1 px-2 relative">
          <FilterIcon className="h-4 w-4" />
          {isFiltered && <div className="size-[5px] rounded-full bg-black absolute bottom-[10px] right-[6px]"></div>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="rounded-lg border shadow-md w-80">
        <Slider
          defaultValue={[min, max]}
          /* @ts-ignore */
          value={column.getFilterValue() ?? [min, max]}
          min={min}
          max={max}
          onValueChange={(range) => column.setFilterValue(range)}
        />
        <div className="flex justify-between pt-2">
          <div>
            {/* @ts-ignore */}
            {Array.isArray(column.getFilterValue()) ? column.getFilterValue()[0] : min} {type}
          </div>

          <div>
            {/* @ts-ignore */}
            {Array.isArray(column.getFilterValue()) ? column.getFilterValue()[1] : max} {type}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
