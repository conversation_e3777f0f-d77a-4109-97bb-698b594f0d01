import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { SearchIcon } from 'lucide-react';
import { Column } from '@tanstack/table-core';
import { Command, CommandInput, CommandList } from '@/components/ui/command';
import { useEffect, useState } from 'react';
import useDebounce from '@/hooks/use-debounce';

type SliderFilterProps = {
  column: Column<any, unknown>;
};

export function TextFilter({ column }: SliderFilterProps) {
  // @ts-ignore
  const [filterValue, setFilterValue] = useState<string | undefined>(column.getFilterValue()?.text);

  const debouncedValue = useDebounce(filterValue, 300);

  useEffect(() => {
    column.setFilterValue((prev: any) => {
      return { ...prev, text: debouncedValue };
    });
  }, [debouncedValue]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="py-1 px-2 relative">
          <SearchIcon className="h-4 w-4" />
          {filterValue && <div className="size-[5px] rounded-full bg-black absolute bottom-[10px] right-[6px]"></div>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="rounded-lg border shadow-md p-0 w-80">
        <Command>
          <CommandInput onValueChange={setFilterValue} placeholder="Suche..." value={filterValue} />
          <CommandList></CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
