import { useMemo, useCallback, useState } from 'react';
import { Table, VisibilityState, Header } from '@tanstack/react-table';
import React from 'react';
import ReactDOMServer from 'react-dom/server';

export function useColumnVisibility(table: Table<any>) {
  const columnOptions = useMemo(
    () =>
      table
        .getAllColumns()
        .filter((column) => column.getCanHide())
        .map((column) => {
          let label: string;
          const headerValue = column.columnDef.header;
          if (typeof headerValue === 'string') {
            label = headerValue;
          } else if (typeof headerValue === 'function') {
            // Create a mock header object
            const mockHeader: Header<any, unknown> = {
              column,
              // @ts-ignore
              header: column.id,
              id: column.id,
              isPlaceholder: false,
              placeholderId: undefined,
              depth: 0,
              index: 0,
              colSpan: 1,
              rowSpan: 1,
              subHeaders: [],
              getLeafHeaders: () => [],
              getContext: () => ({ table, header: mockHeader, column }),
            };

            // Call the header function with the mock header
            const headerElement = headerValue(mockHeader.getContext());
            if (React.isValidElement(headerElement)) {
              // If it's a React element, we render it to a string
              let renderedHeader = ReactDOMServer.renderToString(headerElement);
              // Remove HTML tags to get plain text
              label = renderedHeader.replace(/<[^>]*>/g, '');
            } else if (typeof headerElement === 'string') {
              label = headerElement;
            } else {
              // Fallback to column ID if we can't extract the header text
              label = column.id;
            }
          } else {
            // Fallback to column ID if header is not a string or function
            label = column.id;
          }
          return {
            key: column.id,
            label: label,
          };
        }),
    [],
  );

  const defaultVisibleColumns = useMemo(() => columnOptions.map((option) => option.key), [columnOptions]);

  const [selectedColumns, setSelectedColumns] = useState<string[]>(defaultVisibleColumns);

  const handleColumnVisibilityChange = useCallback(
    (selectedOptions: string[]) => {
      setSelectedColumns(selectedOptions);
      const newVisibility: VisibilityState = {};
      table.getAllColumns().forEach((column) => {
        if (column.getCanHide()) {
          newVisibility[column.id] = selectedOptions.includes(column.id);
        }
      });
      table.setColumnVisibility(newVisibility);
    },
    [table],
  );

  return {
    selectedColumns,
    columnOptions,
    defaultVisibleColumns,
    handleColumnVisibilityChange,
  };
}
