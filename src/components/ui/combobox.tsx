import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useCallback, useState } from 'react';

type ComboboxOption = {
  value: string;
  label: string;
};

type ComboboxProps = {
  options: ComboboxOption[];
  onChange: (value: string) => void;
  selectedValue?: string;
};

export function Combobox({ options, onChange, selectedValue }: ComboboxProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = useCallback(
    (currentValue: string) => {
      setOpen(false);
      onChange(currentValue);
    },
    [onChange],
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-[450px] justify-between">
          {selectedValue ? options.find((option) => option.value === selectedValue)?.label : 'Filter wählen...'}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[450px] p-0">
        <Command>
          <CommandInput placeholder="Filter suchen..." />
          <CommandEmpty>Kein Filter gefunden.</CommandEmpty>
          <CommandList>
            {options.map((option, idx) => (
              <CommandItem key={idx} value={option.value} onSelect={handleSelect}>
                <Check className={cn('mr-2 h-4 w-4', selectedValue === option.value ? 'opacity-100' : 'opacity-0')} />
                <p>{option.label}</p>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
