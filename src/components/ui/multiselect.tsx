import { UsersIcon } from 'lucide-react';
import { CheckIcon } from '@radix-ui/react-icons';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandItem, CommandList } from '@/components/ui/command';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { cn } from '@/lib/utils';

type MultiselectProps = {
  label?: string;
  defaultValues?: string[];
  values?: string[];
  options: { key: string; label: string }[];
  onChange: (selectedOptions: string[]) => void;
  canSelectAll?: boolean;
  icon?: ReactNode;
};

export const Multiselect = ({
  defaultValues = [],
  options,
  onChange,
  canSelectAll,
  label,
  icon,
  values: controlledValues,
}: MultiselectProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(
    defaultValues?.length === options.length && canSelectAll ? defaultValues.concat('all') : defaultValues,
  );
  const allOptions = useMemo(() => {
    if (canSelectAll) {
      return options.concat({
        key: 'all',
        label: 'Alle Mitarbeiter',
      });
    }

    return options;
  }, [options, canSelectAll]);

  const toggleValue = (value: string) => {
    let keysForSubmission = selectedKeys;

    if (canSelectAll && value === 'all') {
      if (selectedKeys.includes('all')) {
        setSelectedKeys([]);
        onChange([]);
        return;
      }

      setSelectedKeys(allOptions.map((option) => option.key));
      onChange(options.map((option) => option.key));
      return;
    }

    if (canSelectAll && value !== 'all' && selectedKeys.includes('all')) {
      keysForSubmission = selectedKeys.filter((v) => v !== 'all');
      setSelectedKeys(keysForSubmission.filter((v) => v !== 'all'));
    }

    if (keysForSubmission.includes(value)) {
      setSelectedKeys((prev) => prev.filter((v) => v !== value));
      onChange(keysForSubmission.filter((v) => v !== value));
    } else {
      setSelectedKeys((prev) => [...prev, value]);
      onChange([...keysForSubmission, value]);

      if (canSelectAll && value !== 'all' && selectedKeys.length + 1 === options.length) {
        setSelectedKeys((prev) => prev.concat('all'));
      }
    }
  };

  useEffect(() => {
    if (controlledValues) {
      setSelectedKeys(controlledValues);
    }
  }, [controlledValues]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" className="py-1 px-2 relative">
            {label && <span className="text-sm mr-2">{label}</span>}
            {icon ? icon : <UsersIcon className="h-4 w-4" />}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0">
          <Command>
            <CommandList>
              <CommandEmpty>Keine Einträge gefunden.</CommandEmpty>
              {allOptions &&
                Array.isArray(allOptions) &&
                allOptions?.map((option, idx) => (
                  <CommandItem key={idx} value={option.key} onSelect={toggleValue}>
                    <CheckIcon
                      className={cn(
                        'mr-2 h-4 w-4',
                        (controlledValues || selectedKeys)?.includes(option.key) ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                    {option.key == '' ? 'Leer' : option.label}
                  </CommandItem>
                ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
};
