'use client';

import { createContext, useContext, useState, FC, ReactNode } from 'react';
import { PermissionSelection } from '@/app/employees/[id]/_components/permissionsOptions';

type AuthContextProps = {
  name: string | null;
  email: string | null;
  image: string | null;
  access: PermissionSelection[] | null;
};

const AuthContext = createContext<AuthContextProps>({
  name: null,
  email: null,
  image: null,
  access: null,
});

type AuthProviderProps = AuthContextProps & { children: ReactNode };

export const AuthProvider: FC<AuthProviderProps> = ({ children, name, email, image, access }) => {
  const [authState] = useState<AuthContextProps>({
    name: name,
    email: email,
    image: image,
    access: access,
  });

  return <AuthContext.Provider value={authState}>{children}</AuthContext.Provider>;
};

export const useAuth = () => useContext(AuthContext);

export { AuthContext };
