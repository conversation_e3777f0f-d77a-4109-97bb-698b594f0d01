import {
  BexioApiResponse,
  BexioBusinessActivity,
  BexioContact,
  BexioContactSector,
  BexioInvoice,
  BexioInvoiceExtended,
  BexioProject,
  BexioSuccess,
  BexioTimesheet,
  BexioUser,
  BexioWorkPackage,
} from '@/data/types/bexio.types';
import axios, { AxiosRequestConfig, Method } from 'axios';
import { PostHog } from 'posthog-node';

const posthog = new PostHog(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
  host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
});

export class BexioClient {
  apiHost: string;
  apiToken: string;
  maxRetries: number;

  constructor() {
    this.apiHost = process.env.BEXIO_API_HOST!;
    this.apiToken = process.env.BEXIO_API_TOKEN!;
    this.maxRetries = process.env.BEXIO_MAX_RETRIES ? Number(process.env.BEXIO_MAX_RETRIES) : 3;
  }

  private async makeAuthorizedRequest(path: string, method: Method, body?: any): Promise<any> {
    console.log('BexioClient.makeAuthorizedRequest', path, method);

    const config: AxiosRequestConfig = {
      url: `${this.apiHost}${path}`,
      method: method as any,
      headers: {
        Authorization: `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: body ? JSON.stringify(body) : undefined,
      validateStatus: function (_) {
        return true; // Never throw an error regardless of the HTTP status code received
      },
    };

    let attempt = 0;
    const maxRetries = ['GET', 'DELETE'].includes(method.toUpperCase()) ? this.maxRetries : 1;
    while (attempt < maxRetries) {
      try {
        const response = await axios(config);
        console.log(method, path, response.status);

        if (response.status >= 500) {
          attempt++;
          console.warn(`Retrying request ${method} ${path} | (${attempt}/${this.maxRetries})`);
          if (attempt === this.maxRetries) {
            posthog.capture({
              distinctId: 'axios-client',
              event: 'Errored Fetch Request',
              properties: { url: path, status: response.status, method: method, data: response.data },
            });
            await posthog.shutdown();
          }
        } else {
          if (response.data) {
            return response.data;
          }

          if (maxRetries > 1) {
            attempt++;
            console.warn(`Retrying request ${method} ${path} | (${attempt}/${this.maxRetries})`);
          } else {
            return response.data;
          }
        }
      } catch (error) {
        console.error('Failed to make authorized request:', error);
        throw error;
      }
    }
  }

  // bexio timesheet = clickup time entry
  public async createTimesheet(timesheet: any): Promise<BexioApiResponse<BexioTimesheet>> {
    return this.makeAuthorizedRequest('/2.0/timesheet', 'POST', timesheet);
  }

  public async updateTimesheet(timesheetId: number, timesheet: any): Promise<BexioApiResponse<BexioTimesheet>> {
    return this.makeAuthorizedRequest(`/2.0/timesheet/${timesheetId}`, 'POST', timesheet);
  }

  public async getTimesheet(timesheetId: number): Promise<BexioApiResponse<BexioTimesheet>> {
    return this.makeAuthorizedRequest(`/2.0/timesheet/${timesheetId}`, 'GET', null);
  }

  public async getTimesheets(offset: number, limit: number = 2000): Promise<BexioApiResponse<BexioTimesheet[]>> {
    return this.makeAuthorizedRequest(`/2.0/timesheet?limit=${limit}&offset=${offset}`, 'GET', null);
  }

  public async deleteTimesheet(timesheetId: number): Promise<BexioApiResponse<BexioSuccess>> {
    return this.makeAuthorizedRequest(`/2.0/timesheet/${timesheetId}`, 'DELETE', {});
  }

  public async getProjects(): Promise<BexioApiResponse<BexioProject[]>> {
    return this.makeAuthorizedRequest('/2.0/pr_project?limit=2000', 'GET', null);
  }

  /*
    {
      "name": "Test Project", // Project Name
      "pr_state_id": 2, // 1 = offen, 2 = active, 3 = archived
      "pr_project_type_id": 2, // 1 = internal project, 2 = external project
      "contact_id": 1, // company (contact) id
      "user_id": 1, // bexio user id (michel as default)
    }
   */
  public async createProject(project: any): Promise<BexioApiResponse<BexioProject>> {
    return this.makeAuthorizedRequest('/2.0/pr_project', 'POST', project);
  }

  public async getProject(projectId: number): Promise<BexioApiResponse<BexioProject>> {
    return this.makeAuthorizedRequest(`/2.0/pr_project/${projectId}`, 'GET', null);
  }

  public async updateProject(projectId: number, project: any): Promise<BexioApiResponse<BexioProject>> {
    return this.makeAuthorizedRequest(`/2.0/pr_project/${projectId}`, 'POST', project);
  }

  public async deleteProject(projectId: number): Promise<BexioApiResponse<BexioSuccess>> {
    return this.makeAuthorizedRequest(`/2.0/pr_project/${projectId}`, 'DELETE', {});
  }

  public async createWorkPackage(projectId: number, workPackage: any): Promise<BexioApiResponse<BexioWorkPackage>> {
    return this.makeAuthorizedRequest(`/3.0/projects/${projectId}/packages`, 'POST', workPackage);
  }

  public async updateWorkPackage(
    projectId: number,
    workPackageId: number,
    workPackage: any,
  ): Promise<BexioApiResponse<BexioWorkPackage>> {
    return this.makeAuthorizedRequest(`/3.0/projects/${projectId}/packages/${workPackageId}`, 'PATCH', workPackage);
  }

  public async deleteWorkPackage(projectId: number, workPackageId: number): Promise<BexioApiResponse<BexioSuccess>> {
    return this.makeAuthorizedRequest(`/3.0/projects/${projectId}/packages/${workPackageId}`, 'DELETE', {});
  }

  public async getWorkPackages(projectId: number): Promise<BexioApiResponse<BexioWorkPackage[]>> {
    return this.makeAuthorizedRequest(`/3.0/projects/${projectId}/packages`, 'GET', null);
  }

  public async getContacts(): Promise<BexioApiResponse<BexioContact[]>> {
    return this.makeAuthorizedRequest('/2.0/contact?limit=2000', 'GET', null);
  }

  public async getUsers(): Promise<BexioApiResponse<BexioUser[]>> {
    return this.makeAuthorizedRequest('/3.0/users', 'GET', null);
  }

  public async getBusinessActivities(): Promise<BexioApiResponse<BexioBusinessActivity[]>> {
    return this.makeAuthorizedRequest('/2.0/client_service', 'GET', null);
  }

  public async getInvoicesFromClient(clientId: number): Promise<BexioApiResponse<BexioInvoice[]>> {
    return this.makeAuthorizedRequest('/2.0/kb_invoice/search', 'POST', [
      {
        field: 'contact_id',
        value: clientId,
        criteria: '=',
      },
    ]);
  }

  public async getInvoices(offset: number, limit: number = 2000): Promise<BexioApiResponse<BexioInvoice[]>> {
    return this.makeAuthorizedRequest(`/2.0/kb_invoice?limit=${limit}&offset=${offset}`, 'GET', null);
  }

  public async getInvoice(invoiceId: number): Promise<BexioApiResponse<BexioInvoiceExtended>> {
    return this.makeAuthorizedRequest(`/2.0/kb_invoice/${invoiceId}`, 'GET', null);
  }

  public async getContactSectors(): Promise<BexioApiResponse<BexioContactSector[]>> {
    return this.makeAuthorizedRequest(`/2.0/contact_branch`, 'GET', null);
  }
}
