import {
  ClickUpDataResponse,
  ClickUpFolder,
  ClickUpFoldersResponse,
  ClickUpList,
  ClickUpListsResponse,
  ClickUpSimpleResponse,
  ClickUpSpacesResponse,
  ClickUpTaskMembersResponse,
  ClickUpTasksResponse,
  ClickUpTimeEntriesTag,
  ClickUpTimeEntry,
  ClickUpWebhooksResponse,
  ClickUpSingleWebhookResponse,
  ClickUpTask,
  ClickUpGroupsResponse,
} from '@/data/types/clickup.types';
import axios, { AxiosRequestConfig } from 'axios';
import { PostHog } from 'posthog-node';

const posthog = new PostHog(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
  host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
});

export class ClickUpClient {
  apiHost: string;
  apiToken: string;
  workspaceId: string;
  teamId: string;
  webhookHost: string;
  maxRetries: number;

  constructor() {
    this.apiHost = process.env.CLICKUP_API_HOST!;
    this.apiToken = process.env.CLICKUP_API_TOKEN!;
    this.workspaceId = process.env.CLICKUP_WORKSPACE_ID!;
    this.teamId = process.env.CLICKUP_TEAM_ID!;
    this.webhookHost = process.env.WEBHOOK_HOST!;
    this.maxRetries = process.env.CLICKUP_MAX_RETRIES ? Number(process.env.CLICKUP_MAX_RETRIES) : 3;
  }

  private async makeAuthorizedRequest(path: string, method: string, body: any): Promise<any> {
    const config: AxiosRequestConfig = {
      url: `${this.apiHost}${path}`,
      method: method,
      headers: {
        Authorization: this.apiToken,
        'Content-Type': 'application/json',
      },
      data: body ? JSON.stringify(body) : undefined,
      validateStatus: () => true, // Ensure all responses are treated as resolved promises
    };

    let attempt = 0;
    const maxRetries = method.toUpperCase() === 'GET' ? this.maxRetries : 1;
    while (attempt < maxRetries) {
      try {
        const response = await axios(config);
        console.log(method, path, response.status);

        if (response.status >= 500) {
          attempt++;
          console.warn(`Retrying request ${method} ${path} | (${attempt}/${this.maxRetries})`);
          if (attempt === this.maxRetries) {
            posthog.capture({
              distinctId: 'axios-client',
              event: 'Errored Fetch Request',
              properties: { url: path, status: response.status, method: method, data: response.data },
            });
            await posthog.shutdown();
          }
        } else {
          return response.data;
        }
      } catch (error) {
        console.error('Failed to make authorized request:', error);
        throw error;
      }
    }

    throw new Error(`Failed to make authorized request to ${path} after ${this.maxRetries} attempts`);
  }

  public async createWebhookForSpace(spaceId: number, events: string[]): Promise<ClickUpSingleWebhookResponse> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/webhook`, 'POST', {
      endpoint: `${this.webhookHost}/api/webhook/clickup`,
      events: events,
      space_id: spaceId,
    });
  }

  public async getWebhooks(): Promise<ClickUpWebhooksResponse> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/webhook`, 'GET', null);
  }

  public async deleteWebhook(webhookId: string): Promise<ClickUpSimpleResponse<null>> {
    return this.makeAuthorizedRequest(`/api/v2/webhook/${webhookId}`, 'DELETE', null);
  }

  public async getFolder(folderId: string): Promise<ClickUpSimpleResponse<ClickUpFolder>> {
    return this.makeAuthorizedRequest(`/api/v2/folder/${folderId}`, 'GET', null);
  }

  public async getAllFolders(): Promise<ClickUpFoldersResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${this.workspaceId}/folder`, 'GET', null);
  }

  public async getAllFoldersFromSpace(spaceId: string): Promise<ClickUpFoldersResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${spaceId}/folder`, 'GET', null);
  }

  public async getAllArchivedFolders(): Promise<ClickUpFoldersResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${this.workspaceId}/folder?archived=true`, 'GET', null);
  }

  public async getAllArchivedFoldersFromSpace(spaceId: string): Promise<ClickUpFoldersResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${spaceId}/folder?archived=true`, 'GET', null);
  }

  public async getLists(folderId: number): Promise<ClickUpListsResponse> {
    return this.makeAuthorizedRequest(`/api/v2/folder/${folderId}/list`, 'GET', null);
  }

  public async getListsFromSpace(spaceId: string): Promise<ClickUpListsResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${spaceId}/list`, 'GET', null);
  }

  public async getArchivedListsFromSpace(spaceId: string): Promise<ClickUpListsResponse> {
    return this.makeAuthorizedRequest(`/api/v2/space/${spaceId}/list?archived=true`, 'GET', null);
  }

  public async getArchivedLists(folderId: number): Promise<ClickUpListsResponse> {
    return this.makeAuthorizedRequest(`/api/v2/folder/${folderId}/list?archived=true`, 'GET', null);
  }

  public async createFolder(name: string): Promise<ClickUpSimpleResponse<ClickUpFolder>> {
    return this.makeAuthorizedRequest(`/api/v2/space/${this.workspaceId}/folder`, 'POST', { name });
  }

  public async updateFolder(folderId: string, folder: any): Promise<ClickUpSimpleResponse<ClickUpFolder>> {
    return this.makeAuthorizedRequest(`/api/v2/folder/${folderId}`, 'PUT', folder);
  }

  public async updateList(listId: number, list: any): Promise<ClickUpSimpleResponse<ClickUpList>> {
    return this.makeAuthorizedRequest(`/api/v2/list/${listId}`, 'PUT', list);
  }

  public async getList(listId: number): Promise<ClickUpSimpleResponse<ClickUpList>> {
    return this.makeAuthorizedRequest(`/api/v2/list/${listId}`, 'GET', null);
  }

  public async getTask(taskId: string): Promise<ClickUpSimpleResponse<ClickUpTask>> {
    return this.makeAuthorizedRequest(`/api/v2/task/${taskId}`, 'GET', null);
  }

  public async getTasks(page: number, attrs: object = {}): Promise<ClickUpTasksResponse> {
    const attrString = Object.entries(attrs)
      .map(([key, value]) => `&${key}=${value}`)
      .join('');
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/task?space_ids[]=${this.workspaceId}&page=${page}&include_closed=true&subtasks=true${attrString}`,
      'GET',
      null,
    );
  }

  public async getCustomTasksQuery(listId: string, page: number, attrs: object = {}): Promise<ClickUpTasksResponse> {
    const attrString = Object.entries(attrs)
      .map(([key, value]) => `&${key}=${value}`)
      .join('');
    return this.makeAuthorizedRequest(`/api/v2/list/${listId}/task?&page=${page}${attrString}`, 'GET', null);
  }

  public async getTasksFromList(listId: string, page: number): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(
      `/api/v2/list/${listId}/task?page=${page}&include_closed=true&subtasks=true`,
      'GET',
      null,
    );
  }

  public async getOpenTasksFromList(listId: string, page: number): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(`/api/v2/list/${listId}/task?page=${page}`, 'GET', null);
  }

  public async getArchivedTasks(page: number): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/task?space_ids[]=${this.workspaceId}&archived=true&page=${page}&include_closed=true&subtasks=true`,
      'GET',
      null,
    );
  }

  public async getArchivedTasksFromList(listId: string, page: number): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(
      `/api/v2/list/${listId}/task?archived=true&page=${page}&include_closed=true&subtasks=true`,
      'GET',
      null,
    );
  }

  public async createTask(listId: string, task: any): Promise<ClickUpSimpleResponse<ClickUpTask>> {
    return this.makeAuthorizedRequest(`/api/v2/list/${listId}/task`, 'POST', task);
  }

  public async deleteTask(taskId: string): Promise<ClickUpSimpleResponse<null>> {
    return this.makeAuthorizedRequest(`/api/v2/task/${taskId}`, 'DELETE', null);
  }

  public async getTasksOfSpaces(page: number, spaces: string[]): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/task?${spaces.map((s) => `space_ids[]=${s}`).join('&')}&page=${page}&include_closed=true&subtasks=true`,
      'GET',
      null,
    );
  }

  public async getArchivedTasksOfSpaces(page: number, spaces: string[]): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/task?${spaces.map((s) => `space_ids[]=${s}`).join('&')}&archived=true&page=${page}&include_closed=true&subtasks=true`,
      'GET',
      null,
    );
  }

  public async getAllListMembers(ListId: number): Promise<ClickUpTaskMembersResponse> {
    return this.makeAuthorizedRequest(`/api/v2/list/${ListId}/member`, 'GET', null);
  }

  public async getBillableTimeEntries(userId: string): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?space_id=${this.workspaceId}&assignee=${userId}&start_date=0&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getTimeEntry(timeEntryId: string): Promise<ClickUpDataResponse<ClickUpTimeEntry>> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries/${timeEntryId}`, 'GET', null);
  }

  public async getBillableTimeEntriesRange(
    userId: string,
    start: number,
    end: number,
  ): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?space_id=${this.workspaceId}&assignee=${userId}&start_date=${start}&end_date=${end}&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getAllTimeEntries(userIds: string[]): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?assignee=${userIds.join(',')}&start_date=0&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getAllTimeEntriesRange(
    userId: string,
    start: number,
    end: number,
  ): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?assignee=${userId}&start_date=${start}&end_date=${end}&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getAllTimeEntriesFrom(userId: string, start: number): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?assignee=${userId}&start_date=${start}&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getRunningTimeEntry(userId: string): Promise<ClickUpDataResponse<ClickUpTimeEntry>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries/current?assignee=${userId}&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getSpaceTimeEntriesRange(
    userId: string,
    spaceId: string,
    start: number,
    end: number,
  ): Promise<ClickUpDataResponse<ClickUpTimeEntry[]>> {
    return this.makeAuthorizedRequest(
      `/api/v2/team/${this.teamId}/time_entries?assignee=${userId}&space_id=${spaceId}&start_date=${start}&end_date=${end}&include_location_names=true`,
      'GET',
      null,
    );
  }

  public async getTimeEntriesTags(): Promise<ClickUpDataResponse<ClickUpTimeEntriesTag[]>> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries/tags`, 'GET', null);
  }

  public async bulkDeleteTagsFromTimeEntries(
    timeEntryIds: string[],
    tagName: string,
  ): Promise<ClickUpSimpleResponse<object>> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries/tags`, 'DELETE', {
      time_entry_ids: timeEntryIds,
      tags: [tagName],
    });
  }

  public async bulkAssignTagsToTimeEntries(timeEntryIds: string[], tag: any): Promise<ClickUpSimpleResponse<object>> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries/tags`, 'POST', {
      time_entry_ids: timeEntryIds,
      tags: [tag],
    });
  }

  public async getSpaces(): Promise<ClickUpSpacesResponse> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/space`, 'GET', null);
  }

  public async createTimeEntry(
    tid: string,
    assignee: number,
    start: number,
    duration: number,
    tags: any[],
  ): Promise<ClickUpSimpleResponse<ClickUpTimeEntry>> {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries`, 'POST', {
      tid,
      start,
      duration,
      assignee,
      tags,
    });
  }

  public async getGroups(): Promise<ClickUpGroupsResponse> {
    return this.makeAuthorizedRequest(`/api/v2/group?team_id=${this.teamId}`, 'GET', null);
  }

  public async deleteTimeEntry(entryId: string) {
    return this.makeAuthorizedRequest(`/api/v2/team/${this.teamId}/time_entries/${entryId}`, 'DELETE', null);
  }

  public async getTasksFromView(viewId: string, page: number = 0): Promise<ClickUpTasksResponse> {
    return this.makeAuthorizedRequest(`/api/v2/view/${viewId}/task?page=${page}&subtasks=true`, 'GET', null);
  }
}
