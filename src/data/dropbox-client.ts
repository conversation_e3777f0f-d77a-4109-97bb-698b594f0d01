import { Dropbox } from 'dropbox';
import fetch from 'node-fetch';

export class DropboxClient {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  client: Dropbox;

  constructor() {
    this.clientId = process.env.DROPBOX_CLIENT_ID!;
    this.clientSecret = process.env.DROPBOX_CLIENT_SECRET!;
    this.refreshToken = process.env.DROPBOX_REFRESH_TOKEN!;
    this.client = new Dropbox({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      refreshToken: this.refreshToken,
      pathRoot: JSON.stringify({ '.tag': 'namespace_id', namespace_id: process.env.DROPBOX_ROOT_NAMESPACE_ID }),
      fetch,
    });
  }

  public async createFolder(folderPath: string) {
    return this.client.filesCreateFolderV2({ path: folderPath });
  }

  public async deleteFolder(folderPath: string) {
    return this.client.filesDeleteV2({ path: folderPath });
  }

  public async renameFolder(folderPath: string, newPath: string, autorename: boolean = false) {
    return this.client.filesMoveV2({ from_path: folderPath, to_path: newPath, autorename });
  }

  public async getFolder(folderId: string) {
    return this.client.filesGetMetadata({ path: folderId });
  }

  public async getFolderContents(folderId: string, recursive: boolean = false) {
    return this.client.filesListFolder({ path: folderId, recursive, limit: 2000 });
  }

  public async getFolderContentsContinue(cursor: string) {
    return this.client.filesListFolderContinue({ cursor });
  }
}
