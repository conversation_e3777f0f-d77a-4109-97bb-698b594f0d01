import nodemailer, { Transporter } from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';

export class EmailClient {
  from: string;
  user: string;
  password: string;
  smtpHost: string;
  transporter: Transporter<SMTPTransport.SentMessageInfo>;

  constructor() {
    this.from = process.env.EMAIL_FROM!;
    this.user = process.env.EMAIL_USER!;
    this.password = process.env.EMAIL_PASSWORD!;
    this.smtpHost = process.env.EMAIL_SMTP_HOST!;

    this.transporter = nodemailer.createTransport({
      host: this.smtpHost,
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.user,
        pass: this.password,
      },
      tls: {
        ciphers: 'SSLv3',
      },
    });
  }
  public async sendEmail(to: string, subject: string, text: string) {
    console.log('Sending email to', to);
    const mailOptions = {
      from: `${this.from}`,
      to: to,
      subject: subject,
      html: text,
    };

    return this.transporter.sendMail(mailOptions);
  }
}
