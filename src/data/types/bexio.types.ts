export type BexioApiResponse<T> = BexioError | T;

export type BexioError = {
  error_code: number;
  message: string;
};

export type BexioSuccess = {
  success: boolean;
};

export type BexioBusinessActivity = {
  id: number;
  name: string;
  default_is_billable: boolean;
  default_price_per_hour: number;
  account_id: number;
};

export type BexioProject = {
  id: number;
  uuid: string;
  nr: number;
  name: string;
  start_date: string | null;
  end_date: string | null;
  comment: string;
  pr_state_id: number;
  pr_project_type_id: number;
  contact_id: number;
  contact_sub_id: number | null;
  pr_invoice_id: number | null;
  pr_invoice_type_amount: string;
  pr_budget_type_id: number | null;
  pr_budget_type_amount: string;
  user_id: number;
};

export type BexioWorkPackage = {
  id: number;
  name: string; // <= 255 characters
  spent_time_in_hours: number;
  estimated_time_in_hours: number;
  comment: string; // <= 1000 characters
  pr_milestone_id: number;
};

export type BexioTimesheet = {
  id: number;
  user_id: number;
  status_id: number;
  client_service_id: number;
  text: string;
  allowable_bill: boolean;
  charge: string | number;
  contact_id: number | null;
  sub_contact_id: number | null;
  pr_project_id: number | null;
  pr_package_id: number | null;
  pr_milestone_id: number | null;
  travel_time: string | null;
  travel_charge: string | null;
  travel_distance: number;
  estimated_time: string | null;
  date: string;
  duration: string | null;
  running: boolean;
  tracking: BexioTimesheetDuration | BexioTimesheetRange | BexioTimesheetStopwatch;
};

export type BexioTimesheetDuration = {
  type: 'duration';
  date: string;
  duration: string;
};

export type BexioTimesheetRange = {
  type: 'range';
  start: string;
  end: string;
};

export type BexioTimesheetStopwatch = {
  type: 'stopwatch';
  duration: string;
};

export type BexioContact = {
  id: number;
  nr: string;
  contact_type_id: number;
  name_1: string;
  name_2: string | null;
  salutation_id: number | null;
  salutation_form: string | null;
  title_id: number | null;
  birthday: string | null;
  address: string | null;
  postcode: number | null;
  city: string | null;
  country_id: number | null;
  mail: string | null;
  mail_second: string | null;
  phone_fixed: string | null;
  phone_fixed_second: string | null;
  phone_mobile: string | null;
  fax: string | null;
  url: string | null;
  skype_name: string | null;
  remarks: string | null;
  language_id: number | null;
  contact_group_ids: string | null;
  contact_branch_ids: string | null;
  user_id: number;
  owner_id: number;
  updated_at: string;
};

export type BexioUser = {
  id: number;
  salutation_type: string;
  firstname: string | null;
  lastname: string | null;
  email: string;
  is_superadmin: boolean;
  is_accountant: boolean;
};

export type BexioInvoice = {
  id: number;
  document_nr: string;
  title: string;
  contact_id: number;
  contact_sub_id: number;
  user_id: number;
  project_id: number;
  logopaper_id: number;
  language_id: number;
  bank_account_id: number;
  currency_id: number;
  payment_type_id: number;
  header: string;
  footer: string;
  total_gross: string;
  total_net: string;
  total_taxes: string;
  total_received_payments: string;
  total_credit_vouchers: string;
  total_remaining_payments: string;
  total: string;
  total_rounding_difference: number;
  mwst_type: number;
  mwst_is_net: boolean;
  show_position_taxes: boolean;
  is_valid_from: string;
  is_valid_to: string;
  contact_address: string;
  kb_item_status_id: 7 | 8 | 9 | 16 | 19 | 31;
  reference: string | null;
  api_reference: string | null;
  viewed_by_client_at: string | null;
  updated_at: string;
  esr_id: number;
  qr_invoice_id: number;
  template_slug: string;
  taxs: BexioTax[];
  network_link: string;
};

export type BexioTax = {
  percentage: string;
  value: string;
};

export type BexioContactSector = {
  id: number;
  name: string;
};

export type BexioInvoiceExtended = BexioInvoice & {
  positions: BexioInvoicePosition[];
};

export type BexioInvoicePosition = {
  id: number;
  amount: string;
  account_id?: number;
  position_total?: string;
  text: string;
};

export function isBexioError(response: BexioApiResponse<any>): response is BexioError {
  return response && 'error_code' in response && (response as BexioError).error_code !== undefined;
}
