export type ClickUpDataResponse<T> = ClickUpError | { data: T };

export type ClickUpSimpleResponse<T> = ClickUpError | T;

export type ClickUpError = {
  err: string;
  ECODE: string;
};

export type ClickUpTimeEntriesTag = {
  name: string;
  creator: number;
  tag_bg: string;
  tag_fg: string;
};

export type ClickUpTimeEntry = {
  id: string;
  task: ClickUpTimeEntryTask;
  wid: string;
  user: ClickUpUser;
  billable: boolean;
  start: string;
  end: string;
  duration: string;
  description: string;
  tags: ClickUpTaskTag[];
  source: string;
  at: string;
  task_location: ClickUpTaskLocation;
  task_url: string;
};

export type ClickUpTimeEntryTask = {
  id: string;
  custom_id: string;
  name: string;
  status: ClickUpTaskStatus;
  custom_type: string | null;
};

export type ClickUpTaskStatus = {
  status: string;
  color: string;
  orderindex: number;
  type: string;
};

export type ClickUpUser = {
  id: number;
  username: string;
  initials: string;
  email: string;
  color: string;
  profilePicture: string;
};

export type ClickUpTaskLocation = {
  list_id: number;
  folder_id: number;
  space_id: number;
  list_name?: string;
  folder_name?: string;
  space_name?: string;
};

export type ClickUpTaskTag = {
  name: string;
  tag_fg: string;
  tag_bg: string;
  creator: number;
};

export type ClickUpTask = {
  id: string;
  name: string;
  status: ClickUpTaskStatus;
  archived: boolean;
  description: string;
  orderindex: number;
  date_created: string;
  date_updated: string;
  date_closed: string | null;
  date_done: string | null;
  creator: { id: number; username: string; profilePicture: string };
  assignees: ClickUpTaskAssignee[];
  due_date: string | null;
  start_date: string | null;
  time_estimate: number;
  time_spent?: number;
  list: { id: string; name: string };
  space: { id: string };
  url: string;
  custom_fields?: ClickUpTaskCustomField[];
};

export type ClickUpTaskAssignee = {
  id: number;
  username: string;
  email: string;
  color: string;
  initials: string;
  profilePicture: string;
};

export type ClickUpTaskCustomField = {
  id: string;
  name: string;
  type: string;
  type_config: {
    single_user?: boolean;
    include_team_members?: boolean;
    include_guests?: boolean;
    options?: CustomFieldOption[];
  };
  date_created: string;
  hide_from_guests: boolean;
  value: (CustomFieldValue | string[])[];
  required: boolean;
};

type CustomFieldOption = {
  id: string;
  name: string;
  color: string;
  orderindex: number;
};

type CustomFieldValue = {
  id: number;
  username: string;
  email: string;
  color: string;
  initials: string;
  profilePicture: string;
};

export type ClickUpTasksResponse = ClickUpError | { tasks: ClickUpTask[]; last_page: boolean };

export type ClickUpTaskMember = {
  id: number;
  username: string;
  email: string;
  color: string;
  initials: string;
  profilePicture: string;
};

export type ClickUpTaskMembersResponse = ClickUpError | { members: ClickUpTaskMember[] };

export type ClickUpSpace = {
  id: string;
  name: string;
  private: boolean;
  color: string;
  avatar: string;
  admin_can_manage: boolean;
  archived: boolean;
};

export type ClickUpSpacesResponse = ClickUpError | { spaces: ClickUpSpace[] };

export type ClickUpFolder = {
  id: string;
  name: string;
  orderindex: number;
  override_statuses: boolean;
  hidden: boolean;
  space: ClickUpSpace;
  task_count: number;
  archived: boolean;
  assignee: any; // some assignee object
  lists: ClickUpList[];
};

export interface ClickUpList {
  id: string;
  name: string;
  orderindex: number;
  deleted?: boolean;
  content: string;
  status: any; // some status object
  priority: any; // some priority object
  assignee: ClickUpListAssignee | null;
  task_count: number;
  due_date: string | null;
  start_date: string | null;
  folder: { id: string; name: string; hidden: boolean };
  space: { id: string; name: string };
  archived: boolean;
}

export type ClickUpListAssignee = {
  id: string;
  color: string;
  username: string;
  initials: string;
  profilePicture: string;
};

export type ClickUpFoldersResponse = ClickUpError | { folders: ClickUpFolder[] };

export type ClickUpListsResponse = ClickUpError | { lists: ClickUpList[] };

export type ClickUpWebhook = {
  id: string;
  userid: number;
  team_id: number;
  endpoint: string;
  client_id: string;
  events: string[];
  task_id: string | null;
  list_id: string | null;
  folder_id: string | null;
  space_id: string | null;
  health: { status: string; fail_count: number };
  secret: string;
};

export type ClickUpWebhooksResponse = ClickUpError | { webhooks: ClickUpWebhook[] };

export type ClickUpSingleWebhookResponse = ClickUpError | { id: string; webhook: ClickUpWebhook };

export type ClickUpGroupMember = {
  id: number;
  username: string;
  email: string;
  color: string;
  initials: string;
  profilePicture: string;
};

export type ClickUpGroup = {
  id: string;
  team_id: string;
  userId: number;
  name: string;
  handle: string;
  date_created: string;
  initials: string;
  members: ClickUpGroupMember[];
  avatar: any;
};

export type ClickUpGroupsResponse = ClickUpError | { groups: ClickUpGroup[] };

export function isClickUpError(response: any): response is ClickUpError {
  return (response as ClickUpError).err !== undefined;
}
