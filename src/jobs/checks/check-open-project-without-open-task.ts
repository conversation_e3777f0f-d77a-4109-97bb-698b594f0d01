import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC } from '@/lib/utils';
import { ClickUpClient } from '@/data/clickup-client';

export const checkOpenProjectWithoutOpenTask = client.defineJob({
  id: 'check-open-project-without-open-task',
  name: 'Check open Project without open task',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('0 4 * * 1-5') }), // every weekday at 4am
  run: async (payload, io, _) => {
    const activeProjectsWithoutOpenTask = await io.runTask('get-active-projects-without-open-task', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select(
          'clickup_list_id, clickup_name, clickup_user_id, clients(name), tasks(clickup_status, clickup_due_date)',
        )
        .eq('clickup_archived', false)
        .eq('is_client_project', true)
        .eq('deleted', false)
        .is('clickup_end', null)
        .neq('tasks.clickup_status', 'Closed');

      if (error) {
        throw new Error(`Error fetching active projects without tasks or due date: ${JSON.stringify(error)}`);
      }

      const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
      return data.filter((project) => {
        const tasks = project.tasks || [];
        const hasOpenTasks = !!tasks.length;
        const hasATaskWithDueDate = tasks.some((task) => task.clickup_due_date);
        const hasATaskThatIsNotOverdue = tasks.some(
          (task) => task.clickup_due_date && task.clickup_due_date > yesterday,
        );

        return !hasOpenTasks || !hasATaskWithDueDate || !hasATaskThatIsNotOverdue;
      });
    });

    await io.logger.info(`Found ${activeProjectsWithoutOpenTask.length} projects without open task`);

    const globalSettings = await io.runTask('get-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    await io.runTask('notify-project-without-open-task', async () => {
      for (const project of activeProjectsWithoutOpenTask) {
        await io.runTask(`notify-project-without-open-task-${project.clickup_list_id}`, async () => {
          const clickupClient = new ClickUpClient();
          await clickupClient.createTask(String(globalSettings.pm_list_id), {
            name: `pm: Check Tasks - ${project.clickup_name} - ${project.clients?.name}`,
            description: `Auf folgender aktiven Kundenprojektliste "${project.clickup_name}" des Kunden "${project.clients?.name}" ist eines dieser Probleme vorhanden:

- Keine offene Task mehr vorhanden
- Keine Task mit Fälligkeitsdatum oder Fälligkeitsdatum in der Zukunft

Checke die Liste:
Überprüfe, ob das Projekt abgeschlossen werden kann. 
Falls ja, dann verfahre nach dem PM Projektabschluss Prozess und erfasse eine Task hierzu auf der Liste und setze das Fälligkeitsdatum.
Falls nicht, plane weitere Tasks ein auf der Liste, mit denen du fortfahren musst, um das Projekt weiter voranzutreiben (mindestens ein rm: Task muss immer auf der Liste sein, bspw. wenn ein Projekt angehalten ist).

Hinweis: Eine Kundenprojektliste die nicht archiviert ist muss immer mindestens eine offene Task mit Fälligkeitsdatum in der Zukunft haben, damit das Projekt nicht plötzlich in Vergessenheit gerät.

Link zur Clickup-Liste:  https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${project.clickup_list_id}`,
            assignees: [project.clickup_user_id || process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });
        });
      }
    });
  },
});
