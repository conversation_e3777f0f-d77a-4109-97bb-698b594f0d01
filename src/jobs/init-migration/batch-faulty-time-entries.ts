import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertUNIXTimestampToDateTimeString, prettyPrintTime } from '@/lib/utils';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';

export const batchFaultyTimeEntriesJob = client.defineJob({
  id: 'batch-faulty-time-entries',
  name: 'Batch faulty time entries',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH GLOBAL SETTINGS
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const { data, error } = await getGlobalSettings();
      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const fstJan23Date = new Date('2023-01-01T00:00:00.000Z').getTime();
    const todayMinus24h = new Date(new Date().getTime() - 12 * 60 * 60 * 1000).getTime();

    // CHECK FOR OVERLAPPING TIME ENTRIES
    await io.runTask('check-for-overlapping-time-entries', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select('id, clickup_start, clickup_end, clickup_duration, clickup_user_id, clickup_task_id, employees(name)')
        .gte('clickup_start', fstJan23Date)
        .lte('clickup_end', todayMinus24h);

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      const timeEntries = data || [];
      const overlappingTimeEntries = [];
      for (const te of timeEntries) {
        const overlapping = timeEntries.filter(
          (t) =>
            t.clickup_user_id === te.clickup_user_id &&
            t.id !== te.id &&
            Number(t.clickup_start) <= Number(te.clickup_start) &&
            Number(t.clickup_end) >= Number(te.clickup_start) &&
            Number(t.clickup_end) - Number(te.clickup_start) >= 60 * 1000,
        );

        if (overlapping.length > 0) {
          overlappingTimeEntries.push(overlapping.concat([te]));
        }
      }

      // list of overlapping time entries for each user and date
      const overlappingTimeEntriesPerUser: Record<string, Record<string, any[]>> = {};
      for (const te of overlappingTimeEntries) {
        const date = new Date(Number(te[0].clickup_start)).toISOString().slice(0, 10);
        const user = te[0].employees?.name || 'unknown';
        if (!overlappingTimeEntriesPerUser[user]) {
          overlappingTimeEntriesPerUser[user] = { [date]: [] };
        }

        if (!overlappingTimeEntriesPerUser[user][date]) {
          overlappingTimeEntriesPerUser[user][date] = [];
        }
        overlappingTimeEntriesPerUser[user][date].push(te);
      }

      // create csv file with overlapping time entries and users
      let csv = 'user,date,start,end,duration,task\n';
      for (const [user, record] of Object.entries(overlappingTimeEntriesPerUser)) {
        for (const [date, tes] of Object.entries(record)) {
          for (const otes of tes) {
            for (const te of otes) {
              csv += `${user},${date},${convertUNIXTimestampToDateTimeString(Number(te.clickup_start))},${convertUNIXTimestampToDateTimeString(Number(te.clickup_end))},${prettyPrintTime(Number(te.clickup_duration))},${te.clickup_task_id ? `https://app.clickup.com/t/${te.clickup_task_id}` : 'no task'}\n`;
            }
          }
          csv += '\n';
        }
      }

      return csv;
    });
  },
});
