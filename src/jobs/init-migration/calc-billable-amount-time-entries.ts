import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';

export const calcBillableAmountTimeEntries = client.defineJob({
  id: 'calc-billable-amount-time-entries',
  name: 'Calc Billable Amount Time Entries',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const timeEntriesWithAmount = await io.runTask(`calc-billable-amount-time-entries`, async () => {
      const supabase = createClient();
      const { data: tags, error: tagsError } = await supabase.from('business_activities').select('*');

      if (tagsError) {
        throw new Error(`Error fetching tags: ${JSON.stringify(tagsError)}`);
      }

      const { data: timeEntries, error: timeEntriesError } = await supabase
        .from('time_entries')
        .select('*')
        .eq('billable', true)
        .or('billable_amount.is.null,billable_amount.eq.0');

      if (timeEntriesError) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(timeEntriesError)}`);
      }

      return timeEntries.map((entry) => {
        const tag = tags.find((tag) => tag.clickup_task_tag === entry.clickup_task_tag);

        if (!tag || !tag.billable || !tag.hourly_rate) {
          return {
            clickup_time_entry_id: entry.clickup_time_entry_id!,
            billable_amount: 0,
          };
        }

        const timeInHours = (entry.clickup_duration || 0) / 1000 / 60 / 60;
        return {
          clickup_time_entry_id: entry.clickup_time_entry_id!,
          billable_amount: timeInHours * (tag?.hourly_rate || 0),
        };
      });
    });

    await io.runTask(`update-time-entries`, async () => {
      const supabase = createClient();
      const { error } = await supabase.from('time_entries').upsert(timeEntriesWithAmount, {
        onConflict: 'clickup_time_entry_id',
      });

      if (error) {
        throw new Error(`Error updating time entries: ${JSON.stringify(error)}`);
      }
    });

    await io.runTask(`update-unarchived-bexio-projects-effective-cost`, async () => {
      const supabase = createClient();
      const { data: projects, error } = await supabase
        .from('projects')
        .select('clickup_list_id, bexio_project_id, clickup_archived, clickup_end')
        .eq('is_client_project', true)
        .eq('deleted', false)
        .not('bexio_project_id', 'is', null);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      let iter = 0;
      for (const project of projects) {
        if (++iter % 100 === 0) {
          await io.wait(`wait-iter-${iter}`, 1);
        }

        await io.runTask(`calc-effective-cost-${project.clickup_list_id}`, async () => {
          if (!project.clickup_list_id || !project.bexio_project_id) {
            await io.logger.info(`Skipping project ${project.clickup_list_id} ${project.bexio_project_id}`);
            return;
          }

          const aWeekAgo = new Date(new Date().setDate(new Date().getDate() - 7));
          if (project.clickup_archived && new Date(Number(project.clickup_end)) >= aWeekAgo) {
            await io.logger.info(`Skipping archived project ${project.clickup_list_id} ${project.bexio_project_id}`);
            return;
          }

          const { data: clickupTasks, error } = await supabase
            .from('tasks')
            .select('clickup_task_id, time_entries(id, billable, billable_amount)')
            .eq('clickup_list_id', project.clickup_list_id!);

          if (error) {
            throw new Error(`Error fetching clickup tasks: ${JSON.stringify(error)}`);
          }

          const accAmount = clickupTasks.reduce((acc, cur) => {
            const timeEntries = cur.time_entries.filter((entry) => entry.billable && entry.billable_amount);
            return acc + timeEntries.reduce((acc, cur) => acc + Number(cur.billable_amount), 0);
          }, 0);

          await io.logger.info(`Effective Cost: ${accAmount}`);
          const updateValues = {
            effective_cost: accAmount,
          };

          const { error: updateError } = await supabase
            .from('bexio_projects')
            .update(updateValues)
            .eq('bexio_id', project.bexio_project_id!);

          if (updateError) {
            throw new Error(`Error updating bexio projects: ${JSON.stringify(error)}`);
          }
        });
      }
    });
  },
});
