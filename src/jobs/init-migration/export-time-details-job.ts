import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { getISODateString } from '@/lib/utils';
import { createClient } from '@/data/supabase-server';
import { ClickUpTask, ClickUpTimeEntry } from '@/data/types/clickup.types';
import { getAllPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';
import { getAllClickupTimeEntries } from '@/server-actions/time-reporting/simple-report';

export const exportTimeDetailsJob = client.defineJob({
  id: 'export-time-details-job',
  name: 'Export Time Details',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const ginaClickupUserId = '38340642';
    const ginaEmployeeId = '46411d02-f6ed-4294-9e82-9149f29954c7';

    const days = await io.runTask('fetch-days', async () => {
      const start = new Date('2024-01-01');
      const yesterday = getISODateString(new Date(new Date().setDate(new Date().getDate() - 1)));
      const end = new Date(yesterday);
      let curDate = getISODateString(new Date(start));
      const days = [];
      while (new Date(curDate) < end) {
        days.push(curDate);
        curDate = getISODateString(new Date(new Date(curDate).setDate(new Date(curDate).getDate() + 1)));
      }
      return days;
    });

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const workingHours = await io.runTask('fetch-working-hours', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('working_hours').select('*').eq('user_id', ginaEmployeeId);

      if (error) {
        throw new Error(`Error fetching working hours: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const ptoTimes = await io.runTask('fetch-pto-times', async () => {
      const tasks: ClickUpTask[] = await getAllPTOTimes(String(globalSettings.pto_list_id));
      const ptoTasks = tasks.filter(
        (task) => task.start_date && task.assignees.map((a) => Number(a.id)).includes(Number(ginaClickupUserId)),
      );
      await io.logger.info(`Found ${ptoTasks.length} PTO tasks`);
      return ptoTasks.map((task) => {
        return {
          clickup_task_id: task.id,
          clickup_task_name: task.name,
          date: getISODateString(new Date(Number(task.start_date!))),
          duration: (task.time_spent || 0) / task.assignees.length,
          clickup_user_id: ginaClickupUserId,
        };
      });
    });

    const clickupHrs = await io.runTask('clickup-hrs', async () => {
      const clickupEntries: ClickUpTimeEntry[] = await getAllClickupTimeEntries(String(globalSettings.pto_list_id));
      const clickupEntriesFiltered = clickupEntries.filter(
        (entry) => Number(entry.user.id) === Number(ginaClickupUserId),
      );

      return clickupEntriesFiltered.reduce(
        (acc, curr) => {
          const date = getISODateString(new Date(Number(curr.start)));
          if (!acc[date]) {
            acc[date] = 0;
          }
          if (curr.duration) {
            acc[date] += Number(curr.duration) / 1000 / 60 / 60;
          }
          return acc;
        },
        {} as { [key: string]: number },
      );
    });

    const report = await io.runTask('generate-report', async () => {
      let reportCsv = `Datum,Arbeitszeit,PTO,Tracked,Gesamt\n`;
      for (const day of days) {
        const filteredTasks = ptoTimes.filter((ptoTime) => ptoTime.date === day);
        const filteredWH = workingHours.filter((wh) => wh.date === day);
        const ptoReduced = filteredTasks.reduce((acc, ptoTime) => {
          return acc + ptoTime.duration / 1000 / 60 / 60;
        }, 0);
        const whReduced = filteredWH.reduce((acc, wh) => {
          return acc + Number(wh.hours);
        }, 0);
        const clickupHours = clickupHrs[day] || 0;
        reportCsv += `${day},${whReduced},${ptoReduced},${clickupHours},${clickupHours + ptoReduced}\n`;
      }
      return reportCsv;
    });
  },
});
