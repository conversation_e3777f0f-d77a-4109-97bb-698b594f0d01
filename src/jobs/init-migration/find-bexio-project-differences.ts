import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { getClickupClientLists } from '@/jobs/shared/clickup/get-clickup-client-lists';

export const findBexioProjectDifferences = client.defineJob({
  id: 'find-bexio-project-differences',
  name: 'Find Bexio project differences',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { shouldDeleteRedundantProjects } = payload;

    const clickupLists = await io.runTask('fetch-clickup-lists', async () => {
      const lists = await getClickupClientLists(io);
      return lists.map((list) => ({
        id: list.id,
        name: list.name,
      }));
    });

    const bexioProjects = await io.runTask('fetch-bexio-projects', async () => {
      const bexioClient = new BexioClient();
      const projectResponse = await bexioClient.getProjects();

      if (isBexioError(projectResponse)) {
        throw new Error(`Error fetching projectResponse: ${JSON.stringify(projectResponse)}`);
      }

      return projectResponse.map((bts) => ({
        bexio_project_id: bts.id,
        bexio_nr: bts.nr,
        date: bts.start_date,
      }));
    });

    const [dbBexioIds, dbClickupIds] = await io.runTask('fetch-active-db-projects', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('projects').select('*');
      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const activeProjects = data
        .filter((p) => p.bexio_project_id)
        .filter((p) => !p.deleted)
        .filter((p) => !p.clickup_archived);
      const bexioIds = activeProjects.map((p) => p.bexio_project_id!);
      const clickupIds = activeProjects.map((p) => p.clickup_list_id!).filter((id) => id);
      return [bexioIds, clickupIds];
    });

    const redundantProjects = await io.runTask('get-redundant-projects', async () => {
      return bexioProjects.filter((p) => !dbBexioIds.includes(p.bexio_project_id));
    });

    await io.logger.info(`Found ${redundantProjects.length} redundant projects`);
    await io.logger.info(`Found ${dbBexioIds.length} db projects`);
    await io.logger.info(`Found ${bexioProjects.length} bexio projects`);
    await io.logger.info(`Found ${clickupLists.length} clickup lists`);

    const projectsNotSyncedToDb = clickupLists.filter((list) => !dbClickupIds.some((listId) => listId === list.id));
    const projectsNotDeletedInDb = dbClickupIds.filter((id) => !clickupLists.some((p) => p.id === id));

    await io.logger.info(`Found ${projectsNotSyncedToDb.length} projects not synced to db`);
    if (projectsNotSyncedToDb.length) {
      await io.logger.info(`Projects not synced to db: ${JSON.stringify(projectsNotSyncedToDb)}`);
    }

    await io.logger.info(`Found ${projectsNotDeletedInDb.length} projects not deleted in db`);
    if (projectsNotDeletedInDb.length) {
      await io.logger.info(`Projects not deleted in db: ${JSON.stringify(projectsNotDeletedInDb)}`);
    }

    if (!shouldDeleteRedundantProjects) {
      return;
    }

    // await io.runTask('delete-redundant-projects', async () => {
    //   const bexioClient = new BexioClient();
    //   for (const p of redundantProjects) {
    //     await io.runTask(`delete-redundant-project-${p.bexio_project_id}`, async () => {
    //       const res = await bexioClient.deleteProject(p.bexio_project_id);
    //
    //       if (isBexioError(res)) {
    //         throw new Error(`Failed to delete redundant project ${p.bexio_project_id}: ${res.message}`);
    //       }
    //     });
    //   }
    // });
  },
});
