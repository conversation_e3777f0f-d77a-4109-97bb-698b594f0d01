import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { BexioTimesheet, isBexioError } from '@/data/types/bexio.types';

export const findBexioTimeDifferences = client.defineJob({
  id: 'find-bexio-time-differences',
  name: 'Find Bexio time differences',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { shouldDeleteRedundantTimeEntries } = payload;

    const bexioTimeSheets = await io.runTask('fetch-bexio-timesheets', async () => {
      const bexioClient = new BexioClient();
      let bexioTimesheets: BexioTimesheet[] = [];
      let curTimesheets: BexioTimesheet[] = [];
      let offset = 0;
      let lastPage = false;
      while (!lastPage) {
        [curTimesheets, lastPage] = await io.runTask(`fetch-bexio-timesheets-offset-${offset}`, async () => {
          const timesheetResponse = await bexioClient.getTimesheets(offset);

          if (isBexioError(timesheetResponse)) {
            throw new Error(`Error fetching timesheets: ${JSON.stringify(timesheetResponse)} at offset ${offset}`);
          }

          return [timesheetResponse, !timesheetResponse.length];
        });

        bexioTimesheets = bexioTimesheets.concat(curTimesheets);
        await io.logger.info(
          `Ran task fetch-bexio-timesheets-offset-${offset} with ${curTimesheets.length} timesheets`,
        );
        offset += 2000;
      }

      return bexioTimesheets.map((bts) => ({
        bexio_timesheet_id: bts.id,
        date: bts.date,
        duration: bts.duration,
      }));
    });

    const dbTimeEntries = await io.runTask('fetch-time-entries', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('time_entries').select('*');
      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      return data.filter((te) => te.bexio_timesheet_id).map((te) => te.bexio_timesheet_id!);
    });

    const redundantTimeEntries = await io.runTask('get-redundant-time-entries', async () => {
      return bexioTimeSheets
        .filter((te) => !dbTimeEntries.includes(te.bexio_timesheet_id))
        .filter((te) => te.date.startsWith('2024'));
    });

    await io.logger.info(`Found ${redundantTimeEntries.length} redundant time entries`);

    if (!shouldDeleteRedundantTimeEntries) {
      return;
    }

    await io.runTask('delete-redundant-time-entries', async () => {
      const bexioClient = new BexioClient();
      for (const te of redundantTimeEntries) {
        await io.runTask(`delete-redundant-time-entry-${te.bexio_timesheet_id}`, async () => {
          const res = await bexioClient.deleteTimesheet(te.bexio_timesheet_id);

          if (isBexioError(res)) {
            throw new Error(`Failed to delete redundant time entry ${te.bexio_timesheet_id}: ${res.message}`);
          }
        });
      }
    });
  },
});
