import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { DropboxClient } from '@/data/dropbox-client';
import { ClickUpClient } from '@/data/clickup-client';
import { convertCronToUTC } from '@/lib/utils';

export const findProjectDropboxPathDifferences = client.defineJob({
  id: 'find-project-dropbox-path-differences',
  name: 'Find project Dropbox path differences',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('30 5 * * *') }), // every day at 5:30
  run: async (payload, io, _) => {
    const dropboxPaths = await io.runTask('fetch-dropbox-paths', async () => {
      const dropboxClient = new DropboxClient();
      const prefix = process.env.DROPBOX_FOLDER_PREFIX!;
      const response = await dropboxClient.getFolderContents(prefix, true);
      let firstList = response.result.entries
        .filter((e) => !!e)
        .map((entry) => String(entry.path_lower))
        .filter((path) => path.split('/').length - 1 <= 4);
      let hasMore = response.result.has_more;
      let cursor = response.result.cursor;
      while (hasMore) {
        const newResponse = await dropboxClient.getFolderContentsContinue(cursor);
        hasMore = newResponse.result.has_more;
        cursor = newResponse.result.cursor;
        firstList = firstList.concat(
          newResponse.result.entries
            .filter((e) => !!e)
            .map((entry) => String(entry.path_lower))
            .filter((path) => path.split('/').length - 1 <= 4),
        );
      }

      return firstList.filter(
        (path) =>
          !path.includes('zarchiv') &&
          !path.includes('zdeleted') &&
          !path.includes('/_ci') &&
          !path.includes('/marketing') &&
          !(path.split('/').length - 1 === 2),
      );
    });

    const dbPaths = await io.runTask('fetch-bexio-projects', async () => {
      const supabase = createClient();
      const { data: projects, error } = await supabase
        .from('projects')
        .select('dropbox_folder_path')
        .eq('is_client_project', true);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const { data: clients, error: clientError } = await supabase.from('clients').select('dropbox_folder_path');

      if (clientError) {
        throw new Error(`Error fetching clients: ${JSON.stringify(clientError)}`);
      }

      return projects
        .concat(clients)
        .filter((proj) => proj.dropbox_folder_path)
        .map((project) => project.dropbox_folder_path!);
    });

    const differences = await io.runTask('get-differences', async () => {
      return dropboxPaths.filter((path) => !dbPaths.includes(path));
    });

    await io.logger.info(`Found ${differences.length} differences`);

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    await io.runTask('notify-wrong-folders', async () => {
      const supabase = createClient();
      for (const difference of differences) {
        const { data, error } = await supabase
          .from('error_notifications')
          .select('message_hash')
          .eq('message_hash', `redundantFolder#${difference}`);

        if (error) {
          throw new Error(`Error fetching error notifications: ${JSON.stringify(error)}`);
        }

        if (data.length > 0) {
          continue;
        }

        await io.runTask(`notify-redundant-folder-${difference}`, async () => {
          const clickupClient = new ClickUpClient();
          await clickupClient.createTask(String(globalSettings.pm_list_id), {
            name: `it: Dropbox Kundenprojekte Ordner - manueller Ordner erstellt`,
            description: `Auf Dropbox im Ordner Kundenprojekte wurde fälschlicherweise ein Ordner von einem User manuell erstellt.
Ordnername: ${difference}.`,
            assignees: [process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          await supabase
            .from('error_notifications')
            .upsert(
              { message_hash: `redundantFolder#${difference}`, last_sent: new Date().toISOString() },
              { onConflict: 'message_hash' },
            );
        });
      }
    });
  },
});
