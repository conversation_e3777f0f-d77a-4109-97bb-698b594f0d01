import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { getClickupFoldersJob } from '@/jobs/shared/clickup/get-clickup-folders-job';
import { ClickUpFolder, isClickUpError } from '@/data/types/clickup.types';
import { ClickUpClient } from '@/data/clickup-client';
import { DropboxClient } from '@/data/dropbox-client';
import { buildDropboxClientFolderPath } from '@/lib/utils';
import { DropboxResponseError } from 'dropbox';
import { files } from 'dropbox/types/dropbox_types';
import { createClientDropboxFoldersJob } from '@/jobs/shared/dropbox/create-client-dropbox-folders-job';

export const importBexioContactsImportClickupFoldersJob = client.defineJob({
  id: 'import-bexio-contacts-import-clickup-folders',
  name: 'Import Bexio Contacts and import Clickup Folders',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH BEXIO CONTACTS
    await io.runTask('fetch-bexio-contacts', async () => {
      const bexioClient = new BexioClient();
      const contacts = await bexioClient.getContacts();

      if (!contacts || isBexioError(contacts) || contacts.length === 0) {
        throw new Error(`Error fetching Bexio contacts: ${JSON.stringify(contacts)}`);
      }

      const companyContacts = contacts.filter((contact) => contact.contact_type_id === 1);

      const clientsUpsert = companyContacts.map((contact: any) => {
        return {
          name: contact.name_1,
          bexio_contact_id: contact.id,
        };
      });

      const { error } = await supabaseServiceClient()
        .from('clients')
        .upsert(clientsUpsert, { onConflict: 'bexio_contact_id' });

      if (error) {
        throw new Error(`Error upserting Bexio contacts: ${JSON.stringify(error)}`);
      }
    });

    // 2 FETCH CLICKUP FOLDERS
    await io.runTask('fetch-clickup-folders', async () => {
      const foldersJob = await getClickupFoldersJob.invokeAndWaitForCompletion('get-folders', {});
      if (!foldersJob.ok) {
        throw new Error(`Error fetching folders: ${JSON.stringify(foldersJob)}`);
      }

      const { clientFolders } = foldersJob.output;
      // @ts-ignore
      const clientsUpsert = clientFolders.map((folder: ClickUpFolder) => {
        return {
          name: folder.name,
          clickup_folder_id: folder.id,
          clickup_archived: folder.archived || false,
          clickup_folder_assignee_id: folder.assignee?.id || null,
        };
      });

      const { error } = await supabaseServiceClient().from('clients').upsert(clientsUpsert, { onConflict: 'name' });

      if (error) {
        throw new Error(`Error upserting ClickUp folders: ${JSON.stringify(error)}`);
      }
    });

    // 3 CREATE MISSING ClICKUP FOLDERS
    await io.runTask('create-missing-clickup-folders', async () => {
      const missingClickUpFolders = await io.runTask('fetch-missing-folders', async () => {
        const supabase = createClient();
        const { data, error } = await supabase.from('clients').select('*').is('clickup_folder_id', null);

        if (error) {
          throw new Error(`Error fetching clients: ${JSON.stringify(error)}`);
        }

        return data || [];
      });

      for (const folder of missingClickUpFolders) {
        const clickupClient = new ClickUpClient();
        const supabase = createClient();
        await io.runTask(`create-clickup-folder-for-contact-${folder.bexio_contact_id}`, async () => {
          const createdFolder = await clickupClient.createFolder(folder.name);

          if (isClickUpError(createdFolder)) {
            throw new Error(`Error creating folder: ${JSON.stringify(createdFolder)}`);
          }

          const { error } = await supabase
            .from('clients')
            .update({
              clickup_folder_id: createdFolder.id,
            })
            .eq('id', folder.id);

          if (error) {
            throw new Error(`Error updating client in db: ${error}`);
          }
        });
      }
    });

    // 4 CREATE MISSING DROPBOX FOLDERS
    await io.runTask('create-missing-dropbox-folders', async () => {
      const missingDropboxFolders = await io.runTask('fetch-missing-folders', async () => {
        const supabase = createClient();
        const { data, error } = await supabase.from('clients').select('*').is('dropbox_folder_path', null);

        if (error) {
          throw new Error(`Error fetching clients: ${JSON.stringify(error)}`);
        }

        return data || [];
      });

      await createClientDropboxFoldersJob.invokeAndWaitForCompletion('create-dropbox-folders', {
        folders: missingDropboxFolders,
      });
    });
  },
});
