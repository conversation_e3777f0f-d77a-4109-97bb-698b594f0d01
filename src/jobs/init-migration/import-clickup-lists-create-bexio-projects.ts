import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { ClickUpClient } from '@/data/clickup-client';
import { convertUNIXTimestampToDateTimeString, stripProjectPrefix } from '@/lib/utils';
import { getClickUpListsJob } from '@/jobs/shared/clickup/get-clickup-lists-job';
import { isBexioError } from '@/data/types/bexio.types';
import { createOrRenameProjectDropBoxFoldersJob } from '@/jobs/shared/dropbox/create-or-rename-project-drop-box-folders-job';
import { ClickUpList, isClickUpError } from '@/data/types/clickup.types';

export const importClickupListsCreateBexioProjectsJob = client.defineJob({
  id: 'import-clickup-lists-create-bexio-projects',
  name: 'Import clickup lists and create bexio projects',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH ALL CLICKUP LISTS AND INSERT THEM INTO DB
    // @ts-ignore
    const { clientLists, nonClientLists } = await io.runTask<{
      clientLists: ClickUpList[];
      nonClientLists: ClickUpList[];
    }>('fetch-all-clickup-lists', async () => {
      // 1.1 FETCH ALL CLICKUP LISTS
      const listsJob = await getClickUpListsJob.invokeAndWaitForCompletion('get-clickup-lists', {});
      if (!listsJob.ok) {
        throw new Error(`Error fetching lists: ${JSON.stringify(listsJob)}`);
      }

      return listsJob.output;
    });

    // 1.2 INSERT CLICKUP LISTS INTO DB
    await io.runTask('insert-clickup-lists-into-db', async () => {
      const listsUpsert = clientLists.map((list) => {
        return {
          name: stripProjectPrefix(list.name),
          clickup_list_id: list.id,
          clickup_name: stripProjectPrefix(list.name),
          clickup_archived: list.archived,
          clickup_folder_id: list.folder.id,
          clickup_start: Number(list.start_date),
          clickup_end: list.due_date ? Number(list.due_date) : null,
          is_client_project: true,
        };
      });

      const allUpsert = nonClientLists
        .map((list) => {
          return {
            name: stripProjectPrefix(list.name),
            clickup_list_id: list.id,
            clickup_name: list.name,
            clickup_archived: list.archived,
            clickup_start: Number(list.start_date),
            clickup_end: list.due_date ? Number(list.due_date) : null,
            is_client_project: false, // skipping: clickup_folder_id
          };
        })
        .concat(listsUpsert);

      const { error: insertClickupListsError } = await createClient()
        .from('projects')
        .upsert(allUpsert, { onConflict: 'clickup_list_id' });

      if (insertClickupListsError) {
        throw new Error(`Error inserting lists: ${JSON.stringify(insertClickupListsError)}`);
      }
    });

    // 2 FETCH PROJECTS THAT NEED TO BE PUSHED TO BEXIO
    const projectsToBePushedToBexio = await io.runTask('get-all-projects-that-need-to-be-pushed-to-bexio', async () => {
      const { data, error } = await createClient()
        .from('projects')
        .select('*, clients(*), employees(*)')
        .is('bexio_project_id', null)
        .eq('deleted', false)
        .eq('is_client_project', true);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      if (!data) {
        await io.logger.info(`No projects to create`);
        return;
      }

      return data;
    });

    if (!projectsToBePushedToBexio) {
      await io.logger.info(`No projects to create`);
      return;
    }

    // 3 PUSH PROJECTS TO BEXIO
    await io.runTask('push-projects-to-bexio', async () => {
      for (const dbProject of projectsToBePushedToBexio) {
        if (!dbProject.clients || !dbProject.clients.bexio_contact_id) {
          await io.logger.log(`Skipping Project ${dbProject.clickup_list_id} - has no client`);
          continue;
        }

        await io.runTask(`create-bexio-project-${dbProject.clickup_list_id}`, async () => {
          const start = dbProject.clickup_start
            ? convertUNIXTimestampToDateTimeString(dbProject.clickup_start)
            : new Date().toISOString().split('T')[0];
          const end = dbProject.clickup_end ? convertUNIXTimestampToDateTimeString(dbProject.clickup_end) : null;
          const curProject = {
            name: stripProjectPrefix(String(dbProject.clickup_name)),
            start_date: start,
            end_date: end,
            pr_state_id: dbProject.clickup_archived ? 3 : 2, // archived = 3, active = 2
            pr_project_type_id: 2, // internal project = 1, external project = 2
            contact_id: dbProject.clients?.bexio_contact_id,
            user_id: dbProject.employees?.bexio_user_id || 1,
          };

          // 3.1 CREATE PROJECT IN BEXIO
          const bexioClient = new BexioClient();
          const createdProject = await bexioClient.createProject(curProject);

          if (isBexioError(createdProject)) {
            throw new Error(`Error creating project: ${JSON.stringify(createdProject)}`);
          }

          // 3.2 RENAME PROJECT (LIST) IN CLICKUP
          const newProjectName = `${createdProject.nr} ${curProject.name}`;
          const clickupClient = new ClickUpClient();
          const response = await clickupClient.updateList(Number(dbProject.clickup_list_id), {
            name: newProjectName,
          });
          if (isClickUpError(response)) {
            throw new Error(`Error renaming project in ClickUp: ${JSON.stringify(response)}`);
          }

          // 3.3 UPDATE PROJECT NAME IN DB WITH BEXIO PROJECT PREFIX
          //@ts-ignore
          const { error } = await supabaseServiceClient()
            .from('projects')
            .update({
              clickup_name: newProjectName,
              name: curProject.name,
              bexio_project_id: createdProject.id,
            })
            .eq('clickup_list_id', dbProject.clickup_list_id);

          if (error) {
            throw new Error(`Error updating project in db: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 4 FETCH PROJECTS THAT NEED A DROPBOX FOLDER
    const projectsThatNeedADropboxFolder = await io.runTask('get-all-projects-that-need-a-dropbox-folder', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*, clients(*)')
        .is('dropbox_folder_path', null)
        .eq('is_client_project', true);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      if (!data) {
        await io.logger.info(`No projects to create`);
        return [];
      }

      return data;
    });

    // 5 CREATE DROPBOX FOLDERS
    const dropBoxJob = await createOrRenameProjectDropBoxFoldersJob.invokeAndWaitForCompletion(
      'create-dropbox-folder-job',
      {
        projects: projectsThatNeedADropboxFolder,
      },
    );

    if (!dropBoxJob.ok) {
      throw new Error(`Error creating dropbox folders: ${JSON.stringify(dropBoxJob)}`);
    }
  },
});
