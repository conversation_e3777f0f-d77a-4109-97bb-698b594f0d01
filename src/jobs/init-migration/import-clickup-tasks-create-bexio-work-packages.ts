import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { isClickUpError } from '@/data/types/clickup.types';
import { ClickUpClient } from '@/data/clickup-client';
import { saveClickupTasksToDb } from '@/jobs/shared/clickup/save-clickup-tasks-to-db';

export const importClickupTasksCreateBexioWorkPackagesJob = client.defineJob({
  id: 'import-clickup-tasks-create-bexio-work-packages',
  name: 'Import clickup tasks and create bexio work packages',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { skipClientTasks = false, skipNonClientTasks = false, skipBexioPush = false } = payload;

    const [clientListIds, nonClientListIds] = await io.runTask('fetch-all-client-clickup-lists', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('clickup_list_id, is_client_project')
        .eq('deleted', false);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const clientListIds = data
        .filter((project) => project.is_client_project && project.clickup_list_id)
        .map((project) => project.clickup_list_id);
      const nonClientListIds = data
        .filter((project) => !project.is_client_project && project.clickup_list_id)
        .map((project) => project.clickup_list_id);
      return [clientListIds, nonClientListIds];
    });

    if (!skipClientTasks) {
      await io.runTask('fetch-all-client-clickup-tasks', async () => {
        // TODO REMOVE AFTER DEBUGGING
        return;

        let iter = 0;
        for (const listId of clientListIds) {
          iter++;
          if (iter % 100 === 0) {
            await io.wait(`yield-fetch-all-client-clickup-tasks-${iter}`, 10);
          }

          await io.runTask(`fetch-client-tasks-for-list-${listId}`, async () => {
            if (!listId) {
              await io.logger.info(`Missing list id for client project`);
              return;
            }

            let page = 0;
            let lastPage = false;
            while (!lastPage) {
              lastPage = await io.runTask(`fetch-client-list-${listId}-tasks-page-${page}`, async () => {
                const clickupClient = new ClickUpClient();
                const tasksResponse = await clickupClient.getTasksFromList(listId, page);

                if (isClickUpError(tasksResponse)) {
                  // TODO: add exception again?
                  await io.logger.error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
                  return true;
                }

                await saveClickupTasksToDb(tasksResponse.tasks, []);
                return tasksResponse.last_page;
              });
              page++;
            }
          });
        }
      });

      await io.runTask('fetch-all-archived-client-clickup-tasks', async () => {
        let iter = 0;
        for (const listId of clientListIds) {
          iter++;

          // TODO REMOVE AFTER DEBUGGING
          if (iter <= 174) {
            continue;
          }

          if (iter % 100 === 0) {
            await io.wait(`yield-fetch-all-archived-client-clickup-tasks-${iter}`, 10);
          }

          await io.runTask(`fetch-archived-client-tasks-for-list-${listId}`, async () => {
            if (!listId) {
              await io.logger.info(`Missing list id for client project`);
              return;
            }
            let page = 0;
            let lastPage = false;
            while (!lastPage) {
              lastPage = await io.runTask(`fetch-archived-clients-list-${listId}-tasks-page-${page}`, async () => {
                const clickupClient = new ClickUpClient();
                const tasksResponse = await clickupClient.getArchivedTasksFromList(listId, page);

                if (isClickUpError(tasksResponse)) {
                  // TODO: add exception again?
                  await io.logger.error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
                  return true;
                }

                await saveClickupTasksToDb(tasksResponse.tasks, []);
                return tasksResponse.last_page;
              });
              page++;
            }
          });
        }
      });
    }

    if (!skipNonClientTasks) {
      await io.runTask('fetch-all-non-client-clickup-tasks', async () => {
        const clickupClient = new ClickUpClient();
        for (const listId of nonClientListIds) {
          await io.runTask(`fetch-non-clients-tasks-for-list-${listId}`, async () => {
            if (!listId) {
              await io.logger.info(`Missing list id for client project`);
              return;
            }
            let page = 0;
            let lastPage = false;
            while (!lastPage) {
              lastPage = await io.runTask(`fetch-non-clients-list-${listId}-tasks-page-${page}`, async () => {
                const tasksResponse = await clickupClient.getTasksFromList(listId, page);

                if (isClickUpError(tasksResponse)) {
                  throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
                }

                await saveClickupTasksToDb([], tasksResponse.tasks);
                return tasksResponse.last_page;
              });
              page++;
            }
          });
        }
      });

      await io.runTask('fetch-all-non-client-archived-clickup-tasks', async () => {
        const clickupClient = new ClickUpClient();
        for (const listId of nonClientListIds) {
          await io.runTask(`fetch-archived-non-clients-tasks-for-list-${listId}`, async () => {
            if (!listId) {
              await io.logger.info(`Missing list id for client project`);
              return;
            }
            let page = 0;
            let lastPage = false;
            while (!lastPage) {
              lastPage = await io.runTask(`fetch-archived-non-clients-list-${listId}-tasks-page-${page}`, async () => {
                const tasksResponse = await clickupClient.getArchivedTasksFromList(listId, page);

                if (isClickUpError(tasksResponse)) {
                  throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
                }

                await saveClickupTasksToDb([], tasksResponse.tasks);
                return tasksResponse.last_page;
              });
              page++;
            }
          });
        }
      });
    }

    // 3 PUSH TASKS TO BEXIO
    if (!skipBexioPush) {
      await io.runTask('push-tasks-to-bexio', async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('tasks')
          .select('*, projects(*)')
          .eq('is_client_task', true)
          .is('bexio_work_package_id', null);

        if (error) {
          throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
        }

        if (!data) {
          await io.logger.info(`No tasks to create`);
          return;
        }

        for (const dbTask of data) {
          await io.runTask(`create-bexio-work-package-${dbTask.clickup_task_id}`, async () => {
            // 3.1 CREATE WORK PACKAGE
            const projectId = dbTask.projects?.bexio_project_id;
            const curWorkPackage = {
              name: trimLength(dbTask.name, 254), // TRIM TO 255 CHARS
              comment: trimLength(dbTask.clickup_task_description, 999), // TRIM TO 1000 CHARS
              estimated_time_in_hours: convertMillisecondsToHours(Number(dbTask.clickup_time_estimate)),
            };

            if (!projectId) {
              throw new Error(`Error creating work package: Project not found for task ${dbTask.clickup_task_id}`);
            }

            // 3.2 CREATE WORK PACKAGE IN BEXIO
            const bexioClient = new BexioClient();
            const createdWorkPackage = await bexioClient.createWorkPackage(projectId, curWorkPackage);

            if (isBexioError(createdWorkPackage) || !createdWorkPackage.id) {
              throw new Error(
                `Error creating work package: ${dbTask.clickup_task_id}: ${JSON.stringify(createdWorkPackage)}`,
              );
            }

            // 3.3 UPDATE TASK IN DB WITH BEXIO WORK PACKAGE ID
            const { error } = await supabaseServiceClient()
              .from('tasks')
              .update({
                bexio_work_package_id: createdWorkPackage.id,
              })
              .eq('clickup_task_id', dbTask.clickup_task_id);

            if (error) {
              throw new Error(`Error updating task ${dbTask.clickup_task_id} in db: ${JSON.stringify(error)}`);
            }
          });
        }
      });
    }
  },
});
