import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertUNIXTimestampToDateTimeString } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

export const importClickupTimeEntriesCreateBexioTimeEntriesJob = client.defineJob({
  id: 'import-clickup-time-entries-create-bexio-time-entries',
  name: 'Import clickup time entries and create bexio time entries',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const startUnixMs = 0;
    const endUnixMs = new Date().getTime();

    const nonClientSpaces = await io.runTask('fetch-all-clickup-spaces', async () => {
      const clickupClient = new ClickUpClient();
      const spacesResponse = await clickupClient.getSpaces();

      if ('err' in spacesResponse) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces
        .map((space) => space.id)
        .filter((spaceId) => spaceId !== process.env.CLICKUP_WORKSPACE_ID);
    });

    const employees = await io.runTask('fetch-all-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*');

      if (error) {
        throw new Error(`Error fetching employees: ${error}`);
      }

      return data;
    });

    await io.runTask('fetch-all-clickup-time-entries', async () => {
      const clickupClient = new ClickUpClient();
      for (const employee of employees) {
        await io.runTask(`fetch-billable-time-entries-for-employee-${employee.clickup_user_id}`, async () => {
          if (!employee.clickup_user_id) {
            return;
          }

          const timeEntries = await clickupClient.getBillableTimeEntriesRange(
            employee.clickup_user_id,
            startUnixMs,
            endUnixMs,
          );
          if (isClickUpError(timeEntries)) {
            throw new Error(
              `Error fetching billable time entries for employee ${employee.clickup_user_id}: ${JSON.stringify(timeEntries)}`,
            );
          }

          const billableTimeEntries = timeEntries.data.filter((timeEntry) => Number(timeEntry.duration) > 0);
          const timeEntriesUpsert = billableTimeEntries.map((timeEntry) => {
            return {
              clickup_time_entry_id: timeEntry.id,
              clickup_description: timeEntry.description,
              clickup_task_id: timeEntry.task?.id || null,
              clickup_user_id: timeEntry.user.id,
              clickup_duration: timeEntry.duration,
              clickup_start: timeEntry.start,
              clickup_end: timeEntry.end,
              clickup_task_tag: timeEntry.tags.length == 1 ? timeEntry.tags[0].name : 'null',
              billable: true,
            };
          });

          const { error } = await supabaseServiceClient()
            .from('time_entries')
            .upsert(timeEntriesUpsert, { onConflict: 'clickup_time_entry_id' });
          if (error) {
            throw new Error(`Error saving time entries to db: ${JSON.stringify(error)}`);
          }
        });

        for (const spaceId of nonClientSpaces) {
          await io.runTask(
            `fetch-non-billable-time-entries-for-employee-${employee.clickup_user_id}-space-${spaceId}`,
            async () => {
              if (!employee.clickup_user_id) {
                return;
              }

              const timeEntries = await clickupClient.getSpaceTimeEntriesRange(
                employee.clickup_user_id,
                spaceId,
                startUnixMs,
                endUnixMs,
              );
              if (isClickUpError(timeEntries)) {
                throw new Error(
                  `Error fetching non billable time entries for employee ${employee.clickup_user_id}: ${JSON.stringify(timeEntries)}`,
                );
              }

              const nonBillableTimeEntries = timeEntries.data.filter((timeEntry) => Number(timeEntry.duration) > 0);
              const timeEntriesUpsert = nonBillableTimeEntries.map((timeEntry) => {
                return {
                  clickup_time_entry_id: timeEntry.id,
                  clickup_description: timeEntry.description,
                  clickup_task_id: timeEntry.task?.id || null,
                  clickup_user_id: timeEntry.user.id,
                  clickup_duration: timeEntry.duration,
                  clickup_start: timeEntry.start,
                  clickup_end: timeEntry.end,
                  clickup_task_tag: timeEntry.tags.length == 1 ? timeEntry.tags[0].name : 'null',
                  billable: false,
                };
              });

              const { error } = await supabaseServiceClient()
                .from('time_entries')
                .upsert(timeEntriesUpsert, { onConflict: 'clickup_time_entry_id', ignoreDuplicates: true });
              if (error) {
                throw new Error(`Error saving time entries to db: ${JSON.stringify(error)}`);
              }
            },
          );
        }
      }
    });

    // 3 PUSH CLICKUP TIME ENTRIES TO BEXIO
    await io.runTask('push-clickup-time-entries-to-bexio', async () => {
      // 2 FETCH ALL CLICKUP TIME ENTRIES THAT NEED TO BE PUSHED TO BEXIO
      const supabase = createClient();
      const { data: timeEntries, error } = await supabase
        .from('time_entries')
        .select(
          '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
        )
        .is('bexio_timesheet_id', null)
        .is('billable', true);
      if (error) {
        throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
      }

      const bexioClient = new BexioClient();
      for (const timeEntry of timeEntries) {
        await io.runTask(`create-bexio-timesheet-${timeEntry.clickup_time_entry_id}`, async () => {
          if (
            !timeEntry.tasks ||
            !timeEntry.tasks.bexio_work_package_id ||
            !timeEntry.tasks.projects ||
            !timeEntry.tasks.projects.bexio_project_id
          ) {
            await io.logger.error(
              `Error creating bexio time entry: missing task or project: ${JSON.stringify(timeEntry.clickup_time_entry_id)}`,
            );
            return;
          }

          if (
            !timeEntry.employees ||
            !timeEntry.employees.bexio_user_id ||
            !timeEntry.business_activities ||
            !timeEntry.business_activities.bexio_business_activity_id
          ) {
            await io.logger.error(
              `Error creating bexio time entry: missing employee or business activity: ${JSON.stringify(
                timeEntry.clickup_time_entry_id,
              )}`,
            );
            return;
          }

          const timeEntryToPush = {
            user_id: timeEntry.employees.bexio_user_id || 1,
            client_service_id: timeEntry.business_activities.bexio_business_activity_id,
            text: timeEntry.tasks.name,
            allowable_bill: timeEntry.billable,
            pr_project_id: timeEntry.tasks.projects.bexio_project_id,
            pr_package_id: timeEntry.tasks.bexio_work_package_id,
            tracking: {
              type: 'range',
              start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
              end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
            },
          };

          // 3.1 CREATE TIME ENTRY IN BEXIO
          const bexioTimeEntry = await bexioClient.createTimesheet(timeEntryToPush);

          if (isBexioError(bexioTimeEntry) || !bexioTimeEntry.id) {
            throw new Error(`Error creating bexio time entry: ${JSON.stringify(bexioTimeEntry)}`);
          }

          // 3.2 SAVE BEXIO TIME ENTRY ID TO DB
          const { error } = await supabaseServiceClient().from('time_entries').upsert(
            {
              clickup_time_entry_id: timeEntry.clickup_time_entry_id,
              bexio_timesheet_id: bexioTimeEntry.id,
            },
            { onConflict: 'clickup_time_entry_id' },
          );

          if (error) {
            throw new Error(`Error upserting bexio time entry id to db: ${JSON.stringify(error)}`);
          }
        });
      }
    });
  },
});
