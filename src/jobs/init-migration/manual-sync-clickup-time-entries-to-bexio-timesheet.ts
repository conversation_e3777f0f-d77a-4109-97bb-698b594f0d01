import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertUNIXTimestampToDateTimeString, shallowEqualIgnoreProperties } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { getClickupTimeEntriesJob } from '@/jobs/shared/clickup/get-clickup-time-entries-job';
import { tempUnarchive } from '@/jobs/shared/bexio-projects/temp-unarchive';

export const manualSyncClickupTimeEntriesToBexioTimesheetJob = client.defineJob({
  id: 'manual-sync-clickup-time-entries-to-bexio-timesheet',
  name: 'Manual Sync clickup time entries to bexio timesheet',
  version: '0.0.1',
  trigger: invokeTrigger(), // every hour from 6am to 11pm
  run: async (payload, io, _) => {
    const { startUnixMs, endUnixMs } = payload;

    // 1 FETCH CLICKUP TIME ENTRIES (FROM 30 DAYS AGO until 24 HOURS AGO)
    const [timeEntriesToInsert, timeEntriesToUpdate, timeEntriesToDelete] = await io.runTask(
      'fetch-clickup-time-entries',
      async () => {
        // 1.1 FETCH CLICKUP TIME ENTRIES IN RANGE
        const job = await getClickupTimeEntriesJob.invokeAndWaitForCompletion('fetch-time-entries', {
          startMs: startUnixMs,
          endMs: endUnixMs,
        });
        if (!job.ok) {
          throw new Error(`Error fetching clickup time entries: ${JSON.stringify(job)}`);
        }

        const { billableTimeEntries, nonBillableTimeEntries } = job.output;

        // 1.2 FETCH TIME ENTRIES FROM DB BEFORE MUTATION (in range)
        const { data, error } = await createClient()
          .from('time_entries')
          .select('*')
          .gte('clickup_start', startUnixMs)
          .lte('clickup_end', endUnixMs);

        if (error) {
          throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
        }
        if (!data) {
          throw new Error(`No data returned from fetching time entries from db: ${JSON.stringify(data)}`);
        }
        const timeEntriesBeforeMutation = data || [];

        // 1.3 DETERMINE WHICH TIME ENTRIES TO INSERT, UPDATE OR DELETE
        return await io.runTask('determine-time-entries-to-insert-update-or-delete', async () => {
          const freshTimeEntries = billableTimeEntries.map((timeEntry) => {
            return {
              clickup_time_entry_id: timeEntry.id,
              clickup_description: timeEntry.description,
              clickup_task_id: timeEntry.task_id,
              clickup_user_id: timeEntry.user_id,
              clickup_duration: timeEntry.duration,
              clickup_start: timeEntry.start,
              clickup_end: timeEntry.end,
              clickup_task_tag: String(timeEntry.first_tag_name),
              billable: true,
            };
          });

          const allFreshTimeEntries = nonBillableTimeEntries
            .map((timeEntry) => {
              return {
                clickup_time_entry_id: timeEntry.id,
                clickup_description: timeEntry.description,
                clickup_task_id: timeEntry.task_id,
                clickup_user_id: timeEntry.user_id,
                clickup_duration: timeEntry.duration,
                clickup_start: timeEntry.start,
                clickup_end: timeEntry.end,
                clickup_task_tag: String(timeEntry.first_tag_name),
                billable: false,
              };
            })
            .concat(freshTimeEntries);

          // Fresh entries that are not in the db
          const timeEntriesToInsert = allFreshTimeEntries.filter((timeEntryBeforeMutation) => {
            return !timeEntriesBeforeMutation.some(
              (timeEntry) => timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id,
            );
          });

          // Fresh entries that in the db and are different than the db
          const timeEntriesToUpdate = allFreshTimeEntries.filter((timeEntryBeforeMutation) => {
            return timeEntriesBeforeMutation.some(
              (timeEntry) =>
                timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id &&
                !shallowEqualIgnoreProperties(timeEntryBeforeMutation, timeEntry, [
                  'id',
                  'bexio_timesheet_id',
                  'error_reminder_sent_date',
                ]),
            );
          });

          // Entries in the db that are not in fresh entries
          const timeEntriesToDelete = timeEntriesBeforeMutation.filter((timeEntryBeforeMutation) => {
            return !allFreshTimeEntries.some(
              (timeEntry) => timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id,
            );
          });

          return [timeEntriesToInsert, timeEntriesToUpdate, timeEntriesToDelete];
        });
      },
    );

    // 2.1 INSERT NEW CLICKUP TIME ENTRIES INTO DB AND PUSH TO BEXIO
    await io.runTask('insert-new-clickup-time-entries-into-db-and-bexio', async () => {
      const missingTE = await io.runTask('fetch-additional-missing-time-entries-from-db', async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('time_entries')
          .select(
            '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
          )
          .is('bexio_timesheet_id', null)
          .eq('billable', true);

        if (error) {
          throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
        }

        return data || [];
      });

      if (timeEntriesToInsert.length == 0 && missingTE.length == 0) {
        await io.logger.info(`No time entries to insert`);
        return;
      }

      await io.logger.info(`Inserting ${timeEntriesToInsert.length} new time entries`);
      await io.logger.info(`Inserting ${missingTE.length} missing time entries`);

      // 2.1.1 INSERT NEW CLICKUP TIME ENTRIES INTO DB
      let timeEntriesToBeCreated = await io.runTask('insert-new-clickup-time-entries-into-db', async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('time_entries')
          .insert(timeEntriesToInsert as any)
          .select(
            '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
          );

        if (error) {
          throw new Error(`Error saving time entries to db: ${JSON.stringify(error)}`);
        }

        return data.filter((timeEntry) => timeEntry.billable && timeEntry.tasks?.bexio_work_package_id);
      });

      timeEntriesToBeCreated = timeEntriesToBeCreated.concat(missingTE);

      // 2.1.2 PUSH NEW CLICKUP TIME ENTRIES TO BEXIO
      const bexioClient = new BexioClient();
      for (const timeEntry of timeEntriesToBeCreated) {
        await io.runTask(`push-new-clickup-time-entries-to-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
          if (!timeEntry.business_activities?.bexio_business_activity_id) {
            await io.logger.info(
              `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio business activity`,
            );
            return;
          }

          if (!timeEntry.tasks?.projects?.bexio_project_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio project`);
            return;
          }

          const timeSheet = {
            user_id: timeEntry.employees?.bexio_user_id || 1,
            client_service_id: timeEntry.business_activities?.bexio_business_activity_id,
            text: timeEntry.tasks?.name,
            allowable_bill: true,
            pr_project_id: timeEntry.tasks?.projects?.bexio_project_id,
            pr_package_id: timeEntry.tasks?.bexio_work_package_id,
            tracking: {
              type: 'range',
              start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
              end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
            },
          };

          let createdTimeSheet = await bexioClient.createTimesheet(timeSheet);
          if (isBexioError(createdTimeSheet)) {
            if (createdTimeSheet.error_code == 422 || createdTimeSheet.error_code == 404) {
              createdTimeSheet = await io.runTask(
                `unarchive-bexio-project-for-update-te-${timeEntry.clickup_time_entry_id}`,
                () =>
                  tempUnarchive(Number(timeEntry.tasks?.projects?.bexio_project_id), () =>
                    bexioClient.createTimesheet(timeSheet),
                  ),
              );

              if (isBexioError(createdTimeSheet)) {
                throw new Error(`Error pushing timesheet to bexio: ${JSON.stringify(createdTimeSheet)}`);
              }
            } else {
              throw new Error(`Error pushing timesheet to bexio: ${JSON.stringify(createdTimeSheet)}`);
            }
          }

          const supabase = createClient();
          const updatedTimeEntry = await supabase
            .from('time_entries')
            .update({ bexio_timesheet_id: createdTimeSheet.id })
            .eq('clickup_time_entry_id', String(timeEntry.clickup_time_entry_id));

          if (updatedTimeEntry.error) {
            throw new Error(`Error updating time entry in db: ${JSON.stringify(updatedTimeEntry.error)}`);
          }
        });
      }
    });

    // 2.2 UPDATE CLICKUP TIME ENTRIES IN DB
    await io.runTask('update-clickup-time-entries-in-db-and-bexio', async () => {
      if (timeEntriesToUpdate.length == 0) {
        await io.logger.info(`No time entries to update`);
        return;
      }

      await io.logger.info(`Updating ${timeEntriesToUpdate.length} time entries`);

      // 2.2.1 UPDATE CLICKUP TIME ENTRIES IN DB
      const savedTimeEntriesToUpdate = await io.runTask('update-clickup-time-entries-in-db', async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('time_entries')
          .upsert(timeEntriesToUpdate as any, { onConflict: 'clickup_time_entry_id' })
          .select(
            '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
          );
        if (error) {
          throw new Error(`Error updating time entry in db: ${JSON.stringify(error)}`);
        }

        return (
          data?.filter(
            (timeEntry) =>
              timeEntry.billable && timeEntry.clickup_time_entry_id && timeEntry.tasks?.bexio_work_package_id,
          ) || []
        );
      });

      // 2.2.2 PUSH UPDATED CLICKUP TIME ENTRIES TO BEXIO
      const bexioClient = new BexioClient();
      for (const timeEntry of savedTimeEntriesToUpdate) {
        await io.runTask(`push-updated-clickup-time-entries-to-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
          if (!timeEntry.business_activities?.bexio_business_activity_id) {
            await io.logger.info(
              `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio business activity`,
            );
            return;
          }

          if (!timeEntry.tasks?.projects?.bexio_project_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio project`);
            return;
          }

          if (!timeEntry.bexio_timesheet_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`);
            return;
          }

          const timeSheet = {
            user_id: timeEntry.employees?.bexio_user_id || 1,
            client_service_id: timeEntry.business_activities?.bexio_business_activity_id,
            text: timeEntry.tasks?.name,
            allowable_bill: true,
            pr_project_id: timeEntry.tasks?.projects?.bexio_project_id,
            pr_package_id: timeEntry.tasks?.bexio_work_package_id,
            tracking: {
              type: 'range',
              start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
              end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
            },
          };

          let updatedTimeSheet = await bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet);
          if (isBexioError(updatedTimeSheet)) {
            if (updatedTimeSheet.error_code == 422 || updatedTimeSheet.error_code == 404) {
              updatedTimeSheet = await io.runTask(
                `unarchive-bexio-project-for-update-te-${timeEntry.clickup_time_entry_id}`,
                () =>
                  tempUnarchive(Number(timeEntry.tasks?.projects?.bexio_project_id), () =>
                    bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet),
                  ),
              );

              if (isBexioError(updatedTimeSheet)) {
                throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
              }
            } else {
              throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
            }
          }
        });
      }
    });

    // 2.3 DELETE CLICKUP TIME ENTRIES FROM DB AND BEXIO
    await io.runTask('delete-clickup-time-entries-from-db-and-bexio', async () => {
      if (timeEntriesToDelete.length == 0) {
        await io.logger.info(`No time entries to delete`);
        return;
      }

      await io.logger.info(`Deleting ${timeEntriesToDelete.length} time entries`);
      await io.wait('wait-for-buffer', 30);

      // 2.3.1 DELETE CLICKUP TIME ENTRIES FROM DB
      const supabase = createClient();
      const { error } = await supabase
        .from('time_entries')
        .delete()
        .in(
          'clickup_time_entry_id',
          timeEntriesToDelete.map((timeEntry) => timeEntry.clickup_time_entry_id),
        );
      if (error) {
        throw new Error(`Error deleting time entry from db: ${JSON.stringify(error)}`);
      }

      // 2.3.2 DELETE CLICKUP TIME ENTRIES FROM BEXIO
      const bexioClient = new BexioClient();
      for (const timeEntry of timeEntriesToDelete) {
        await io.runTask(`delete-clickup-time-entries-from-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
          if (!timeEntry.bexio_timesheet_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`);
            return;
          }

          const deletedTimeSheet = await bexioClient.deleteTimesheet(Number(timeEntry.bexio_timesheet_id));
          if (isBexioError(deletedTimeSheet) || !deletedTimeSheet.success) {
            throw new Error(`Error deleting timesheet from bexio: ${JSON.stringify(deletedTimeSheet)}`);
          }
        });
      }
    });
  },
});
