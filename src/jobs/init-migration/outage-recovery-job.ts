import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import {
  getClickUpListsJob,
  importClickupTasksCreateBexioWorkPackagesJob,
  newClickupListCreateNewBexioProjectJob,
} from '@/jobs';

export const outageRecoveryJob = client.defineJob({
  id: 'outage-recovery-job',
  name: 'Outage Recovery Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // FETCH ALL CLICKUP LISTS
    const response = await getClickUpListsJob.invokeAndWaitForCompletion('get-clickup-lists', {});
    if (!response.ok) {
      throw new Error(`Error fetching clickup lists: ${response.error}`);
    }
    const { clientLists, nonClientLists } = response.output;
    const allLists = clientLists.concat(nonClientLists);

    // FETCH ALL DB PROJECTS
    const projects = await io.runTask('fetch-db-projects', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('projects').select('*').eq('deleted', false);

      if (error) {
        throw Error(`Error fetching projects: ${error.message}`);
      }

      return data || [];
    });

    // CHECK IF CLICKUP LISTS THAT ARE MISSING IN DB
    const missingListIds = await io.runTask('check-for-differences', async () => {
      const missingLists: string[] = [];
      for (const list of allLists) {
        const listInDb = projects.find((p) => p.clickup_list_id === list.id);
        if (!listInDb) {
          missingLists.push(list.id);
        }
      }
      return missingLists;
    });

    await io.runTask('create-missing-projects', async () => {
      for (const listId of missingListIds) {
        await newClickupListCreateNewBexioProjectJob.invokeAndWaitForCompletion(
          'new-clickup-list-create-new-bexio-project',
          {
            newClickupListId: listId,
          },
        );
      }
    });

    await importClickupTasksCreateBexioWorkPackagesJob.invokeAndWaitForCompletion(
      'import-clickup-tasks-create-bexio-work-packages',
      {},
    );
  },
});
