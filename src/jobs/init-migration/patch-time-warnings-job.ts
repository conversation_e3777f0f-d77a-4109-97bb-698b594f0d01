import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask, isClickUpError } from '@/data/types/clickup.types';

export const patchTimeWarningsJob = client.defineJob({
  id: 'patch-time-warnings-job',
  name: 'Patch Time Warnings Job',
  version: '0.0.3',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const TasksToDelete = await io.runTask('fetch-tasks-to-delete', async () => {
      const clickupClient = new ClickUpClient();
      let page = 0;
      let lastPage = false;
      let tasks: ClickUpTask[] = [];
      while (!lastPage) {
        if (page % 10 === 0) {
          await io.wait(`yield-delete-tasks-${page}`, 1);
        }

        const tasksResponse = await io.runTask(`fetch-tasks-page-${page}`, async () => {
          const tasksResponse = await clickupClient.getCustomTasksQuery(
            globalSettings.false_timeentries_list_id!,
            page,
            {
              include_closed: false,
              subtasks: false,
            },
          );

          if (isClickUpError(tasksResponse)) {
            throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
          }
          await io.logger.info(`Fetched ${tasks.length} tasks`);
          return tasksResponse;
        });

        tasks = tasks.concat(tasksResponse.tasks);
        lastPage = tasksResponse.last_page;
        page++;
      }

      const anHourAgo = new Date(Date.now() - 2 * 60 * 60 * 1000 * 1000).getTime();
      return tasks.filter((task) => task.due_date && Number(task.due_date) >= anHourAgo).map((task) => task.id);
    });

    await io.logger.info(`Found ${TasksToDelete.length} tasks to delete`);

    await io.runTask('delete-tasks', async () => {
      const clickupClient = new ClickUpClient();

      let iter = 0;
      for (const taskId of TasksToDelete) {
        if (++iter % 100 === 0) {
          await io.wait(`yield-delete-tasks-${iter}`, 1);
        }

        await io.runTask(`delete-task-${taskId}`, async () => {
          const res = await clickupClient.deleteTask(taskId);
          if (isClickUpError(res)) {
            throw new Error(`Error deleting task: ${JSON.stringify(res)}`);
          }
        });
      }
    });
  },
});
