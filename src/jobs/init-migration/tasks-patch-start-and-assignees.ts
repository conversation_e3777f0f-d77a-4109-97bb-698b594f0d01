import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask } from '@/data/types/clickup.types';

// Minimal task data to reduce memory usage
type TaskStartAndAssigneesData = {
  id: string;
  start_date: number | null;
  assignees: number[];
};

export const tasksPatchStartAndAssigneesJob = client.defineJob({
  id: 'tasks-patch-start-and-assignees-job',
  name: 'Tasks Patch Start Date and Assignees Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const clickupClient = new ClickUpClient();

    // 1. FETCH ALL SPACES
    const spaceIds = await io.runTask('fetch-all-clickup-spaces', async () => {
      const spacesResponse = await clickupClient.getSpaces();

      if ('err' in spacesResponse) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces.map((space) => space.id);
    });

    await io.logger.info(`Found ${spaceIds.length} spaces to process`);

    // 2. FETCH ALL TASKS FROM ALL SPACES AND COLLECT MINIMAL DATA
    let allTasksWithStartAndAssignees: TaskStartAndAssigneesData[] = [];

    for (const spaceId of spaceIds) {
      const minTasksFromSpace = await io.runTask(`fetch-tasks-from-space-${spaceId}`, async () => {
        let page = 0;
        let lastPage = false;
        let tasksFromSpace = 0;
        let minTasks: TaskStartAndAssigneesData[] = [];

        while (!lastPage) {
          const [minimalTasks, isLastPage] = await io.runTask(`fetch-space-${spaceId}-page-${page}`, async () => {
            const tasksResponse = await clickupClient.getTasksOfSpaces(page, [spaceId]);

            if ('err' in tasksResponse) {
              throw new Error(
                `Error fetching tasks from space ${spaceId}: ${JSON.stringify(tasksResponse)} at page ${page}`,
              );
            }

            // Extract only the minimal data we need immediately
            const minimalTasks: TaskStartAndAssigneesData[] = tasksResponse.tasks.map((task: ClickUpTask) => ({
              id: task.id,
              start_date: task.start_date ? Number(task.start_date) : null,
              assignees: task.assignees.map(assignee => assignee.id),
            }));

            return [minimalTasks, tasksResponse.last_page];
          });

          minTasks = minTasks.concat(minimalTasks);
          tasksFromSpace += minimalTasks.length;

          await io.logger.info(`Fetched ${minimalTasks.length} tasks from space ${spaceId}, page ${page}`);

          lastPage = isLastPage;
          page++;
        }

        await io.logger.info(`Total tasks from space ${spaceId}: ${tasksFromSpace}`);
        return minTasks;
      });

      allTasksWithStartAndAssignees = allTasksWithStartAndAssignees.concat(minTasksFromSpace);
    }

    await io.logger.info(`Total tasks fetched from ClickUp: ${allTasksWithStartAndAssignees.length}`);

    // 3. UPDATE ALL TASKS IN DATABASE WITH SINGLE UPSERT
    await io.runTask('bulk-upsert-tasks-with-start-and-assignees', async () => {
      const supabase = createClient();

      // Prepare data for bulk upsert
      const upsertData = allTasksWithStartAndAssignees.map((taskData) => ({
        clickup_task_id: taskData.id,
        clickup_start_date: taskData.start_date,
        clickup_assignees: taskData.assignees,
      }));

      const { error } = await supabase.from('tasks').upsert(upsertData, {
        onConflict: 'clickup_task_id',
      });

      if (error) {
        throw new Error(`Error bulk updating tasks with start dates and assignees: ${JSON.stringify(error)}`);
      }

      await io.logger.info(`Processing complete. Bulk upserted ${allTasksWithStartAndAssignees.length} tasks with start dates and assignees`);
    });
  },
});
