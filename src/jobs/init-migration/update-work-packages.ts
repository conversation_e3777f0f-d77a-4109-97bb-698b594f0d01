import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';

export const updateWorkPackagesJob = client.defineJob({
  id: 'update-work-packages-job',
  name: 'Update work packages in Bexio',
  version: '0.0.1',
  trigger: invokeTrigger(), // every hour from 6am to 11pm
  run: async (payload, io, ctx) => {
    await io.runTask('update-tasks-to-bexio', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('tasks').select('*, projects(*)').eq('is_client_task', true);

      if (error) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
      }

      if (!data) {
        await io.logger.info(`No tasks to create`);
        return;
      }

      for (const dbTask of data) {
        await io.runTask(`update-bexio-work-package-${dbTask.clickup_task_id}`, async () => {
          if (!dbTask.projects?.bexio_project_id || !dbTask.bexio_work_package_id) {
            await io.logger.info(`No bexio project or work package id found for task ${dbTask.clickup_task_id}`);
            return;
          }

          // 3.1 UPDATE WORK PACKAGE
          const projectId = dbTask.projects?.bexio_project_id;
          const workPackageId = Number(dbTask.bexio_work_package_id);
          const curWorkPackage = {
            name: trimLength(dbTask.name, 254), // TRIM TO 255 CHARS
            comment: trimLength(dbTask.clickup_task_description, 999), // TRIM TO 1000 CHARS
            estimated_time_in_hours: convertMillisecondsToHours(Number(dbTask.clickup_time_estimate)),
          };

          if (!projectId) {
            throw new Error(`Error creating work package: Project not found for task ${dbTask.clickup_task_id}`);
          }

          // 3.2 UPDATE WORK PACKAGE IN BEXIO
          const bexioClient = new BexioClient();
          const createdWorkPackage = await bexioClient.updateWorkPackage(projectId, workPackageId, curWorkPackage);

          if (isBexioError(createdWorkPackage)) {
            // @ts-ignore
            if (!createdWorkPackage.error_code === 404) {
              throw new Error(
                `Error updating work package: ${dbTask.clickup_task_id}: ${JSON.stringify(createdWorkPackage)}`,
              );
            }
          }
        });
      }
    });
  },
});
