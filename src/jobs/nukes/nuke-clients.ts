import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { supabaseServiceClient } from '@/data/supabase-server';

// @ts-ignore
export const nukeClientsJob = client.defineJob({
  id: 'nuke-clients',
  name: 'Nuke Clients',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    await io.runTask('delete-all-clients-from-db', async () => {
     const { error } = await supabaseServiceClient().from('clients').delete().gte('id', 0);

      if (error) {
        throw new Error(`Error deleting clients from db: ${JSON.stringify(error)}`);
      }
    });
  },
});