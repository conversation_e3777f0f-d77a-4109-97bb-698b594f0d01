import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { DropboxClient } from '@/data/dropbox-client';
import { isBexioError } from '@/data/types/bexio.types';
import { buildDropboxProjectFolderPath } from '@/lib/utils';

// @ts-ignore
export const nukeProjectsJob = client.defineJob({
  id: 'nuke-projects',
  name: 'Nuke Projects',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH ALL PROJECTS
    const projects = await io.runTask('fetch-projects-from-db', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('projects').select('*, clients(*)').eq('is_client_project', true);

      if (error) {
        throw new Error(`Error fetching bexio projects: ${JSON.stringify(error)}`);
      }

      if (!data || data.length === 0) {
        return [];
      }

      return data;
    });

    // 2 DELETE ALL BEXIO PROJECTS
    await io.runTask('delete-all-bexio-projects', async () => {
      const bexioClient = new BexioClient();
      for (const entry of projects) {
        if (!entry.bexio_project_id) {
          continue;
        }

        await io.runTask(`delete-bexio-project-${entry.bexio_project_id}`, async () => {
          // 2.1 DELETE PROJECT
          const result = await bexioClient.deleteProject(Number(entry.bexio_project_id));

          if (isBexioError(result)) {
            throw new Error(`Error deleting project: ${JSON.stringify(result)}`);
          }

          // 2.2 MARK PROJECT AS DELETED
          // @ts-ignore
          const { error } = await supabaseServiceClient()
            .from('projects')
            .update({ bexio_project_id: null })
            .match({ bexio_project_id: entry.bexio_project_id });

          if (error) {
            throw new Error(`Error updating deleted project: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 3 DELETE ALL DROPBOX FOLDERS
    await io.runTask('delete-all-dropbox-folders', async () => {
      const dropboxClient = new DropboxClient();
      for (const entry of projects) {
        await io.runTask(`delete-dropbox-folder-${entry.dropbox_folder_path}`, async () => {
          await io.try(
            async () => {
              if (!entry.dropbox_folder_path || !entry.clients) {
                return;
              }

              await dropboxClient.deleteFolder(entry.dropbox_folder_path);
            },
            async (error) => {
              // @ts-ignore
              if (error?.status >= 400 && error?.status != 409) {
                throw new Error(`Error deleting folder: ${JSON.stringify(error)}`);
              }
            },
          );

          // 3.2 MARK FOLDER AS DELETED
          const { error } = await supabaseServiceClient()
            .from('projects')
            .update({ dropbox_folder_path: null })
            .match({ dropbox_folder_path: entry.dropbox_folder_path });

          if (error) {
            throw new Error(`Error updating deleted folder: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 4 DELETE ALL PROJECTS
    await io.runTask('delete-all-deleted-projects-from-db', async () => {
      const { error } = await supabaseServiceClient().from('projects').delete().is('bexio_project_id', null);

      if (error) {
        throw new Error(`Error deleting projects: ${JSON.stringify(error)}`);
      }
    });
  },
});
