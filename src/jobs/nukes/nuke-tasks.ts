import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';

export const nukeTasksJob = client.defineJob({
  id: 'nuke-tasks',
  name: 'Nuke Tasks',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH ALL TASKS
    const tasks = await io.runTask('fetch-db-tasks', async () => {
      const { data, error } = await supabaseServiceClient()
        .from('tasks')
        .select('bexio_work_package_id, projects(bexio_project_id)');

      if (error) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
      }

      if (!data || data.length === 0) {
        return [];
      }

      return data;
    });

    // 2 DELETE ALL BEXIO WORK PACKAGES
    await io.runTask('delete-all-bexio-tasks', async () => {
      const bexioClient = new BexioClient();
      for (const entry of tasks) {
        if (!entry.bexio_work_package_id) {
          continue;
        }

        // @ts-ignore
        if (!entry.projects || !entry.projects.bexio_project_id) {
          continue;
        }

        await io.runTask(`delete-bexio-work package-${entry.bexio_work_package_id}`, async () => {
          // 2.1 DELETE WORK PACKAGE
          const result = await bexioClient.deleteWorkPackage(
            // @ts-ignore
            entry.projects.bexio_project_id,
            entry.bexio_work_package_id,
          );

          if (isBexioError(result)) {
            throw new Error(`Error deleting work package: ${JSON.stringify(result)}`);
          }

          // 2.2 MARK TASK AS DELETED
          // @ts-ignore
          const { error } = await supabaseServiceClient()
            .from('tasks')
            .update({ bexio_work_package_id: null })
            .match({ bexio_work_package_id: entry.bexio_work_package_id });

          if (error) {
            throw new Error(`Error updating task: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 3 DELETE ALL TASKS
    await io.runTask('delete-all-deleted-work-packages-from-db', async () => {
      const { error } = await supabaseServiceClient().from('tasks').delete().is('bexio_work_package_id', null);

      if (error) {
        throw new Error(`Error deleting tasks: ${JSON.stringify(error)}`);
      }
    });
  },
});
