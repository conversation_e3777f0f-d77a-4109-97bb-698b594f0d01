import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';

// @ts-ignore
export const nukeTimeEntriesJob = client.defineJob({
  id: 'nuke-time-entries',
  name: 'Nuke Time Entries',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // 1 FETCH ALL TIME ENTRIES
    const entries = await io.runTask('fetch-time-entries', async () => {
      const { data, error } = await supabaseServiceClient().from('time_entries').select('bexio_timesheet_id');

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      if (!data || data.length === 0) {
        return [];
      }

      return data;
    });

    // 2 DELETE ALL BEXIO TIME SHEETS
    await io.runTask('delete-all-bexio-timesheet', async () => {
      const bexioClient = new BexioClient();
      for (const entry of entries) {
        if (!entry.bexio_timesheet_id) {
          continue;
        }

        await io.runTask(`delete-bexio-timesheet-${entry.bexio_timesheet_id}`, async () => {
          // 2.1 DELETE TIMESHEET
          const result = await bexioClient.deleteTimesheet(entry.bexio_timesheet_id);

          if (isBexioError(result)) {
            throw new Error(`Error deleting timesheet: ${JSON.stringify(result)}`);
          }

          // 2.2 MARK TIME ENTRY AS DELETED
          // @ts-ignore
          const { error } = await supabaseServiceClient()
            .from('time_entries')
            .update({ bexio_timesheet_id: null })
            .match({ bexio_timesheet_id: entry.bexio_timesheet_id });

          if (error) {
            throw new Error(`Error updating time entry: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 3 DELETE ALL TIME ENTRIES
    await io.runTask('delete-all-deleted-time-entries-from-db', async () => {
      const { error } = await supabaseServiceClient().from('time_entries').delete().is('bexio_timesheet_id', null);

      if (error) {
        throw new Error(`Error deleting time entries: ${JSON.stringify(error)}`);
      }
    });
  },
});
