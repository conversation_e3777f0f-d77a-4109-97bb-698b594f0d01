import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { createClient } from '@/data/supabase-server';

export async function tempUnarchive<T>(bexioProjectId: number, action: () => Promise<T>) {
  const bexioClient = new BexioClient();
  const supabase = createClient();
  const unarchiveResponse = await bexioClient.updateProject(bexioProjectId, {
    pr_state_id: 2,
  });

  if (isBexioError(unarchiveResponse)) {
    throw new Error(`Error unarchiving project in bexio: ${JSON.stringify(unarchiveResponse)}`);
  }

  try {
    const result = await action();
    if (isBexioError(result)) {
      throw new Error(`Error archiving project in bexio: ${JSON.stringify(result)}`);
    }

    await bexioClient.updateProject(bexioProjectId, {
      pr_state_id: 3,
    });
    await supabase
      .from('bexio_projects')
      .update({
        status: 'Archiviert',
        last_temp_unarchive: new Date().toISOString(),
      })
      .eq('bexio_id', bexioProjectId);

    return result;
  } catch (error) {
    await bexioClient.updateProject(bexioProjectId, {
      pr_state_id: 3,
    });
    await supabase
      .from('bexio_projects')
      .update({
        status: 'Archiviert',
        last_temp_unarchive: new Date().toISOString(),
      })
      .eq('bexio_id', bexioProjectId);

    throw error;
  }
}
