import { IO } from '@trigger.dev/sdk';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

export type PartialClickUpList = { id: string; name: string; archived: boolean; folder: { id: string; name: string } };

export async function getClickupClientLists(io: IO) {
  // ONLY ACTIVE CLIENT LISTS (NO ARCHIVED OR NON-CLIENT LISTS)
  const lists: PartialClickUpList[] = await io.runTask('fetch-all-client-clickup-folders', async () => {
    const clickupClient = new ClickUpClient();
    const folderResponse = await clickupClient.getAllFolders();

    if (isClickUpError(folderResponse)) {
      throw new Error(`Error fetching lists: ${JSON.stringify(folderResponse)}`);
    }

    return folderResponse.folders.flatMap((folder) =>
      folder.lists.map((list) => {
        return {
          id: list.id,
          name: list.name,
          archived: list.archived,
          folder: {
            id: folder.id,
            name: folder.name,
          },
        };
      }),
    );
  });

  return lists;
}
