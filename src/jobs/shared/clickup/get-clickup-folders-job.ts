import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpFolder, isClickUpError } from '@/data/types/clickup.types';

export const getClickupFoldersJob = client.defineJob({
  id: 'get-clickup-folders-job',
  name: 'Get ClickUp Folders Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    // @ts-ignore
    const clientFolders: ClickUpFolder[] = await io.runTask('fetch-all-clickup-folders', async () => {
      const clickupClient = new ClickUpClient();
      const folderResponse = await clickupClient.getAllFolders();
      const archivedFolderResponse = await clickupClient.getAllArchivedFolders();

      if (isClickUpError(folderResponse) || isClickUpError(archivedFolderResponse)) {
        throw new Error(
          `Error fetching lists: ${JSON.stringify(folderResponse)} ${JSON.stringify(archivedFolderResponse)}`,
        );
      }

      return folderResponse.folders.concat(archivedFolderResponse.folders);
    });

    const nonClientSpaces = await io.runTask('fetch-all-clickup-spaces', async () => {
      const clickupClient = new ClickUpClient();
      const spacesResponse = await clickupClient.getSpaces();

      if (isClickUpError(spacesResponse)) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces
        .map((space) => space.id)
        .filter((spaceId) => spaceId !== process.env.CLICKUP_WORKSPACE_ID);
    });

    // @ts-ignore
    const nonClientFolders: ClickUpFolder[] = await io.runTask('fetch-all-non-client-clickup-folders', async () => {
      let nonClientFolders: ClickUpFolder[] = [];
      const clickupClient = new ClickUpClient();
      for (const spaceId of nonClientSpaces) {
        // @ts-ignore
        const curFolders: ClickUpFolder[] = await io.runTask(`fetch-all-folders-of-space-${spaceId}`, async () => {
          const folderResponse = await clickupClient.getAllFoldersFromSpace(spaceId);
          const archivedFolderResponse = await clickupClient.getAllArchivedFoldersFromSpace(spaceId);

          if (isClickUpError(folderResponse) || isClickUpError(archivedFolderResponse)) {
            throw new Error(
              `Error fetching lists: ${JSON.stringify(folderResponse)} ${JSON.stringify(archivedFolderResponse)}`,
            );
          }

          return folderResponse.folders.concat(archivedFolderResponse.folders);
        });

        nonClientFolders = nonClientFolders.concat(curFolders);
      }

      return nonClientFolders;
    });

    // @ts-ignore
    const nonClientHiddenFolders: ClickUpFolder[] = await io.runTask('fetch-non-client-hidden-folders', async () => {
      const clickupClient = new ClickUpClient();
      let nonClientHiddenFolders: ClickUpFolder[] = [];
      for (const spaceId of nonClientSpaces) {
        const curPartialFolders: Partial<ClickUpFolder>[] = await io.runTask(
          `fetch-all-hidden-folders-of-space-${spaceId}`,
          async () => {
            const lists = await clickupClient.getListsFromSpace(spaceId);
            const archivedLists = await clickupClient.getArchivedListsFromSpace(spaceId);

            if (isClickUpError(lists) || isClickUpError(archivedLists)) {
              throw new Error(`Error fetching lists: ${JSON.stringify(lists)} ${JSON.stringify(archivedLists)}`);
            }

            const folders = lists.lists.map((li) => li.folder);
            const archivedFolders = archivedLists.lists.map((li) => li.folder);
            return folders.concat(archivedFolders);
          },
        );

        for (const partialFolder of curPartialFolders) {
          // @ts-ignore
          const curFolder: ClickUpFolder | null = await io.runTask(`fetch-folder-${partialFolder.id}`, async () => {
            if (!partialFolder.id) {
              await io.logger.error(`Error fetching folder: ${JSON.stringify(partialFolder)}`);
              return null;
            }

            const folderResponse = await clickupClient.getFolder(partialFolder.id);
            if (isClickUpError(folderResponse)) {
              throw new Error(`Error fetching folder: ${JSON.stringify(folderResponse)}`);
            }
            return folderResponse;
          });

          if (curFolder) {
            nonClientHiddenFolders.push(curFolder);
          }
        }
      }

      return nonClientHiddenFolders;
    });

    const allNonClientFolders = nonClientFolders.concat(nonClientHiddenFolders);

    return { clientFolders, nonClientFolders: allNonClientFolders };
  },
});
