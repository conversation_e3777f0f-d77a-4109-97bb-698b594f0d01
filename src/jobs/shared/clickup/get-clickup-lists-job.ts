import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpFolder, ClickUpList, isClickUpError } from '@/data/types/clickup.types';
import { getClickupFoldersJob } from '@/jobs/shared/clickup/get-clickup-folders-job';

export const getClickUpListsJob = client.defineJob({
  id: 'get-clickup-lists-job',
  name: 'Get ClickUp Lists Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const { skipNonClientLists } = payload;

    // 1 GET ALL CLICKUP FOLDERS
    // @ts-ignore
    const folders: ClickUpFolder[] = await io.runTask('fetch-all-clickup-folders', async () => {
      const jobRun = await getClickupFoldersJob.invokeAndWaitForCompletion('get-folders', {});
      if (!jobRun.ok) {
        throw new Error(`Error fetching folders: ${JSON.stringify(jobRun)}`);
      }

      if (skipNonClientLists) {
        return jobRun.output.clientFolders;
      }

      const { clientFolders, nonClientFolders } = jobRun.output;
      return clientFolders.concat(nonClientFolders);
    });

    // 2 GET ALL CLICKUP LISTS
    // @ts-ignore
    const lists: ClickUpList[] = await io.runTask('fetch-all-clickup-lists', async () => {
      let lists: ClickUpList[] = [];
      let iter = 0;
      for (const folder of folders) {
        iter++;

        if (iter % 100 === 0) {
          await io.wait(`yield-fetch-all-clickup-lists-${iter}`, 1);
        }

        // @ts-ignore
        const curList: ClickUpList[] = await io.runTask(`fetch-clickup-lists-for-folder-${folder.id}`, async () => {
          const clickupClient = new ClickUpClient();
          const listResponse = await clickupClient.getLists(Number(folder.id));
          const archivedListsResponse = await clickupClient.getArchivedLists(Number(folder.id));

          if (isClickUpError(listResponse) || isClickUpError(archivedListsResponse)) {
            throw new Error(
              `Error fetching lists: ${JSON.stringify(listResponse)} ${JSON.stringify(archivedListsResponse)}`,
            );
          }

          if (!listResponse.lists || !archivedListsResponse.lists) {
            throw new Error(
              `Error fetching lists: ${JSON.stringify(listResponse)} ${JSON.stringify(archivedListsResponse)}`,
            );
          }

          return listResponse.lists.concat(archivedListsResponse.lists);
        });

        lists = lists.concat(curList);
      }

      return lists;
    });

    const clientLists = lists.filter((list) => list.space.id === process.env.CLICKUP_WORKSPACE_ID);
    const nonClientLists = lists.filter((list) => list.space.id !== process.env.CLICKUP_WORKSPACE_ID);

    return { clientLists, nonClientLists };
  },
});
