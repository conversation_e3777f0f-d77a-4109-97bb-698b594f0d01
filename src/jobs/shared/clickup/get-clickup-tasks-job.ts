import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask } from '@/data/types/clickup.types';

export const getClickupTasksJob = client.defineJob({
  id: 'get-clickup-tasks-job',
  name: 'Get Clickup Tasks Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const clientTasks = await io.runTask('fetch-all-client-clickup-tasks', async () => {
      let clickupTasks: ClickUpTask[] = [];
      let curTasks: ClickUpTask[] = [];
      let page = 0;
      let lastPage = false;
      while (!lastPage) {
        [curTasks, lastPage] = await io.runTask(`fetch-tasks-page-${page}`, async () => {
          const clickupClient = new ClickUpClient();
          const tasksResponse = await clickupClient.getTasks(page);

          if ('err' in tasksResponse) {
            throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
          }

          return [tasksResponse.tasks, tasksResponse.last_page];
        });

        clickupTasks = clickupTasks.concat(curTasks);
        await io.logger.info(`Ran task fetch-tasks-page-${page} with ${curTasks.length} tasks`);
        page++;
      }

      return clickupTasks;
    });

    const spaceIds = await io.runTask('fetch-all-clickup-spaces', async () => {
      const clickupClient = new ClickUpClient();
      const spacesResponse = await clickupClient.getSpaces();

      if ('err' in spacesResponse) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces.map((space) => space.id);
    });

    const nonClientSpaces = spaceIds.filter((spaceId) => spaceId !== process.env.CLICKUP_WORKSPACE_ID);

    const nonClientTasks = await io.runTask('fetch-all-clickup-tasks', async () => {
      let clickupTasks: ClickUpTask[] = [];
      let curTasks: ClickUpTask[] = [];
      let page = 0;
      let lastPage = false;
      while (!lastPage) {
        [curTasks, lastPage] = await io.runTask(`fetch-tasks-page-${page}`, async () => {
          const clickupClient = new ClickUpClient();
          const tasksResponse = await clickupClient.getTasksOfSpaces(page, nonClientSpaces);

          if ('err' in tasksResponse) {
            throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
          }

          return [tasksResponse.tasks, tasksResponse.last_page];
        });

        clickupTasks = clickupTasks.concat(curTasks);
        await io.logger.info(`Ran task fetch-tasks-page-${page} with ${curTasks.length} tasks`);
        page++;
      }

      return clickupTasks;
    });

    return { clientTasks, nonClientTasks };
  },
});
