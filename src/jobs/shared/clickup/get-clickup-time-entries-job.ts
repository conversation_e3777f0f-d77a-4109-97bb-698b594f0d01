import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { isClickUpError } from '@/data/types/clickup.types';

export type TimeEntryLight = {
  id: string;
  description: string;
  task_id: string;
  task_name: string;
  list_name: string;
  user_id: number;
  duration: string;
  start: string;
  end: string;
  tags_length: number;
  first_tag_name: string | null;
};

export const getClickupTimeEntriesJob = client.defineJob({
  id: 'get-clickup-time-entries-job',
  name: 'Get CLickUp Time Entries Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { startMs, endMs } = payload;

    const employees = await io.runTask('fetch-all-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*');

      if (error) {
        throw new Error(`Error fetching employees: ${error}`);
      }

      return data;
    });

    const billableTimeEntriesV2 = await io.runTask('fetch-all-billable-time-entries-v2', async () => {
      const clickupClient = new ClickUpClient();
      const assigneeIds = employees
        .filter((e) => e.clickup_user_id)
        .map((e) => e.clickup_user_id)
        .join(',');

      const timeEntries = await clickupClient.getBillableTimeEntriesRange(assigneeIds, startMs, endMs);

      if (isClickUpError(timeEntries)) {
        throw new Error(`Error fetching billable time entries: ${JSON.stringify(timeEntries)}`);
      }

      const billableTimeEntries = timeEntries.data.map((timeEntry) => ({
        id: timeEntry.id,
        description: timeEntry.description,
        task_id: timeEntry.task?.id,
        task_name: timeEntry.task?.name,
        list_name: String(timeEntry.task_location?.list_name),
        user_id: timeEntry.user?.id,
        duration: timeEntry.duration,
        start: timeEntry.start,
        end: timeEntry.end,
        tags_length: timeEntry.tags.length,
        first_tag_name: timeEntry.tags.length ? timeEntry.tags[0].name : null,
      }));

      return billableTimeEntries.filter((timeEntry) => Number(timeEntry.duration) > 0);
    });

    const nonBillableTimeEntriesV2 = await io.runTask('fetch-all-non-billable-time-entries-v2', async () => {
      const clickupClient = new ClickUpClient();
      let allTimeEntries: TimeEntryLight[] = [];

      const assigneeIds = employees
        .filter((e) => e.clickup_user_id)
        .map((e) => e.clickup_user_id)
        .join(',');

      const timeEntries = await clickupClient.getAllTimeEntriesRange(assigneeIds, startMs, endMs);

      if (isClickUpError(timeEntries)) {
        throw new Error(`Error fetching non billable time entries: ${JSON.stringify(timeEntries)}`);
      }

      allTimeEntries = timeEntries.data.map((timeEntry) => ({
        id: timeEntry.id,
        description: timeEntry.description,
        task_id: timeEntry.task?.id,
        task_name: timeEntry.task?.name,
        list_name: String(timeEntry.task_location?.list_name),
        user_id: timeEntry.user?.id,
        duration: timeEntry.duration,
        start: timeEntry.start,
        end: timeEntry.end,
        tags_length: timeEntry.tags.length,
        first_tag_name: timeEntry.tags.length ? timeEntry.tags[0].name : null,
      }));

      return allTimeEntries
        .filter((timeEntry) => !billableTimeEntriesV2.some((billableTimeEntry) => billableTimeEntry.id == timeEntry.id))
        .filter((timeEntry) => Number(timeEntry.duration) > 0);
    });

    return { billableTimeEntries: billableTimeEntriesV2, nonBillableTimeEntries: nonBillableTimeEntriesV2 };
  },
});
