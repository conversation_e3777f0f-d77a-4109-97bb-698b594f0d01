import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTimeEntry } from '@/data/types/clickup.types';
import { IO } from '@trigger.dev/sdk';

export async function getClickupTimeEntries(startMs: number, endMs: number, io: IO) {
  const employees = await io.runTask('fetch-all-employees', async () => {
    const supabase = createClient();
    const { data, error } = await supabase.from('employees').select('*');

    if (error) {
      throw new Error(`Error fetching employees: ${error}`);
    }

    return data;
  });

  let billableTimeEntries: ClickUpTimeEntry[] = [];
  await io.runTask('fetch-all-billable-time-entries', async () => {
    const clickupClient = new ClickUpClient();
    for (const employee of employees) {
      await io.runTask(`fetch-billable-time-entries-for-employee-${employee.clickup_user_id}`, async () => {
        if (!employee.clickup_user_id) {
          return [];
        }

        const timeEntries = await clickupClient.getBillableTimeEntriesRange(employee.clickup_user_id, startMs, endMs);

        if ('err' in timeEntries) {
          throw new Error(
            `Error fetching billable time entries for employee ${employee.clickup_user_id}: ${JSON.stringify(timeEntries)}`,
          );
        }

        billableTimeEntries = billableTimeEntries.concat(
          timeEntries.data.filter((timeEntry) => Number(timeEntry.duration) > 0),
        );
      });
    }
  });

  let nonBillableTimeEntries: ClickUpTimeEntry[] = [];
  await io.runTask('fetch-all-non-billable-time-entries', async () => {
    const clickupClient = new ClickUpClient();
    let allTimeEntries: ClickUpTimeEntry[] = [];

    for (const employee of employees) {
      await io.runTask(`fetch-non-billable-time-entries-for-employee-${employee.clickup_user_id}`, async () => {
        if (!employee.clickup_user_id) {
          return [];
        }

        const timeEntries = await clickupClient.getAllTimeEntriesRange(employee.clickup_user_id, startMs, endMs);

        if ('err' in timeEntries) {
          throw new Error(
            `Error fetching non billable time entries for employee ${employee.clickup_user_id}: ${JSON.stringify(timeEntries)}`,
          );
        }

        allTimeEntries = allTimeEntries.concat(timeEntries.data);
      });
    }

    nonBillableTimeEntries = allTimeEntries
      .filter((timeEntry) => !billableTimeEntries.some((billableTimeEntry) => billableTimeEntry.id == timeEntry.id))
      .filter((timeEntry) => Number(timeEntry.duration) > 0);
  });

  return { billableTimeEntries, nonBillableTimeEntries };
}
