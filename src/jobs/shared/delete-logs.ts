import { createClient } from '@/data/supabase-server';

export async function markForDeletion(type: string, id: string | number) {
  const supabase = createClient();
  return supabase
    .from('delete_log')
    .insert({ type, entity_id: String(id) })
    .select('id')
    .single();
}

export async function markDeletionConfirmed(deletionId: string) {
  const supabase = createClient();
  return supabase.from('delete_log').update({ delete_confirmed: true }).eq('id', deletionId);
}
