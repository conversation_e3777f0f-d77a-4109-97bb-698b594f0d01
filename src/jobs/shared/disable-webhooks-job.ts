import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { supabaseServiceClient } from '@/data/supabase-server';
import { EmailClient } from '@/data/email-client';

export const disableWebhooksJob = client.defineJob({
  id: 'disable-webhooks-job',
  name: 'Disable Webhooks Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    await io.runTask('disable-webhooks', async () => {
      const { error } = await supabaseServiceClient()
        .from('global_settings')
        .upsert({ id: 1, webhooks_active: false }, { onConflict: 'id' });

      if (error) {
        throw new Error(`Error disabling webhooks: ${JSON.stringify(error)}`);
      }
    });
  },
});

export async function disableWebhooks() {
  const { error } = await supabaseServiceClient()
    .from('global_settings')
    .upsert({ id: 1, webhooks_active: false }, { onConflict: 'id' });

  if (error) {
    throw new Error(`Error disabling webhooks: ${JSON.stringify(error)}`);
  }
}
