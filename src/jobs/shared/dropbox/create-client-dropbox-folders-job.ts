import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { DropboxClient } from '@/data/dropbox-client';
import { createClient } from '@/data/supabase-server';
import { buildDropboxClientFolderPath } from '@/lib/utils';

type PayloadType = {
  folders: {
    id: number;
    bexio_contact_id: number;
    name: string;
    clickup_archived: boolean;
  }[];
};

export const createClientDropboxFoldersJob = client.defineJob({
  id: 'create-client-dropbox-folders',
  name: 'Create Client Dropbox Folders',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload: PayloadType, io, ctx) => {
    const { folders } = payload;

    const dropboxClient = new DropboxClient();
    for (const folder of folders) {
      await io.runTask(`create-dropbox-folder-for-contact-${folder.bexio_contact_id}`, async () => {
        const folderPath = buildDropboxClientFolderPath(folder);
        const savedFolderPath = await io.try(
          async () => {
            const response = await dropboxClient.createFolder(folderPath);

            if ('error' in response) {
              return new Error(`Error creating folder: ${JSON.stringify(response)}`);
            }

            return response.result.metadata.path_lower;
          },
          async (error) => {
            // @ts-ignore
            const status = error?.status;

            if (status == 409) {
              await io.logger.info(`Folder already exists: ${folderPath}`);
              await io.logger.info(`Error: ${JSON.stringify(error)}`);
              return folderPath.toLowerCase();
            }

            if (status >= 400) {
              throw new Error(`Error creating folder: ${JSON.stringify(error)}`);
            }
          },
        );

        if (savedFolderPath instanceof Error || !savedFolderPath) {
          return;
        }

        const supabase = createClient();
        const { error } = await supabase
          .from('clients')
          .update({
            dropbox_folder_path: savedFolderPath,
          })
          .eq('id', folder.id);

        if (error) {
          throw new Error(`Error updating client folder path in db: ${error}`);
        }
      });
    }
  },
});
