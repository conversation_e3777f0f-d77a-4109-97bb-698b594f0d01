import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { DropboxClient } from '@/data/dropbox-client';
import { supabaseServiceClient } from '@/data/supabase-server';
import { buildDropboxProjectFolderPath } from '@/lib/utils';

type PayloadType = {
  projects: {
    clickup_list_id: string;
    clickup_name: string;
    clickup_archived: boolean;
    is_client_project: boolean;
    dropbox_folder_path?: string;
    clients: {
      name: string;
      clickup_archived: boolean;
    };
  }[];
};

export const createOrRenameProjectDropBoxFoldersJob = client.defineJob({
  id: 'create-project-dropbox-folder-job',
  name: 'Create Project Dropbox Folder Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload: PayloadType, io, ctx) => {
    const { projects } = payload;

    await io.runTask('create-dropbox-folder', async () => {
      const dropboxClient = new DropboxClient();
      for (const dbProject of projects) {
        await io.runTask(`create-dropbox-folder-${dbProject.clickup_list_id}`, async () => {
          if (!dbProject.is_client_project || !dbProject.clients?.name) {
            await io.logger.info(`No client found for project: ${dbProject.clickup_name}`);
            return;
          }

          const newPath = buildDropboxProjectFolderPath(dbProject, dbProject.clients);
          let createdFolderPath;
          if (!dbProject.dropbox_folder_path) {
            createdFolderPath = await io.try(
              async () => {
                const { result } = await dropboxClient.createFolder(newPath);
                return result.metadata.path_lower;
              },
              async (error) => {
                // @ts-ignore
                const status = error?.status;
                if (status === 409) {
                  await io.logger.error(`Folder already exists: ${newPath}`);
                  await io.logger.error(`Error: ${JSON.stringify(error)}`);
                  return;
                }

                await io.logger.error(`Error renaming folder: ${error}`);
                throw new Error(`Error creating folder: ${error}`);
              },
            );
          } else if (dbProject.dropbox_folder_path !== newPath.toLowerCase()) {
            createdFolderPath = await io.try(
              async () => {
                const { result } = await dropboxClient.renameFolder(dbProject.dropbox_folder_path!, newPath);
                return result.metadata.path_lower;
              },
              async (error) => {
                // @ts-ignore
                const status = error?.status;
                if (status === 409) {
                  await io.logger.error(`Folder already exists: ${newPath}`);
                  await io.logger.error(`Error: ${JSON.stringify(error)}`);
                  return;
                }

                await io.logger.error(`Error renaming folder: ${error}`);
                throw new Error(`Error renaming folder: ${error}`);
              },
            );
          }

          if (!createdFolderPath) {
            await io.logger.info(`Folder already up to date: ${newPath}`);
            return;
          }

          const { error } = await supabaseServiceClient()
            .from('projects')
            .update({
              dropbox_folder_path: createdFolderPath,
            })
            .eq('clickup_list_id', dbProject.clickup_list_id);

          if (error) {
            throw new Error(`Error updating project dropbox folder in db: ${JSON.stringify(error)}`);
          }
        });
      }
    });
  },
});
