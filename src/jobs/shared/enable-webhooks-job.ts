import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { supabaseServiceClient } from '@/data/supabase-server';

export const enableWebhooksJob = client.defineJob({
  id: 'enable-webhooks-job',
  name: 'Enable Webhooks Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    await io.runTask('enable-webhooks', async () => {
      const { error: enableWebhooksError } = await supabaseServiceClient()
        .from('global_settings')
        .upsert({ id: 1, webhooks_active: true }, { onConflict: 'id' });

      if (enableWebhooksError) {
        throw new Error(`Error enabling webhooks: ${JSON.stringify(enableWebhooksError)}`);
      }
    });
  },
});

export async function enableWebhooks() {
  const { error: enableWebhooksError } = await supabaseServiceClient()
    .from('global_settings')
    .upsert({ id: 1, webhooks_active: true }, { onConflict: 'id' });

  if (enableWebhooksError) {
    throw new Error(`Error enabling webhooks: ${JSON.stringify(enableWebhooksError)}`);
  }
}
