import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { EmailClient } from '@/data/email-client';

export const notifyWebmasterJob = client.defineJob({
  id: 'notify-webmaster-job',
  name: 'Notify Webmaster Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const { subject = '[Internal Tools] Fehler in sync job', message = '<PERSON><PERSON> in sync job' } = payload;
    await io.runTask('send-message', async () => {
      const emailClient = new EmailClient();
      await emailClient.sendEmail('<EMAIL>', subject, message);
    });
  },
});
