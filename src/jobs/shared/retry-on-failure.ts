import { FailedRunNotification } from '@trigger.dev/core';
import { client as triggerClient } from '@/trigger';
import { notify<PERSON>ebmaster<PERSON>ob } from '@/jobs';
export async function notifyRetryFailure(notification: FailedRunNotification<any>, maxRetryCount: number) {
  const res = await triggerClient.getRuns(notification.job.id, {
    take: maxRetryCount,
  });
  const runDetails = await triggerClient.getRun(res.runs[0].id);
  await notifyWebmasterJob.invoke({
    subject: `[Internal Tools] <PERSON><PERSON> in Retry Job ${notification.invocation.id}`,
    message: `<PERSON><PERSON> in JOB(s) after ${maxRetryCount} retries:
      <br>
      <br>
      
      Link to runs: ${res.runs.map((run) => `<p>${process.env.TRIGGER_API_URL}/orgs/lumeos-gmbh-adc7/projects/internal-tools-PmKb/jobs/${notification.job.id}/runs/${run.id}/trigger </p>`).join('\n')}
      <br>
      <pre>
      ${JSON.stringify(runDetails, null, 2).replace(/\n/g, '<br>')}
      </pre>
      `,
  });
}
