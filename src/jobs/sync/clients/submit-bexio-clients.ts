import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import <PERSON> from 'papa<PERSON><PERSON>';
import { BexioCSVClientExportRow } from '@/data/types/bexio-csv-client-export.types';
import { updateBexioClientsExport } from '@/jobs';

export const submitBexioClients = client.defineJob({
  id: 'submit-bexio-clients',
  name: 'Submit Bexio Clients from Node Bot',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { csv } = payload;

    await io.runTask('launch-update-action', async () => {
      const result = await new Promise<BexioCSVClientExportRow[]>((resolve, reject) =>
        Papa.parse(csv, {
          header: true,
          complete: async (result) => {
            await io.logger.info('Parsing complete | submitCSVData');
            await io.logger.info(`Result: ${JSON.stringify(result)}`);
            resolve(result.data as BexioCSVClientExportRow[]);
          },
          error: async (error: any) => {
            console.error('Error parsing file:', error);
            await io.logger.error(`Error parsing file: ${JSON.stringify(error)}`);
            reject(new Error(`Error parsing file: ${JSON.stringify(error)}`));
          },
        }),
      );

      await updateBexioClientsExport.invokeAndWaitForCompletion('clients-data-submission', result);
    });
  },
});
