import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { BexioCSVClientExportRow } from '@/data/types/bexio-csv-client-export.types';
import { createClient } from '@/data/supabase-server';

export const updateBexioClientsExport = client.defineJob({
  id: 'update-bexio-clients-export',
  name: 'Update Bexio Clients Export from Node Bot',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload: BexioCSVClientExportRow[], io, _) => {
    await io.runTask('update-bexio-clients', async () => {
      const upsertClients = payload
        .filter((row) => Number(row.Kontaktart) === 1)
        .map((row) => ({
          name: row.Firma,
          num_employees: Number(row['Anzahl Mitarbeitende']) <= 0 ? null : Number(row['Anzahl Mitarbeitende']),
        }));

      const supabase = createClient();
      const { error } = await supabase.from('clients').upsert(upsertClients, {
        onConflict: 'name',
      });

      if (error) {
        throw new Error(`Error upserting clients data: ${error.message}`);
      }
    });
  },
});
