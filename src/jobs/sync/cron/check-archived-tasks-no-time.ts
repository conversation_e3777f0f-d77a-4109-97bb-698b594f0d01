import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask } from '@/data/types/clickup.types';
import { convertCronToUTC } from '@/lib/utils';

export const checkArchivedTasksNoTime = client.defineJob({
  id: 'check-archived-tasks-no-time',
  name: 'Check Archived Tasks with No Time',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('13 6-23 * * *') }),
  run: async (payload, io, _) => {
    let archivedClickupTasks: ClickUpTask[] = [];
    let curTasks: ClickUpTask[] = [];
    let page = 0;
    let lastPage = false;
    while (!lastPage) {
      [curTasks, lastPage] = await io.runTask(`fetch-tasks-page-${page}`, async () => {
        const clickupClient = new ClickUpClient();
        const tasksResponse = await clickupClient.getTasks(page, {
          archived: true,
        });

        if ('err' in tasksResponse) {
          throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
        }

        return [tasksResponse.tasks, tasksResponse.last_page];
      });

      archivedClickupTasks = archivedClickupTasks.concat(curTasks);
      await io.logger.info(`Ran task fetch-tasks-page-${page} with ${curTasks.length} tasks`);
      page++;
    }

    await io.runTask('mark-tasks-as-archived-in-db', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('tasks')
        .update({
          clickup_archived: true,
        })
        .in(
          'clickup_task_id',
          archivedClickupTasks.map((task) => task.id),
        );

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const globalSettings = await io.runTask('get-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    await io.runTask('check-for-archived-tasks-with-tracked-time', async () => {
      const supabase = createClient();
      for (const task of archivedClickupTasks) {
        if (task.time_spent && task.time_spent > 0) {
          const { data, error } = await supabase
            .from('error_notifications')
            .select('message_hash')
            .eq('message_hash', `archivedTaskWithTrackedTime#${task.id}`);

          if (error) {
            throw new Error(`Error fetching error notifications: ${JSON.stringify(error)}`);
          }

          if (data.length > 0) {
            continue;
          }

          await io.runTask(`notify-task-with-tracked-time-${task.id}`, async () => {
            const clickupClient = new ClickUpClient();
            await clickupClient.createTask(String(globalSettings.pm_list_id), {
              name: `adm: Task mit Time-Entries archiviert`,
              description: `Jemand hat den Task "${task.name}" mit vorhandenen Time-Entries archiviert: https://app.clickup.com/t/${task.id}
            
Diese Time-Entries sollen auf einen nicht archivierten Task umgebucht werden.`,
              assignees: [process.env.CLICKUP_ADMIN_USER_ID],
              priority: 1,
              time_estimate: 10 * 60 * 1000,
              due_date: new Date().getTime(),
            });
          });

          await supabase.from('error_notifications').insert({
            message_hash: `archivedTaskWithTrackedTime#${task.id}`,
            created_at: new Date().toISOString(),
            last_sent: new Date().toISOString(),
          });
        }
      }
    });
  },
});
