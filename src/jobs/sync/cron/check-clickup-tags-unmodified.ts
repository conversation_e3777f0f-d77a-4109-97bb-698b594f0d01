import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { EmailClient } from '@/data/email-client';
import { syncClickupTimeEntriesToBexioTimesheetJob } from '@/jobs/sync/cron/time/sync-clickup-time-entries-to-bexio-timesheet';
import { convertCronToUTC } from '@/lib/utils';

export const checkClickupTagsUnmodifiedJob = client.defineJob({
  id: 'check-clickup-tags-unmodified',
  name: 'Check ClickUp tags unmodified',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('* 6-23 * * *') }), // every 5 minutes from 6am to 11pm
  run: async (payload, io, _) => {
    await io.wait('init-wait', 20);

    const [missingTags, redundantTags] = await io.runTask('check-for-differences', async () => {
      const clickupClient = new ClickUpClient();
      const tags = await clickupClient.getTimeEntriesTags();

      if (isClickUpError(tags)) {
        throw new Error(`Error fetching tags: ${JSON.stringify(tags)}`);
      }

      const supabase = createClient();
      const { data, error } = await supabase.from('business_activities').select('*');

      if (error) {
        throw new Error(`Error fetching business activities: ${JSON.stringify(error)}`);
      }

      // tags that are in the database, but not in clickup
      const missingTags = [];
      for (const activity of data) {
        if (activity.clickup_task_tag === 'null' || activity.clickup_task_tag === null) {
          continue;
        }

        const tag = tags.data.find((tag) => tag.name === activity.clickup_task_tag);

        if (!tag) {
          missingTags.push(activity);
        }
      }

      // tags that are in clickup, but not in the database
      const redundantTags = [];
      for (const tag of tags.data) {
        const activity = data.find((activity) => activity.clickup_task_tag === tag.name);

        if (!activity) {
          redundantTags.push(tag);
        }
      }

      return [missingTags, redundantTags];
    });

    if (missingTags.length != 0 || redundantTags.length != 0) {
      await io.runTask('send-email-notification', async () => {
        const message = `Jemand hat die Tags in Clickup verändert. Dieser Fehler sollte automatisch behoben werden. Folgende Tags wurden verändert:\n\n
        Fehlende tags: ${missingTags.map((tag) => tag.clickup_task_tag).join(', ')}\n
        Überflüssige tags: ${redundantTags.map((tag) => tag.name).join(', ')}`;
        const messageHash = Buffer.from(message).toString('base64');

        const supabase = createClient();
        const { data, error } = await supabase
          .from('error_notifications')
          .select('*')
          .eq('message_hash', messageHash)
          .single();

        if (error) {
          throw new Error(`Error fetching email notifications: ${JSON.stringify(error)}`);
        }

        // if last_sent is less than 24 hours ago, don't send another email
        if (data && new Date().getTime() - new Date(data.last_sent).getTime() < 86400000) {
          return;
        }

        const emailClient = new EmailClient();
        await emailClient.sendEmail(process.env.EMAIL_TO!, '[Internal Tools] Fehler bei Clickup Tags', message);
        await supabase
          .from('error_notifications')
          .upsert({ message_hash: messageHash, last_sent: new Date().toISOString() }, { onConflict: 'message_hash' });
      });

      await syncClickupTimeEntriesToBexioTimesheetJob.invokeAndWaitForCompletion(
        'wait-sync-clickup-time-entries-to-bexio-timesheet',
        {
          ts: new Date(),
          lastTimestamp: new Date(),
        },
      );
    }

    // Add missing tags to ClickUp
    for (const activity of missingTags) {
      await io.runTask(`add-tag-${activity.clickup_task_tag}-to-clickup`, async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('time_entries')
          .select('clickup_time_entry_id')
          .eq('clickup_task_tag', activity.clickup_task_tag);

        if (error) {
          throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
        }

        const clickupClient = new ClickUpClient();
        const newTag = {
          name: activity.clickup_task_tag,
          tag_fg: activity.clickup_tag_fg,
          tag_bg: activity.clickup_tag_bg,
        };
        const teIds = data.map((te) => String(te.clickup_time_entry_id));
        const tag = await clickupClient.bulkAssignTagsToTimeEntries(teIds, newTag);

        if (isClickUpError(tag)) {
          throw new Error(`Error creating tag: ${JSON.stringify(tag)} for time entries: ${teIds.join(', ')}`);
        }
      });
    }

    // Remove redundant tags from ClickUp
    for (const tag of redundantTags) {
      await io.runTask(`remove-tag-${tag.name}-from-clickup`, async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('time_entries')
          .select('clickup_time_entry_id')
          .eq('clickup_task_tag', tag.name);

        if (error) {
          throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
        }

        const clickupClient = new ClickUpClient();
        const timeEntryIds = data.map((te) => String(te.clickup_time_entry_id));
        const result = await clickupClient.bulkDeleteTagsFromTimeEntries(timeEntryIds, tag.name);

        if (isClickUpError(result)) {
          throw new Error(`Error deleting tag: ${JSON.stringify(result)}`);
        }
      });
    }
  },
});
