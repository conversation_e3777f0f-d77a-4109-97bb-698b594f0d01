import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { EmailClient } from '@/data/email-client';
import { convertCronToUTC } from '@/lib/utils';

export const checkWebhookHealthJob = client.defineJob({
  id: 'check-webhook-health',
  name: 'Check Webhook Health',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('30 * * * *') }), // every 30 minutes
  run: async (payload, io, _) => {
    await io.wait('init-wait', 10);

    await io.runTask('check-health', async () => {
      const clickUpClient = new ClickUpClient();
      const webhooks = await clickUpClient.getWebhooks();

      if (isClickUpError(webhooks)) {
        throw new Error('Error fetching webhooks');
      }

      const webhooksFiltered = webhooks.webhooks.filter((webhook) => webhook.endpoint.includes('internal-tools'));
      const suspendedWebhooks = webhooksFiltered.filter((webhook) => webhook.health.status === 'suspended');

      if (suspendedWebhooks.length) {
        await io.logger.error(
          `The following webhooks are suspended: ${suspendedWebhooks.map((webhook) => webhook.id).join(', ')}`,
        );
        const emailClient = new EmailClient();
        await emailClient.sendEmail(
          '<EMAIL>',
          '[Internal Tools] Suspended Webhooks',
          `The following webhooks are suspended: ${suspendedWebhooks.map((webhook) => webhook.id).join(', ')}`,
        );
      }
    });
  },
});
