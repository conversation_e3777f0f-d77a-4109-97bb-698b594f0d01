import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertCronToUTC } from '@/lib/utils';

export const checkWorkingHoursAreSet = client.defineJob({
  id: 'check-working-hours-are-set',
  name: 'Check Working Hours Are Set',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 9 * * 1,4') }), // 9:00 on Mondays and Thursdays
  run: async (payload, io, _) => {
    const activeEmployees = await io.runTask('get-active-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*').eq('inactive', false);

      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const [start, end] = await io.runTask('get-date-range', async () => {
      // On Mondays, check for working hours for the week 14 days in the future
      if (new Date().getDay() === 1) {
        return [
          new Date(new Date().setDate(new Date().getDate() + 14)).toISOString().slice(0, 10),
          new Date(new Date().setDate(new Date().getDate() + 20)).toISOString().slice(0, 10),
        ];
      }

      // On Thursdays, check for working hours from next monday to next sunday
      const start = new Date(new Date().setDate(new Date().getDate() + 4)).toISOString().slice(0, 10);
      const end = new Date(new Date().setDate(new Date().getDate() + 10)).toISOString().slice(0, 10);
      return [start, end];
    });

    const employeesMissingWorkingHours = await io.runTask('check-for-missing-working-hours', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('working_hours')
        .select('*')
        .eq('date', start)
        .lte('date', end)
        .gt('hours', 0);

      if (error) {
        throw new Error(`Error fetching working hours: ${JSON.stringify(error)}`);
      }

      const employeesWithWorkingHours = data?.map((wh) => wh.user_id);
      return activeEmployees?.filter((user) => !employeesWithWorkingHours?.includes(user.user_id));
    });

    if (employeesMissingWorkingHours?.length) {
      const globalSettings = await io.runTask('fetch-global-settings', async () => {
        const supabase = createClient();
        const { data, error } = await supabase.from('global_settings').select('*').single();

        if (error) {
          throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
        }

        return data;
      });

      await io.runTask('send-missing-working-hours-notifications', async () => {
        const supabase = createClient();
        const clickupClient = new ClickUpClient();

        for (const employee of employeesMissingWorkingHours) {
          await io.runTask(`send-missing-working-hours-notification-${employee.user_id}`, async () => {
            const { data } = await supabase
              .from('error_notifications')
              .select('*')
              .eq('message_hash', `MissingWorkingHours#${employee.user_id}#${start}#${end}`);

            if (data?.length) {
              await io.logger.info(`Notification already sent for ${employee.user_id}`);
              return;
            }

            const response = await clickupClient.createTask(String(globalSettings.hr_list_id), {
              name: `hr: Sollzeiten Internal - ${employee.name}`,
              description: `${employee.name} ist gem. internal Tool aktiv und hat bald keine Sollzeiten mehr erfasst im internal Tool.
Ergänze wenn nötig die Sollzeiten im Internal Tool unter Arbeitszeiten: https://internal-tools.lumeos.ch/working-hours`,
              assignees: [process.env.CLICKUP_ADMIN_USER_ID],
              priority: 1,
              time_estimate: 10 * 60 * 1000,
              due_date: new Date().getTime(),
            });

            if (isClickUpError(response)) {
              throw new Error(`Error creating task: ${JSON.stringify(response)}`);
            }

            await supabase.from('error_notifications').insert({
              message_hash: `MissingWorkingHours#${employee.user_id}#${start}#${end}`,
              last_sent: new Date().toISOString(),
            });
          });
        }
      });
    }
  },
});
