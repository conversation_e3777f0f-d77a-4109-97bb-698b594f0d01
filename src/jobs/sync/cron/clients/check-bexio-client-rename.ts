import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertCronToUTC } from '@/lib/utils';

export const checkBexioClientRenameJob = client.defineJob({
  id: 'check-bexio-client-rename',
  name: 'Check Bexio client rename',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('* 6-23 * * *') }), // every minute from 6am to 11pm
  run: async (payload, io, _) => {
    await io.wait('init-wait', 15);

    // 1 CHECK FOR DIFFERENCES
    const foldersToBeUpdated = await io.runTask('check-for-differences', async () => {
      const bexioClient = new BexioClient();
      const clients = await bexioClient.getContacts();

      if (isBexioError(clients)) {
        throw new Error(`Error fetching clients: ${JSON.stringify(clients)}`);
      }

      const bexioClients =
        clients.map((cl) => {
          return {
            id: cl.id,
            name: cl.name_1,
          };
        }) || [];

      const supabase = createClient();
      const { data, error } = await supabase.from('clients').select('*');

      if (error) {
        throw new Error(`Error fetching clients: ${JSON.stringify(error)}`);
      }

      const clientsToBeRenamed = data.filter((client) => {
        const bexioClient = bexioClients.find((cl) => cl.id === client.bexio_contact_id);
        return client.bexio_contact_id && bexioClient?.name !== client.name;
      });

      return clientsToBeRenamed.map((client) => {
        const bexioClient = bexioClients.find((cl) => cl.id === client.bexio_contact_id);
        return {
          clickup_folder_id: client.clickup_folder_id,
          name: bexioClient?.name,
        };
      });
    });

    // 2 UPDATE FOLDERS
    await io.runTask('update-folders', async () => {
      const clickupClient = new ClickUpClient();
      const supabase = createClient();
      for (const folder of foldersToBeUpdated) {
        await io.runTask(`update-folder-${folder.clickup_folder_id}`, async () => {
          if (!folder.clickup_folder_id) {
            await io.logger.info(`No folder found for client: ${JSON.stringify(folder)}`);
            return;
          }

          // UPDATE CLICKUP LIST
          const response = await clickupClient.updateFolder(folder.clickup_folder_id, {
            name: folder.name,
          });

          if (isClickUpError(response)) {
            throw new Error(`Error updating folder: ${JSON.stringify(response)}`);
          }

          // UPDATE CLIENT IN DB
          const { error } = await supabase
            .from('clients')
            .update({ name: folder.name })
            .eq('clickup_folder_id', folder.clickup_folder_id);

          if (error) {
            throw new Error(`Error updating project in db: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 3 UPDATE FOLDERS
    // SHOULD DO AUTOMATICALLY AFTER WEBHOOK CALL CAUSE OF RENAME
  },
});
