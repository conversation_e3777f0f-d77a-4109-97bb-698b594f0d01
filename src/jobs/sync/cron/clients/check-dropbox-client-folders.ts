import { cron<PERSON>rigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { DropboxClient } from '@/data/dropbox-client';
import { EmailClient } from '@/data/email-client';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { convertCronToUTC } from '@/lib/utils';

export const checkDropboxClientFoldersJob = client.defineJob({
  id: 'check-dropbox-client-folders',
  name: 'Check Dropbox Client Folders Job',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('30 5 * * *') }), // Every day at 5am
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const dropboxPaths = await io.runTask('get-dropbox-paths', async () => {
      const dropboxClient = new DropboxClient();
      const prefix = process.env.DROPBOX_FOLDER_PREFIX!;
      try {
        const response = await dropboxClient.getFolderContents(prefix);
        return response.result.entries.filter((e) => !!e).map((entry) => String(entry.path_lower));
      } catch (error) {
        const emailClient = new EmailClient();
        await emailClient.sendEmail(
          '<EMAIL>',
          '[Internal Tools] Check Dropbox Client Folders',
          `There was an error fetching the Dropbox folders: ${JSON.stringify(error)}`,
        );
        return [];
      }
    });

    const dbDropboxPaths = await io.runTask('get-db-dropbox-paths', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('clients').select('dropbox_folder_path');
      if (error) {
        await io.logger.error(`Error fetching dropbox paths: ${JSON.stringify(error)}`);
        return [];
      }

      return data?.map((client) => String(client.dropbox_folder_path)).filter((p) => !!p);
    });

    if (dropboxPaths.length == 0 || dbDropboxPaths.length == 0) {
      await io.logger.error('No dropbox paths found');
      return;
    }

    // check if dropbox paths are in db
    const dropboxPathsNotInDB = dropboxPaths.filter((p) => !dbDropboxPaths.includes(p));

    // skip if ending with /zarchiv
    const dropboxPathsNotInDBWithoutZArchiv = dropboxPathsNotInDB.filter((p) => !p.includes('/zarchiv'));
    await io.logger.info(`Dropbox paths not in DB: ${dropboxPathsNotInDBWithoutZArchiv.join('\n')}`);

    await io.runTask('notify-project-manager', async () => {
      for (const path of dropboxPathsNotInDBWithoutZArchiv) {
        await io.runTask(`notify-project-manager-${path}`, async () => {
          const clickupClient = new ClickUpClient();
          const message = `Auf Dropbox im Ordner Kundenprojekte wurde fälschlicherweise ein Ordner von einem User manuell erstellt.
Ordnername: ${path}.
`;
          const messageHash = Buffer.from(message).toString('base64');

          const supabase = createClient();
          const { data, error } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', messageHash)
            .single();

          if (error && error.code != 'PGRST116') {
            throw new Error(`Error fetching notifications: ${JSON.stringify(error)}`);
          }

          if (data?.message_hash) {
            await io.logger.info(`Notification already sent.`);
            return;
          }

          await clickupClient.createTask(String(globalSettings.pm_list_id), {
            name: `it: Dropbox Kundenprojekte Ordner - manueller Ordner erstellt`,
            description: message,
            assignees: [process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          await supabase
            .from('error_notifications')
            .upsert({ message_hash: messageHash, last_sent: new Date().toISOString() }, { onConflict: 'message_hash' });
          return;
        });
      }
    });
  },
});
