import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { createClientDropboxFoldersJob } from '@/jobs';
import { convertCronToUTC } from '@/lib/utils';

type PartialBexioContact = { id: number; name_1: string; contact_branch_ids: number[] };

export const checkNewBexioContactsJob = client.defineJob({
  id: 'check-new-bexio-contacts',
  name: 'Check new bexio contacts',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('* 6-23 * * *') }),
  run: async (payload, io, _) => {
    // 1 FETCH ALL BEXIO CONTACTS
    const bexioContacts: PartialBexioContact[] = await io.runTask('fetch-all-bexio-contacts', async () => {
      const bexioClient = new BexioClient();
      const contacts = await bexioClient.getContacts();

      if (!contacts || isBexioError(contacts) || !contacts.length) {
        throw new Error(`Error fetching contacts: ${JSON.stringify(contacts)}`);
      }

      return contacts
        .filter((contact) => contact.contact_type_id === 1)
        .map((contact) => {
          return {
            id: contact.id,
            name_1: contact.name_1,
            contact_branch_ids: contact.contact_branch_ids?.split(',').map((id) => Number(id)) || [],
          };
        });
    });

    // 2 UPSERT BEXIO CONTACTS INTO DB
    await io.runTask('upsert-bexio-contacts-into-db', async () => {
      const upsertContacts = bexioContacts.map((contact) => {
        return {
          bexio_contact_id: contact.id,
          name: contact.name_1,
          bexio_sector_ids: contact.contact_branch_ids,
        };
      });

      const supabase = createClient();
      const { error } = await supabase.from('clients').upsert(upsertContacts, { onConflict: 'name' });

      if (error) {
        throw new Error(`Error upserting contacts: ${JSON.stringify(error)}`);
      }
    });

    // 3 GET MISSING FOLDERS
    const missingContacts = await io.runTask('fetch-all-clickup-folders', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('clients').select('*').is('clickup_folder_id', null);

      if (error) {
        throw new Error(`Error fetching clients: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    // 4 CREATE MISSING FOLDERS
    await io.runTask('create-clickup-folders', async () => {
      for (const contact of missingContacts) {
        await io.runTask(`create-clickup-folder-for-contact-${contact.bexio_contact_id}`, async () => {
          const clickupClient = new ClickUpClient();
          const createdFolder = await clickupClient.createFolder(contact.name);

          if ('err' in createdFolder) {
            throw new Error(`Error creating folder: ${JSON.stringify(createdFolder)}`);
          }

          const supabase = createClient();
          const { error } = await supabase
            .from('clients')
            .update({
              clickup_folder_id: createdFolder.id,
            })
            .eq('id', contact.id);

          if (error) {
            throw new Error(`Error updating client in db: ${error}`);
          }
        });
      }
    });

    if (missingContacts.length === 0) {
      return;
    }

    // CREATE DROPBOX FOLDERS
    await createClientDropboxFoldersJob.invokeAndWaitForCompletion('create-client-dropbox-folders', {
      folders: missingContacts,
    });
  },
});
