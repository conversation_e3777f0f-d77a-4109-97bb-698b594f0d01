import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { convertCronToUTC } from '@/lib/utils';

export const syncBexioContactSectors = client.defineJob({
  id: 'sync-bexio-contact-sectors',
  name: 'Sync Bexio Contact Sectors',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 23 * * *') }), // every day at 23:00
  run: async (payload, io, _) => {
    await io.runTask('sync-contact-sectors', async () => {
      const bexioClient = new BexioClient();
      const sectorsResponse = await bexioClient.getContactSectors();

      if (isBexioError(sectorsResponse)) {
        throw new Error(`Error fetching bexio contact sectors: ${JSON.stringify(sectorsResponse)}`);
      }

      const supabase = createClient();
      const { error } = await supabase.from('bexio_contact_sectors').upsert(sectorsResponse, { onConflict: 'id' });

      if (error) {
        throw new Error(`Error upserting bexio contact sectors: ${JSON.stringify(error)}`);
      }
    });
  },
});
