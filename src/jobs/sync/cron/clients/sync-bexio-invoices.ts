import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { BexioClient } from '@/data/bexio-clients';
import { BexioInvoice, isBexioError } from '@/data/types/bexio.types';
import { convertCronToUTC } from '@/lib/utils';
import { createClient } from '@/data/supabase-server';
import { BexioInvoiceStatus } from '@/lib/constants';

export const syncBexioInvoices = client.defineJob({
  id: 'sync-bexio-invoices',
  name: 'Sync Bexio Invoices',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 23 * * *') }), // every day at 23:00
  run: async (payload, io, _) => {
    await io.runTask('sync-bexio-invoices', async () => {
      const bexioClient = new BexioClient();
      let allInvoices: BexioInvoice[] = [];
      let hasMoreInvoices = true;
      let offset = 0;
      while (hasMoreInvoices) {
        const bexioInvoices = await io.runTask(`get-bexio-invoices-offset-${offset}`, async () => {
          const invoicesResponse = await bexioClient.getInvoices(offset);
          if (isBexioError(invoicesResponse)) {
            throw new Error(`Error fetching bexio invoices: ${JSON.stringify(invoicesResponse)}`);
          }
          return invoicesResponse;
        });
        allInvoices = allInvoices.concat(bexioInvoices);
        offset += 2000;

        if (bexioInvoices.length < 2000) {
          hasMoreInvoices = false;
        }
      }

      await io.logger.info(`Fetched ${allInvoices.length} bexio invoices`);

      await io.runTask(`sync-bexio-invoices-to-db`, async () => {
        const supabase = createClient();

        const upsertValues = allInvoices.map((invoice) => {
          return {
            id: invoice.id,
            document_nr: invoice.document_nr,
            contact_id: invoice.contact_id,
            project_id: invoice.project_id,
            total: Number(invoice.total),
            total_gross: Number(invoice.total_gross),
            total_net: Number(invoice.total_net),
            total_taxes: Number(invoice.total_taxes),
            total_received_payments: Number(invoice.total_received_payments),
            total_remaining_payments: Number(invoice.total_remaining_payments),
            valid_from: invoice.is_valid_from,
            valid_to: invoice.is_valid_to,
            status: BexioInvoiceStatus[invoice.kb_item_status_id],
          };
        });

        const { error: invoiceError } = await supabase
          .from('bexio_invoices')
          .upsert(upsertValues, { onConflict: 'id' });

        if (invoiceError) {
          throw new Error(`Error upserting invoices to Supabase: ${invoiceError}`);
        }
      });

      const supabase = createClient();
      const { data: invoicesMissingServicesAmount, error } = await supabase
        .from('bexio_invoices')
        .select('id')
        .is('services_amount', null);
      if (error) {
        throw new Error(`Error selecting invoices missing services amount: ${error}`);
      }
      await io.logger.info(`Found ${invoicesMissingServicesAmount?.length} invoices missing services amount`);

      await io.runTask('sync-more-details-of-pending-invoices', async () => {
        const pendingInvoices = allInvoices.filter(
          (invoice) =>
            [7, 8, 16].includes(invoice.kb_item_status_id) ||
            invoicesMissingServicesAmount?.find(
              (invoiceMissingServicesAmount) => invoiceMissingServicesAmount.id === invoice.id,
            ),
        );

        await io.logger.info(`Found ${pendingInvoices.length} pending invoices`);

        const bexioClient = new BexioClient();
        for (const pendingInvoice of pendingInvoices) {
          await io.runTask(`get-bexio-invoice-${pendingInvoice.id}`, async () => {
            const invoice = await bexioClient.getInvoice(pendingInvoice.id);
            if (isBexioError(invoice)) {
              throw new Error(`Error fetching bexio invoice: ${JSON.stringify(invoice)}`);
            }

            const services = invoice.positions
              .filter((p) => p.account_id === 150)
              .reduce((acc, p) => acc + parseFloat(p.position_total || '0'), 0);
            const trades = invoice.positions
              .filter((p) => p.account_id && p.account_id !== 150)
              .reduce((acc, p) => acc + parseFloat(p.position_total || '0'), 0);
            const total = services + trades;
            const servicesNet = (services / total) * Number(invoice.total_net);
            const tradesNet = (trades / total) * Number(invoice.total_net);

            const supabase = createClient();
            const { error: invoiceError } = await supabase
              .from('bexio_invoices')
              .update({
                services_amount: servicesNet,
                trades_amount: tradesNet,
              })
              .eq('id', invoice.id);

            if (invoiceError) {
              throw new Error(`Error upserting invoice ${invoice.id} to Supabase: ${invoiceError}`);
            }
          });
        }
      });

      await io.runTask('set-amounts-to-0-for-invalid-invoices', async () => {
        const invalidInvoices = allInvoices.filter(
          (invoice) => invoice.kb_item_status_id === 19 || invoice.kb_item_status_id === 31,
        );

        await io.logger.info(`Found ${invalidInvoices.length} invalid invoices`);
        const supabase = createClient();

        const { error } = await supabase
          .from('bexio_invoices')
          .update({
            services_amount: 0,
            trades_amount: 0,
          })
          .in(
            'id',
            invalidInvoices.map((invoice) => invoice.id),
          );

        if (error) {
          throw new Error(`Error updating invoices to Supabase: ${error}`);
        }
      });
    });
  },
});
