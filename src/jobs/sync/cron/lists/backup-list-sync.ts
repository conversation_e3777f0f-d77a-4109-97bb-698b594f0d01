import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import {
  deletedClickupListJob,
  getClickUpListsJob,
  newClickupListCreateNewBexioProjectJob,
  updatedClickupListUpdateBexioProjectJob,
} from '@/jobs';
import { isClickUpError } from '@/data/types/clickup.types';
import { ClickUpClient } from '@/data/clickup-client';
import { convertCronToUTC } from '@/lib/utils';

export const backupListSync = client.defineJob({
  id: 'backup-list-sync',
  name: 'Backup List Sync',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('10,25,40,55 * * * *') }), // every 15 minutes
  run: async (payload, io, _) => {
    const clickupClientLists = await io.runTask('fetch-clickup-lists', async () => {
      const res = await getClickUpListsJob.invokeAndWaitForCompletion('get-clickup-lists', {
        skipNonClientLists: true,
      });
      if (!res.ok) {
        throw new Error(`Error fetching clickup lists: ${JSON.stringify(res)}`);
      }

      return res.output.clientLists.map((list) => ({
        id: list.id,
        name: list.name,
        deleted: list.deleted,
        archived: list.archived,
        folderId: list.folder.id,
        start: list.start_date,
        end: list.due_date,
      }));
    });

    const dbProjects = await io.runTask('fetch-active-db-projects', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('projects').select('*').eq('is_client_project', true);
      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const listsToDelete = await io.runTask('get-lists-to-delete', async () => {
      const notDeletedDbProjects = dbProjects
        .filter((project) => !project.deleted)
        .filter((project) => project.clickup_list_id);

      return notDeletedDbProjects.filter(
        (project) => !clickupClientLists.some((list) => project.clickup_list_id === list.id),
      );
    });

    const listsToUpdate = await io.runTask('get-lists-to-update', async () => {
      return clickupClientLists.filter((list) => {
        const dbProject = dbProjects.find((project) => project.clickup_list_id === list.id);
        if (!dbProject) {
          return false;
        }

        const isDifferent =
          dbProject.clickup_name !== list.name ||
          dbProject.clickup_archived !== list.archived ||
          dbProject.clickup_folder_id !== list.folderId ||
          Number(dbProject.clickup_start) !== Number(list.start) ||
          Number(dbProject.clickup_end) !== Number(list.end);

        if (isDifferent) {
          console.info('DIFFERENCE', dbProject.clickup_list_id);
        }

        // Log what properties are different
        if (dbProject.clickup_name !== list.name) {
          console.info(`Different name: ${dbProject.clickup_name} ${list.name}`);
        }

        if (dbProject.clickup_archived !== list.archived) {
          console.info(`Different archived: ${dbProject.clickup_archived} ${list.archived}`);
        }

        if (dbProject.clickup_folder_id !== list.folderId) {
          console.info(`Different folderId: ${dbProject.clickup_folder_id} ${list.folderId}`);
        }

        if (Number(dbProject.clickup_start) !== Number(list.start)) {
          console.info(`Different start: ${dbProject.clickup_start} ${list.start} | ${dbProject.clickup_list_id}`);
        }

        if (Number(dbProject.clickup_end) !== Number(list.end)) {
          console.info(`Different end: ${dbProject.clickup_end} ${list.end} | ${dbProject.clickup_list_id}`);
        }

        return isDifferent;
      });
    });

    const listsToCreate = await io.runTask('get-lists-to-create', async () => {
      return clickupClientLists.filter((list) => {
        return !dbProjects.some((project) => project.clickup_list_id === list.id);
      });
    });

    await io.logger.info(`Found ${listsToDelete.length} lists to delete`);
    await io.logger.info(`Found ${listsToUpdate.length} lists to update`);
    await io.logger.info(`Found ${listsToCreate.length} lists to create`);

    await io.runTask('delete-lists', async () => {
      for (const list of listsToDelete) {
        await io.runTask(`delete-list-${list.clickup_list_id}`, async () => {
          const clickupClient = new ClickUpClient();
          const deletedList = await clickupClient.getList(Number(list.clickup_list_id));

          if (isClickUpError(deletedList)) {
            throw new Error(`Error fetching list: ${JSON.stringify(deletedList)}`);
          }

          if (deletedList.deleted) {
            await deletedClickupListJob.invokeAndWaitForCompletion(`delete-clickup-list-job-${list.clickup_list_id}`, {
              deletedListId: list.clickup_list_id,
            });
          } else {
            await io.logger.error(`List ${list.clickup_list_id} is not deleted`);
          }
        });
      }
    });

    await io.runTask('update-lists', async () => {
      for (const list of listsToUpdate) {
        await io.runTask(`update-list-${list.id}`, async () => {
          await updatedClickupListUpdateBexioProjectJob.invokeAndWaitForCompletion(
            `updated-clickup-list-update-bexio-project-${list.id}`,
            {
              updatedClickupListId: list.id,
            },
          );
        });
      }
    });

    await io.runTask('create-lists', async () => {
      for (const list of listsToCreate) {
        await io.runTask(`create-list-${list.id}`, async () => {
          await newClickupListCreateNewBexioProjectJob.invokeAndWaitForCompletion(
            `backup-create-new-project-${list.id}`,
            {
              newClickupListId: list.id,
            },
          );
        });
      }
    });
  },
});
