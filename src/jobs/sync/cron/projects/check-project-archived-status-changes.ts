import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertCronToUTC } from '@/lib/utils';

export const checkProjectArchivedStatusChangesJob = client.defineJob({
  id: 'check-project-archived-status-changes',
  name: 'Check project archived status changes',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('* 6-23 * * *') }), // every minute from 6am to 11pm
  run: async (payload, io, _) => {
    await io.wait('init-wait', 30);

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').eq('id', 1).single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    // 1 FETCH ARCHIVED BEXIO PROJECTS
    await io.runTask('fetch-bexio-projects', async () => {
      const bexioClient = new BexioClient();
      const projects = await bexioClient.getProjects();

      if (isBexioError(projects)) {
        throw new Error(`Error fetching projects: ${JSON.stringify(projects)}`);
      }

      // ID = 924
      const specialProject = projects.find((p) => p.id === 924);
      await io.logger.info(`Special project found: ${JSON.stringify(specialProject)}`);

      const updatedArchiveStatus = projects.map((pr) => {
        return {
          bexio_project_id: pr.id,
          bexio_archived: pr.pr_state_id === 3,
        };
      });
      const supabase = createClient();
      await supabase.from('projects').upsert(updatedArchiveStatus, { onConflict: 'bexio_project_id' });
    });

    // 2 CHECK FOR DIFFERENCES
    const [listsToBeArchived, listsToBeUnarchivedOnClickup] = await io.runTask('check-for-differences', async () => {
      const supabase = createClient();
      const { data: projectsToBeArchivedOnClickup, error } = await supabase
        .from('projects')
        .select('name, clickup_name, clickup_user_id, bexio_project_id, clickup_list_id, clients(name)')
        .eq('is_client_project', true)
        .eq('deleted', false)
        .eq('clickup_archived', false)
        .eq('bexio_archived', true);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const { data: projectsToBeUnarchivedOnClickup, error: error2 } = await supabase
        .from('projects')
        .select('name, clickup_user_id, bexio_project_id, clickup_list_id')
        .eq('is_client_project', true)
        .eq('clickup_archived', true)
        .eq('deleted', false)
        .eq('bexio_archived', false);

      if (error2) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error2)}`);
      }

      return [projectsToBeArchivedOnClickup, projectsToBeUnarchivedOnClickup];
    });

    // 4 UPDATE TO BE ARCHIVED LISTS
    await io.runTask('update-to-be-archived-lists', async () => {
      const clickupClient = new ClickUpClient();
      const bexioClient = new BexioClient();
      const supabase = createClient();
      for (const list of listsToBeArchived) {
        await io.runTask(`update-tba-project-${list.clickup_list_id}`, async () => {
          if (!list.clickup_list_id || !list.bexio_project_id) {
            await io.logger.log(`Skipping Project ${list.clickup_list_id} - has no bexio project`);
            return;
          }

          const canBeArchived = await io.runTask('check-if-project-can-be-archived', async () => {
            const result = await clickupClient.getOpenTasksFromList(String(list.clickup_list_id), 0);

            if (isClickUpError(result)) {
              throw new Error(`Error fetching list: ${JSON.stringify(result)}`);
            }

            return result.tasks.length === 0;
          });

          if (!canBeArchived) {
            await bexioClient.updateProject(Number(list.bexio_project_id), {
              pr_state_id: 2,
            });
            await io.logger.log(`Project ${list.clickup_list_id} has open tasks, cannot be archived`);
            const pmListId = globalSettings.pm_list_id || list.clickup_list_id;
            await clickupClient.createTask(pmListId, {
              name: `pm: ${list.clickup_name} - Projektabschluss fehlgeschlagen`,
              description: `Projekt: ${list.clickup_name}
Das Projekt konnte nicht abgeschlossen werden, da noch offene Tasks auf der Liste zu finden sind. Verfahre gem. dieser Anleitung:

Gehe in Clickup auf die entsprechende Liste "${list.clickup_name}" im Kundenordner "${list.clients?.name}"
Checke ob es noch offene Tasks hat. Falls diese geschlossen werden können, schliesse diese. Sind es Tasks, die nicht mehr zum Projekt gehören, verschiebe diese auf die richtige Liste oder erstelle eine neue gem. Anleitung oder sind die Tasks noch abzuarbeiten dann plane diese ein und schliesse das Projekt erst nach Fertigstellung ab. Am Schluss darf keine einzige Task mehr auf der Liste sein! Verfahre ggf. erneut nach dem aktuellen PM Workflow.
`,
              assignees: list.clickup_user_id ? [Number(list.clickup_user_id)] : [],
              time_estimate: 5 * 60 * 1000,
              priority: 1, // 2 = High, 1 = Urgent
              due_date: new Date().getTime(),
            });
            return;
          }

          // UPDATE CLICKUP LIST
          const response = await clickupClient.updateList(Number(list.clickup_list_id), {
            archived: true,
          });

          if (isClickUpError(response)) {
            throw new Error(`Error updating list: ${JSON.stringify(response)}`);
          }

          // UPDATE PROJECT IN DB
          const { error } = await supabase
            .from('projects')
            .update({ clickup_archived: true })
            .eq('clickup_list_id', String(list.clickup_list_id));

          if (error) {
            throw new Error(`Error updating project in db: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    // 5 UPDATE TO BE UNARCHIVED LISTS
    await io.runTask('update-to-be-unarchived-lists', async () => {
      const clickupClient = new ClickUpClient();
      for (const list of listsToBeUnarchivedOnClickup) {
        await io.runTask(`update-tbu-project-${list.clickup_list_id}`, async () => {
          if (!list.clickup_list_id || !list.bexio_project_id) {
            await io.logger.log(`Skipping Project ${list.clickup_list_id} - has no bexio project`);
            return;
          }

          // UPDATE CLICKUP LIST
          const response = await clickupClient.updateList(Number(list.clickup_list_id), {
            archived: false,
          });

          if (isClickUpError(response)) {
            throw new Error(`Error updating list: ${JSON.stringify(response)}`);
          }
        });
      }
    });
  },
});
