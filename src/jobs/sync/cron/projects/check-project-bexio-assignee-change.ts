import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { areRotatedNamesEqual } from '@/lib/utils';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

export const checkProjectBexioAssigneeChange = client.defineJob({
  id: 'check-project-bexio-assignee-change',
  name: 'Check project bexio assignee change',
  version: '1.0.0',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const employees = await io.runTask('fetch-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*');

      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const differences = await io.runTask('check-for-differences', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*, employees(*)')
        .eq('is_client_project', true)
        .eq('deleted', false);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const projectsSyncedWithClickup = data || [];

      const { data: bexioProjects, error: bexioError } = await supabase.from('bexio_projects').select('*');
      if (bexioError) {
        throw new Error(`Error fetching Bexio projects: ${JSON.stringify(bexioError)}`);
      }

      // 2.2 CHECK FOR DIFFERENT FOLDER ID
      const differences = [];
      for (const project of projectsSyncedWithClickup) {
        const curBexioProj = bexioProjects.find((proj) => Number(proj.bexio_id) === Number(project.bexio_project_id));

        if (!curBexioProj) {
          continue;
        }

        const bexioAssignedName = curBexioProj.contact_person;
        const clickupAssignedName = project.employees?.name;

        if (!bexioAssignedName) {
          await io.logger.log(
            `No assigned name found for project: ${project.clickup_list_id} | Clickup Name: ${clickupAssignedName} | Bexio Name: ${bexioAssignedName}`,
          );
          continue;
        }

        if (!areRotatedNamesEqual(bexioAssignedName, clickupAssignedName)) {
          await io.logger.log(
            `Found difference for project: ${project.clickup_list_id} | Clickup Name: ${clickupAssignedName} | Bexio Name: ${bexioAssignedName}`,
          );
          differences.push({
            clickup_list_id: project.clickup_list_id,
            bexio_assigned_name: bexioAssignedName,
            clickup_assigned_name: clickupAssignedName,
          });
        }
      }

      return differences;
    });

    await io.logger.info(`Found ${differences.length} projects with differences`);

    await io.runTask('migrate-differences', async () => {
      for (const diff of differences) {
        await io.runTask(`migrate-differences-${diff.clickup_list_id}`, async () => {
          const assignedEmployee = employees.find((e) => areRotatedNamesEqual(e.name, diff.bexio_assigned_name));
          if (!assignedEmployee?.clickup_user_id) {
            await io.logger.info(`No user id found for project: ${diff.clickup_list_id}`);
            return;
          }

          const clickupClient = new ClickUpClient();
          const response = await clickupClient.updateList(Number(diff.clickup_list_id), {
            assignee: assignedEmployee?.clickup_user_id,
          });

          if (isClickUpError(response)) {
            throw new Error(`Error syncing to clickup: ${JSON.stringify(response)}`);
          }
        });
      }
    });
  },
});
