import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { getISODateString } from '@/lib/utils';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';

export const checkProjectBexioStartEndDateChange = client.defineJob({
  id: 'check-project-bexio-start-end-change',
  name: 'Check project Bexio start/end change',
  version: '1.0.0',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const migrationData = [
      {
        nr: 'P-2024-371',
        source: 'bexio',
      },
      {
        nr: 'P-2023-168',
        source: 'clickup',
      },
      {
        nr: 'P-2023-170',
        source: 'clickup',
      },
      {
        nr: 'P-2024-370',
        source: 'bexio',
      },
      {
        nr: 'P-2024-368',
        source: 'clickup',
      },
      {
        nr: 'P-2024-330',
        source: 'bexio',
      },
      {
        nr: 'P-2024-381',
        source: 'bexio',
      },
      {
        nr: 'P-2024-372',
        source: 'clickup',
      },
      {
        nr: 'P-2023-163',
        source: 'clickup',
      },
      {
        nr: 'P-2024-308',
        source: 'clickup',
      },
      {
        nr: 'P-2023-155',
        source: 'clickup',
      },
      {
        nr: 'P-2024-300',
        source: 'clickup',
      },
      {
        nr: 'P-2023-154',
        source: 'bexio',
      },
      {
        nr: 'P-2023-159',
        source: 'clickup',
      },
      {
        nr: 'P-2023-135',
        source: 'clickup',
      },
      {
        nr: 'P-2023-134',
        source: 'bexio',
      },
      {
        nr: 'P-2023-103',
        source: 'clickup',
      },
      {
        nr: 'P-2023-120',
        source: 'clickup',
      },
      {
        nr: 'P-2023-136',
        source: 'clickup',
      },
      {
        nr: 'P-2024-207',
        source: 'bexio',
      },
      {
        nr: 'P-2022-052',
        source: 'bexio',
      },
      {
        nr: 'P-2024-182',
        source: 'clickup',
      },
      {
        nr: 'P-2023-132',
        source: 'clickup',
      },
      {
        nr: 'P-2023-099',
        source: 'clickup',
      },
      {
        nr: 'P-2024-181',
        source: 'clickup',
      },
      {
        nr: 'P-2024-150',
        source: 'clickup',
      },
      {
        nr: 'P-2024-053',
        source: 'bexio',
      },
      {
        nr: 'P-2024-049',
        source: 'bexio',
      },
      {
        nr: 'P-2023-106',
        source: 'bexio',
      },
      {
        nr: 'P-2023-092',
        source: 'clickup',
      },
    ];
    await io.logger.info(`Found ${migrationData.length} projects to migrate`);

    const differences = await io.runTask('check-for-differences', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*, employees(*)')
        .eq('is_client_project', true)
        .eq('deleted', false);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const projectsSyncedWithClickup = data || [];

      const { data: bexioProjects, error: bexioError } = await supabase.from('bexio_projects').select('*');
      if (bexioError) {
        throw new Error(`Error fetching Bexio projects: ${JSON.stringify(bexioError)}`);
      }

      // 2.2 CHECK FOR DIFFERENT FOLDER ID
      const differences = [];
      for (const project of projectsSyncedWithClickup) {
        const curBexioProj = bexioProjects.find((proj) => Number(proj.bexio_id) === Number(project.bexio_project_id));

        if (!curBexioProj) {
          continue;
        }

        const bexioStartDate = curBexioProj.start_date ? getISODateString(new Date(curBexioProj.start_date)) : null;
        const bexioEndDate = curBexioProj.end_date ? getISODateString(new Date(curBexioProj.end_date)) : null;
        const clickupStartDate = project.clickup_start ? getISODateString(new Date(project.clickup_start)) : null;
        const clickupEndDate = project.clickup_end ? getISODateString(new Date(project.clickup_end)) : null;

        if (bexioStartDate !== clickupStartDate || bexioEndDate !== clickupEndDate) {
          await io.logger.log(
            `Found difference for project: ${project.clickup_list_id} | Clickup Start: ${clickupStartDate} | Bexio Start: ${bexioStartDate} | Clickup End: ${clickupEndDate} | Bexio End: ${bexioEndDate}`,
          );
          differences.push({
            bexio_nr: curBexioProj.nr,
            bexio_project_id: curBexioProj.bexio_id,
            clickup_list_id: project.clickup_list_id,
            bexio_start_date: bexioStartDate,
            clickup_start_date: clickupStartDate,
            bexio_end_date: bexioEndDate,
            clickup_end_date: clickupEndDate,
          });
        }
      }

      return differences;
    });

    const differencesWithMissingClickupData = differences.filter(
      (diff) => !diff.clickup_start_date || !diff.clickup_end_date,
    );
    await io.logger.info(`Found ${differencesWithMissingClickupData.length} projects with missing clickup data`);

    await io.runTask('sync-missing-date-data-to-clickup', async () => {
      const clickupClient = new ClickUpClient();
      let counter = 0;
      for (const difference of differencesWithMissingClickupData) {
        counter++;
        if (counter % 100 === 0) {
          await io.wait(`yield-sync-missing-date-data-to-clickup-${counter}`, 5);
        }

        await io.runTask(`sync-missing-date-data-to-clickup-${difference.clickup_list_id}`, async () => {
          if (!difference.clickup_list_id) {
            await io.logger.info('Missing data to sync to clickup: clickup_list_id');
            return;
          }

          let listUpdate = {};
          if (difference.bexio_start_date && !difference.clickup_start_date) {
            listUpdate = {
              start_date: new Date(difference.bexio_start_date).getTime(),
            };
          }

          if (difference.bexio_end_date && !difference.clickup_end_date) {
            listUpdate = {
              ...listUpdate,
              end_date: new Date(difference.bexio_end_date).getTime(),
            };
          }

          if (Object.keys(listUpdate).length === 0) {
            await io.logger.info('No data to sync to clickup');
            return;
          }

          const clickupResponse = await clickupClient.updateList(Number(difference.clickup_list_id), listUpdate);
          if (isClickUpError(clickupResponse)) {
            throw new Error(`Error syncing to clickup: ${JSON.stringify(clickupResponse)}`);
          }
        });
      }
    });

    const differencesWithMissingClickupEndDate = differences.filter(
      (diff) => diff.clickup_start_date && !diff.clickup_end_date,
    );
    await io.logger.info(`Found ${differencesWithMissingClickupEndDate.length} projects with missing clickup end date`);

    const differencesWithMissingClickupStartDate = differences.filter(
      (diff) => !diff.clickup_start_date && diff.clickup_end_date,
    );
    await io.logger.info(
      `Found ${differencesWithMissingClickupStartDate.length} projects with missing clickup start date`,
    );

    const otherDifferences = differences.filter(
      (diff) =>
        !differencesWithMissingClickupData.includes(diff) &&
        !differencesWithMissingClickupEndDate.includes(diff) &&
        !differencesWithMissingClickupStartDate.includes(diff),
    );
    await io.logger.info(`Found ${otherDifferences.length} projects with other differences`);
    await io.logger.info(`Differences: ${JSON.stringify(otherDifferences)}`);

    // await io.runTask('migrate-other-projects', async () => {
    //   for (const diff of otherDifferences) {
    //     await io.runTask(`migrate-other-projects-${diff.clickup_list_id}`, async () => {
    //       const migration = migrationData.find((m) => m.nr === diff.bexio_nr);
    //       if (!migration) {
    //         await io.logger.info(`No migration found for project: ${diff.bexio_nr}`);
    //         return;
    //       }
    //
    //       if (migration.source === 'bexio') {
    //         if (!diff.bexio_start_date || !diff.bexio_end_date) {
    //           await io.logger.info(`No start/end date found for project: ${diff.bexio_nr}`);
    //           return;
    //         }
    //
    //         const clickupClient = new ClickUpClient();
    //         const response = await clickupClient.updateList(Number(diff.clickup_list_id), {
    //           start_date: new Date(diff.bexio_start_date).getTime(),
    //           end_date: new Date(diff.bexio_end_date).getTime(),
    //         });
    //
    //         if (isClickUpError(response)) {
    //           throw new Error(`Error syncing to clickup: ${JSON.stringify(response)}`);
    //         }
    //       } else {
    //         if (!diff.clickup_start_date || !diff.clickup_end_date) {
    //           await io.logger.info(`No start/end date found for project: ${diff.bexio_nr}`);
    //           return;
    //         }
    //
    //         const bexioClient = new BexioClient();
    //         const response = await bexioClient.updateProject(Number(diff.bexio_project_id), {
    //           start_date: diff.clickup_start_date,
    //           end_date: diff.clickup_end_date,
    //         });
    //
    //         if (isBexioError(response)) {
    //           throw new Error(`Error syncing to bexio: ${JSON.stringify(response)}`);
    //         }
    //       }
    //     });
    //   }
    // });
  },
});
