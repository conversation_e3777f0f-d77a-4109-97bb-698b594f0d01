import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { getClickUpListsJob } from '@/jobs/shared/clickup/get-clickup-lists-job';
import { createOrRenameProjectDropBoxFoldersJob } from '@/jobs';
import { convertCronToUTC } from '@/lib/utils';

export const checkProjectClientOwnershipDifferencesFullJob = client.defineJob({
  id: 'check-project-client-ownership-differences-full',
  name: 'Check project client ownership differences full',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('0 6-23 * * *') }), // every hour from 6am to 11pm
  run: async (payload, io, _) => {
    // 1 FETCH ALL CLICKUP LISTS
    const listsJob = await getClickUpListsJob.invokeAndWaitForCompletion('get-clickup-lists', {});
    if (!listsJob.ok) {
      throw new Error(`Error fetching lists: ${JSON.stringify(listsJob)}`);
    }

    const { clientLists } = listsJob.output;

    // 2 FETCH ALL PROJECTS
    const projects = await io.runTask('fetch-all-projects', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('projects').select('*, clients(*)').eq('deleted', false);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    // 3 CHECK FOR DIFFERENT FOLDER ID
    const differences = await io.runTask('check-for-differences', async () => {
      const differences = [];

      for (const project of projects) {
        const list = clientLists.find((list) => list.id == project.clickup_list_id);

        if (!list) {
          await io.logger.info(`No list found for project: ${JSON.stringify(project)}`);
          continue;
        }

        if (list.folder.id !== project.clickup_folder_id) {
          await io.logger.log(`Found difference for project: ${JSON.stringify(project)}`);
          differences.push({ ...project, clickup_folder_id: list.folder.id });
        }
      }

      return differences;
    });

    // 4 UPDATE PROJECTS
    await io.runTask('update-projects', async () => {
      for (const project of differences) {
        await io.runTask(`update-project-${project.id}`, async () => {
          // 4.1 UPDATE PROJECT IN DB
          const supabase = createClient();
          const { data, error } = await supabase
            .from('projects')
            .update({
              clickup_folder_id: project.clickup_folder_id,
            })
            .eq('id', project.id)
            .select('*, clients(*)')
            .single();

          if (error) {
            throw new Error(`Error updating project: ${JSON.stringify(error)}`);
          }

          await io.runTask(`update-bexio-project-${project.id}`, async () => {
            // UPDATE PROJECT CONTACT IN BEXIO
            if (!data?.clients?.bexio_contact_id) {
              await io.logger.log(`Skipping project ${project.id} - has no client`);
              return;
            }

            if (!project.bexio_project_id) {
              await io.logger.log(`Skipping project ${project.id} - has no bexio project`);
              return;
            }

            // 4.2 FETCH PROJECT IN BEXIO
            const bexioClient = new BexioClient();
            const bexioProject = await bexioClient.getProject(project.bexio_project_id);
            if (!bexioProject || isBexioError(bexioProject) || !bexioProject.name) {
              throw new Error(`Error fetching project in bexio: ${JSON.stringify(bexioProject)}`);
            }

            // 4.3 UPDATE PROJECT IN BEXIO
            const updatedBexioProject = {
              name: bexioProject.name,
              pr_state_id: bexioProject.pr_state_id,
              pr_project_type_id: bexioProject.pr_project_type_id,
              contact_id: data.clients.bexio_contact_id,
              user_id: bexioProject.user_id,
            };

            const updatedProject = await bexioClient.updateProject(project.bexio_project_id, updatedBexioProject);
            if (!updatedProject || isBexioError(updatedProject) || !updatedProject.id) {
              throw new Error(`Error updating project in bexio: ${JSON.stringify(updatedProject)}`);
            }
          });

          if (!data.clients?.name) {
            await io.logger.error(`Skipping project ${project.id} - has no client`);
            return;
          }

          await createOrRenameProjectDropBoxFoldersJob.invokeAndWaitForCompletion(
            'create-or-rename-project-drop-box-folders',
            {
              projects: [{ ...project, clients: data.clients }],
            },
          );
        });
      }
    });
  },
});
