import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { isBexioError } from '@/data/types/bexio.types';
import { BexioClient } from '@/data/bexio-clients';
import { createClient } from '@/data/supabase-server';
import { isClickUpError } from '@/data/types/clickup.types';
import { ClickUpClient } from '@/data/clickup-client';
import { convertCronToUTC } from '@/lib/utils';
import { BexioInvoiceStatus } from '@/lib/constants';
import { Tables } from '@/types/gen/database-table';

export const projectReportingJob = client.defineJob({
  id: 'project-reporting',
  name: 'Project Reporting Job',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('35 6-23 * * *') }),
  run: async (payload, io, _) => {
    const [skipArchived] = await io.runTask('get-settings', async () => {
      return [new Date().getMinutes() !== 35];
    });

    const bexioProjects = await io.runTask('fetch-bexio-ids', async () => {
      const bexioClient = new BexioClient();
      const projects = await bexioClient.getProjects();

      if (isBexioError(projects)) {
        throw new Error(`Error fetching bexio project: ${JSON.stringify(projects)}`);
      }

      const upsert = projects.map((p) => {
        return {
          bexio_id: p.id,
          name: p.name,
          nr: String(p.nr),
          status: p.pr_state_id === 1 ? 'Offen' : p.pr_state_id === 2 ? 'Aktiv' : 'Archiviert',
          bexio_client_id: p.contact_id,
          budget: Number(p.pr_budget_type_amount),
          bexio_user_id: p.user_id,
          end_date: p.end_date,
          start_date: p.start_date,
        };
      });

      const supabase = createClient();
      const { error } = await supabase.from('bexio_projects').upsert(upsert, { onConflict: 'bexio_id' });

      if (error) {
        throw new Error(`Error upserting bexio projects: ${JSON.stringify(error)}`);
      }

      return projects
        .filter((p) => (skipArchived ? p.pr_state_id != 3 : true))
        .map((p) => {
          return { id: p.id, contact_id: p.contact_id, archived: p.pr_state_id === 3 };
        });
    });

    await io.runTask('fetch-invoice-ids', async () => {
      let iter = 0;
      for (const proj of bexioProjects) {
        iter++;

        if (iter % 100 === 0) {
          await io.wait(`yield-fetch-invoice-ids-${iter}`, 1);
        }

        await io.runTask(`fetch-invoice-ids-for-${proj.id}`, async () => {
          if (skipArchived && proj.archived) {
            return;
          }

          const bexioClient = new BexioClient();
          const invoices = await bexioClient.getInvoicesFromClient(proj.contact_id);

          if (isBexioError(invoices)) {
            throw new Error(`Error fetching invoices: ${JSON.stringify(invoices)}`);
          }

          const filteredInvoices = invoices
            .filter((i) => i.project_id === proj.id)
            .filter((invoice) => invoice.kb_item_status_id !== 19 && invoice.kb_item_status_id !== 31);
          const totalNet = filteredInvoices.reduce((acc, i) => acc + parseFloat(i.total_net), 0);
          const invoiceIds = filteredInvoices.map((i) => i.id);
          const updateValues = { factorized_amount: totalNet, invoice_ids: invoiceIds };

          const supabase = createClient();
          const { error } = await supabase.from('bexio_projects').update(updateValues).eq('bexio_id', proj.id);

          if (error) {
            throw new Error(`Error updating bexio project: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    await io.runTask('fetch-invoice-details', async () => {
      const supabase = createClient();
      const { data: bexioProjects, error } = await supabase
        .from('bexio_projects')
        .select('bexio_id, invoice_ids, status');
      const filteredBexioProjects =
        bexioProjects?.filter((p) => {
          return skipArchived ? p.status !== 'Archiviert' : true;
        }) || [];

      if (error) {
        throw new Error(`Error fetching bexio projects: ${JSON.stringify(error)}`);
      }

      let iter = 0;
      for (const proj of filteredBexioProjects) {
        iter++;

        if (iter % 100 === 0) {
          await io.wait(`yield-fetch-invoice-details-${iter}`, 1);
        }

        await io.runTask(`fetch-invoice-details-${proj.bexio_id}`, async () => {
          const servicesAmounts: number[] = [];
          const tradeAmounts: number[] = [];

          for (const invoiceId of proj.invoice_ids || []) {
            const [currServices, currTrades] = await io.runTask(
              `fetch-invoice-details-${proj.bexio_id}-${invoiceId}`,
              async () => {
                const bexioClient = new BexioClient();
                const invoice = await bexioClient.getInvoice(invoiceId);

                if (isBexioError(invoice)) {
                  throw new Error(`Error fetching invoice: ${JSON.stringify(invoice)}`);
                }

                if (invoice.kb_item_status_id === 19 || invoice.kb_item_status_id === 31) {
                  await io.logger.info(`Skipping invoice ${invoiceId} - has status 19 (Canceled) or 31 (Unpaid)`);
                  return [0, 0];
                }

                const services = invoice.positions
                  .filter((p) => p.account_id === 150)
                  .reduce((acc, p) => acc + parseFloat(p.position_total || '0'), 0);
                const trades = invoice.positions
                  .filter((p) => p.account_id && p.account_id !== 150)
                  .reduce((acc, p) => acc + parseFloat(p.position_total || '0'), 0);
                const total = services + trades;
                const servicesNet = (services / total) * Number(invoice.total_net);
                const tradesNet = (trades / total) * Number(invoice.total_net);

                const supabase = createClient();
                const { error: invoiceError } = await supabase
                  .from('bexio_invoices')
                  .upsert(
                    {
                      id: invoice.id,
                      document_nr: invoice.document_nr,
                      contact_id: invoice.contact_id,
                      project_id: invoice.project_id,
                      total: Number(invoice.total),
                      total_gross: Number(invoice.total_gross),
                      total_net: Number(invoice.total_net),
                      total_taxes: Number(invoice.total_taxes),
                      total_received_payments: Number(invoice.total_received_payments),
                      total_remaining_payments: Number(invoice.total_remaining_payments),
                      services_amount: servicesNet,
                      trades_amount: tradesNet,
                      valid_from: invoice.is_valid_from,
                      valid_to: invoice.is_valid_to,
                      status: BexioInvoiceStatus[invoice.kb_item_status_id],
                    },
                    { onConflict: 'id' },
                  )
                  .select();

                if (invoiceError) {
                  await io.logger.error(`Error upserting invoice ${invoice.id} to Supabase: ${invoiceError}`);
                }

                return [servicesNet, tradesNet];
              },
            );

            servicesAmounts.push(currServices);
            tradeAmounts.push(currTrades);
          }

          const servicesTotal = servicesAmounts.reduce((acc, s) => acc + s, 0);
          const tradesTotal = tradeAmounts.reduce((acc, t) => acc + t, 0);
          const updateValues = { services_amount: servicesTotal, trades_amount: tradesTotal };
          const { error } = await supabase.from('bexio_projects').update(updateValues).eq('bexio_id', proj.bexio_id);

          if (error) {
            throw new Error(`Error updating bexio project: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    await io.runTask('fetch-project-times', async () => {
      const supabase = createClient();
      const { data: bexioProjects, error } = await supabase.from('bexio_projects').select('bexio_id, status');
      const filteredBexioProjects =
        bexioProjects?.filter((p) => {
          return skipArchived ? p.status !== 'Archiviert' : true;
        }) || [];

      if (error) {
        throw new Error(`Error fetching bexio projects: ${JSON.stringify(error)}`);
      }

      let iter = 0;
      for (const proj of filteredBexioProjects) {
        iter++;

        if (iter % 100 === 0) {
          await io.wait(`yield-fetch-project-times-${iter}`, 1);
        }

        await io.runTask(`fetch-project-times-${proj.bexio_id}`, async () => {
          const supabase = createClient();
          const { data: dbProject, error: dbProjectError } = await supabase
            .from('projects')
            .select('clickup_list_id, clickup_archived, clickup_end')
            .eq('bexio_project_id', proj.bexio_id)
            .single();

          if (dbProjectError) {
            throw new Error(`Error fetching db project: ${JSON.stringify(dbProjectError)}`);
          }

          if (!dbProject.clickup_list_id) {
            await io.logger.error(`No clickup list id found for project: ${JSON.stringify(dbProject)}`);
            return;
          }

          let clickupTasksTimeSpent = 0,
            clickupTasksTimeEstimated = 0,
            clickupOpenTasksTimeEstimated = 0;
          let curTimeSpent = 0,
            curTimeEstimated = 0,
            curOpenTimeEstimated = 0;
          let page = 0;
          let lastPage = false;
          while (!lastPage) {
            [curTimeSpent, curTimeEstimated, curOpenTimeEstimated, lastPage] = await io.runTask(
              `fetch-tasks-time-page-${page}-list-${dbProject.clickup_list_id}`,
              async () => {
                const clickupClient = new ClickUpClient();
                const tasksResponse = await clickupClient.getTasksFromList(String(dbProject.clickup_list_id), page);

                if (isClickUpError(tasksResponse)) {
                  throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)} at page ${page}`);
                }

                const timeSpent = tasksResponse.tasks.reduce((acc, task) => {
                  return acc + (task.time_spent ? Number(task.time_spent) : 0);
                }, 0);
                const timeEstimated = tasksResponse.tasks.reduce((acc, task) => {
                  return acc + (task.time_estimate ? Number(task.time_estimate) : 0);
                }, 0);
                const openTimeEstimated = tasksResponse.tasks.reduce((acc, task) => {
                  if (task.status.type !== 'closed' && !task.archived) {
                    return acc + (task.time_estimate ? Number(task.time_estimate) : 0);
                  }
                  return acc;
                }, 0);

                return [timeSpent, timeEstimated, openTimeEstimated, tasksResponse.last_page];
              },
            );

            clickupTasksTimeSpent += curTimeSpent;
            clickupTasksTimeEstimated += curTimeEstimated;
            clickupOpenTasksTimeEstimated += curOpenTimeEstimated;
            page++;
          }

          const effectiveCost = await io.runTask(`calc-effective-cost-${dbProject.clickup_list_id}`, async () => {
            const aWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            if (dbProject.clickup_archived && dbProject.clickup_end && new Date(dbProject.clickup_end) < aWeekAgo) {
              return undefined;
            }

            const supabase = createClient();
            const { data: clickupTasks, error } = await supabase
              .from('tasks')
              .select('clickup_task_id, time_entries(id, billable, billable_amount)')
              .eq('clickup_list_id', dbProject.clickup_list_id!);

            if (error) {
              throw new Error(`Error fetching clickup tasks: ${JSON.stringify(error)}`);
            }

            const accAmount = clickupTasks.reduce((acc, cur) => {
              const timeEntries = cur.time_entries.filter((entry) => entry.billable && entry.billable_amount);
              return acc + timeEntries.reduce((acc, cur) => acc + Number(cur.billable_amount), 0);
            }, 0);

            return accAmount;
          });

          let updateValues: Partial<Tables<'bexio_projects'>> = {
            time_spent: clickupTasksTimeSpent / 1000 / 60 / 60,
            time_estimate: clickupTasksTimeEstimated / 1000 / 60 / 60,
            time_estimate_open: clickupOpenTasksTimeEstimated / 1000 / 60 / 60,
          };

          if (effectiveCost !== undefined) {
            updateValues.effective_cost = effectiveCost;
          }

          const { error } = await supabase.from('bexio_projects').update(updateValues).eq('bexio_id', proj.bexio_id);

          if (error) {
            throw new Error(`Error updating bexio project: ${JSON.stringify(error)}`);
          }
        });
      }
    });

    const generalSettings = await io.runTask('fetch-general-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching general settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    await io.runTask('calc-bexio-project-metrics', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('bexio_projects').select('*');

      if (error) {
        throw new Error(`Error fetching bexio projects: ${JSON.stringify(error)}`);
      }

      const hourlyRate = generalSettings.general_hourly_rate;
      const projectData = data?.map((proj) => {
        const timeSpent = proj.time_spent || 0;
        const timeEstimateOpen = proj.time_estimate_open || 0;

        let progressPercentage =
          ((proj.time_spent || 0) / ((proj.time_spent || 0) + (proj.time_estimate_open || 0))) * 100;
        progressPercentage = proj.time_spent === 0 ? 0 : progressPercentage;
        progressPercentage = isNaN(progressPercentage) ? 0 : progressPercentage;

        if (timeSpent + timeEstimateOpen === 0 && proj.status === 'Archiviert') {
          progressPercentage = 100;
        }
        if (timeSpent + timeEstimateOpen === 0 && proj.status !== 'Archiviert') {
          progressPercentage = 0;
        }

        const estimatedCost = (proj.time_estimate_open || 0) * hourlyRate + (proj.effective_cost || 0);
        return {
          ...proj,
          difference_time: (proj.time_estimate || 0) - (proj.time_spent || 0),
          difference_time_percentage: ((100 * Number(proj.time_spent)) / Number(proj.time_estimate) - 100) * -1,
          difference_budget: (proj.budget || 0) - (proj.effective_cost || 0),
          difference_budget_percentage: ((100 * Number(proj.effective_cost)) / Number(proj.budget) - 100) * -1,
          progress_percentage: isNaN(progressPercentage) ? 0 : progressPercentage,
          estimated_cost: estimatedCost,
          difference_estimated_cost: (proj.budget || 0) - estimatedCost,
          difference_estimated_cost_percentage: ((100 * estimatedCost) / (proj.budget || 0) - 100) * -1,
        };
      });

      const { error: updateError } = await supabase
        .from('bexio_projects')
        .upsert(projectData, { onConflict: 'bexio_id' });

      if (updateError) {
        throw new Error(`Error updating bexio projects: ${JSON.stringify(updateError)}`);
      }
    });
  },
});
