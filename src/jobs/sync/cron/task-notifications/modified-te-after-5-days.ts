import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertUNIXTimestampToDateTimeString, prettyPrintTime } from '@/lib/utils';
import { IO } from '@trigger.dev/sdk';

type DbTimeEntry = {
  clickup_time_entry_id: string | null;
  clickup_task_id: string | null;
  clickup_start: number | null;
  clickup_end: number | null;
  clickup_duration: number | null;
  tasks?: {
    name: string;
    projects?: {
      name: string | null;
    } | null;
  } | null;
  employees?: { name: string } | null;
};

type ClickupTimeEntryLight = {
  clickup_time_entry_id: string;
  clickup_description: string | null;
  clickup_task_id: string | null;
  clickup_user_id: string | null;
  clickup_duration: number | null;
  clickup_start: number | null;
  clickup_end: number | null;
  clickup_task_tag: string | null;
  billable: boolean | null;
};

export async function modifiedTeAfter5Days(
  prevTimeEntry: DbTimeEntry | null,
  newTimeEntry: ClickupTimeEntryLight,
  timeEntriesLocationMap: Map<string, { task_name: string; list_name: string }>,
  listId: string,
  io: IO,
) {
  if (!prevTimeEntry || !newTimeEntry) {
    await io.logger.error(`No time entries to notify`);
    return;
  }

  const clickupClient = new ClickUpClient();
  const newTask = await clickupClient.createTask(listId, {
    name: `hr: alter Timentry wurde verändert - ${prevTimeEntry.tasks?.name}`,
    description: `Alter Timentry (älter als 5 Tage) wurde nun nachträglich verändert. Checke:

Timeentry von: ${prevTimeEntry.employees?.name}

Datum Änderung: ${new Date().toISOString().slice(0, 10)}

Time-Entry ID: ${prevTimeEntry.clickup_time_entry_id}

Liste alt: ${prevTimeEntry.tasks?.projects?.name}
Liste neu: ${timeEntriesLocationMap.get(newTimeEntry.clickup_time_entry_id)?.list_name}

Task alt: ${prevTimeEntry.tasks?.name}
Task neu: ${timeEntriesLocationMap.get(newTimeEntry.clickup_time_entry_id)?.task_name}

Alter Zeiteintrag war: ${convertUNIXTimestampToDateTimeString(Number(prevTimeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(prevTimeEntry.clickup_end)} | ${prettyPrintTime(prevTimeEntry.clickup_duration)}
Neuer Zeiteintrag ist: ${convertUNIXTimestampToDateTimeString(Number(newTimeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(newTimeEntry.clickup_end)} | ${prettyPrintTime(newTimeEntry.clickup_duration)}
`,
    assignees: [process.env.CLICKUP_ADMIN_USER_ID],
    priority: 1,
    time_estimate: 10 * 60 * 1000, // 10 minutes
    due_date: new Date().getTime(),
  });

  if (isClickUpError(newTask)) {
    await io.logger.error(`Error creating task: ${JSON.stringify(newTask)}`);
    return;
  }

  return newTask;
}
