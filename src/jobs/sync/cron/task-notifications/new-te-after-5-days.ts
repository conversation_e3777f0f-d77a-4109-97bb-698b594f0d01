import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertUNIXTimestampToDateTimeString } from '@/lib/utils';
import { IO } from '@trigger.dev/sdk';

type DbTimeEntry = {
  clickup_time_entry_id: string | null;
  clickup_task_id: string | null;
  clickup_start: number | null;
  clickup_end: number | null;
  tasks?: {
    name: string;
  } | null;
  employees?: { name: string } | null;
};

export async function newTeAfter5Days(timeEntry: DbTimeEntry, listId: string, io: IO) {
  const clickupClient = new ClickUpClient();
  const newTask = await clickupClient.createTask(listId, {
    name: `hr: Timentry älter als 5 Tage hinzugefügt - ${timeEntry.tasks?.name}`,
    description: `Jemand hat manuell einen Timeentry nachträglich gebucht für älter als vor 5 Tagen. Checke:

User: ${timeEntry.employees?.name}
Datum Änderung: ${new Date().toISOString().slice(0, 10)}
Task: ${timeEntry.tasks?.name}
Timentry ID: ${timeEntry.clickup_time_entry_id}
Neuer Zeiteintrag ist: ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(timeEntry.clickup_end)}
`,
    assignees: [process.env.CLICKUP_ADMIN_USER_ID],
    priority: 1,
    time_estimate: 10 * 60 * 1000, // 10 minutes
    due_date: new Date().getTime(),
  });

  if (isClickUpError(newTask)) {
    await io.logger.error(`Error creating task: ${JSON.stringify(newTask)}`);
    return;
  }

  return newTask;
}
