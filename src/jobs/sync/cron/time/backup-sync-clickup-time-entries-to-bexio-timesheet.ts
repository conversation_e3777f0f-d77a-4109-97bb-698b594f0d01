import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertCronToUTC, convertUNIXTimestampToDateTimeString } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { tempUnarchive } from '@/jobs/shared/bexio-projects/temp-unarchive';
import { notifyRetryFailure } from '@/jobs/shared/retry-on-failure';

export const backupSyncClickupTimeEntriesToBexioTimesheetJob = client.defineJob({
  id: 'backup-sync-clickup-time-entries-to-bexio-timesheet',
  name: 'Backup Sync clickup time entries to bexio timesheet',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('0 3 * * *') }), // Every day at 3:00 AM
  run: async (payload, io, _) => {
    // 0 PREPARE CACHED CONSTANTS
    const [startUnixMs, endUnixMs] = await io.runTask('prepare-cached-constants', async () => {
      const today = new Date();
      const startUnixMs = new Date().setDate(today.getDate() - 30);
      const endUnixMs = new Date().getTime();
      // const startUnixMs = new Date('2024-04-01').getTime();
      // const endUnixMs = new Date('2024-05-01').getTime();

      return [startUnixMs, endUnixMs];
    });

    // 2.2 UPDATE CLICKUP TIME ENTRIES IN DB
    await io.runTask('update-clickup-time-entries-in-db-and-bexio', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select(
          '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
        )
        .gte('clickup_start', startUnixMs)
        .lte('clickup_end', endUnixMs);

      if (error) {
        throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
      }

      const timeEntriesToUpdate = data || [];

      if (timeEntriesToUpdate.length == 0) {
        await io.logger.info(`No time entries to update`);
        return;
      }

      await io.logger.info(`Updating ${timeEntriesToUpdate.length} time entries`);
      const clientTimeEntries = timeEntriesToUpdate.filter((te) => te.billable);

      // 2.2.2 PUSH UPDATED CLICKUP TIME ENTRIES TO BEXIO
      const bexioClient = new BexioClient();
      let count = 0;
      for (const timeEntry of clientTimeEntries) {
        await io.runTask(`push-updated-clickup-time-entries-to-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
          if (!timeEntry.business_activities?.bexio_business_activity_id) {
            await io.logger.info(
              `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio business activity`,
            );
            return;
          }

          if (!timeEntry.tasks?.projects?.bexio_project_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio project`);
            return;
          }

          if (!timeEntry.bexio_timesheet_id) {
            await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`);
            return;
          }

          const timeSheet = {
            user_id: timeEntry.employees?.bexio_user_id || 1,
            client_service_id: timeEntry.business_activities?.bexio_business_activity_id,
            text: timeEntry.tasks?.name,
            allowable_bill: true,
            pr_project_id: timeEntry.tasks?.projects?.bexio_project_id,
            pr_package_id: timeEntry.tasks?.bexio_work_package_id,
            tracking: {
              type: 'range',
              start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
              end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
            },
          };
          await io.logger.info(`Updating time sheet ${timeEntry.bexio_timesheet_id}: ${JSON.stringify(timeSheet)}`);

          let updatedTimeSheet = await bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet);
          if (isBexioError(updatedTimeSheet)) {
            if (updatedTimeSheet.error_code == 422 || updatedTimeSheet.error_code == 404) {
              updatedTimeSheet = await io.runTask(
                `unarchive-bexio-project-for-update-te-${timeEntry.clickup_time_entry_id}`,
                () =>
                  tempUnarchive(Number(timeEntry.tasks?.projects?.bexio_project_id), () =>
                    bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet),
                  ),
              );

              if (isBexioError(updatedTimeSheet)) {
                throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
              }
            } else {
              throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
            }
          }

          if (!updatedTimeSheet) {
            await io.logger.error('No response from updating timesheet to bexio');
          }
        });

        count++;
        if (count % 100 === 0) {
          await io.wait(`yield-push-updated-clickup-time-entries-to-bexio-${count}`, 5);
        }
      }
    });
  },
  onFailure: async (notification) => {
    if (notification.isTest) {
      return;
    }

    const retryCount = Number(notification.invocation.context.retryCount || 0);
    if (retryCount < 5) {
      await backupSyncClickupTimeEntriesToBexioTimesheetJob.invoke(notification.invocation.payload, {
        context: { retryCount: retryCount + 1 },
      });
      return;
    }

    // Runs after retryCount >= 5
    await notifyRetryFailure(notification, 5);
  },
});
