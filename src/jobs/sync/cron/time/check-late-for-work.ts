import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertCronToUTC, prettyPrintTsToDate, prettyPrintTsToTime } from '@/lib/utils';

export const checkLateForWork = client.defineJob({
  id: 'check-late-for-work',
  name: 'Check Late For Work',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('30 9 * * *') }), // every day at 9:30
  run: async (payload, io, _) => {
    const weekday = new Date().getDay();
    await io.logger.info(`Weekday: ${weekday}`);
    if (weekday === 0 || weekday === 6) {
      await io.logger.info('Sunday or Saturday. Skipping job.');
      return;
    }

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const employeesThatShouldWork = await io.runTask('check-if-employee-should-work', async () => {
      const todayDateStr = new Date().toISOString().slice(0, 10);
      const supabase = createClient();
      const { data, error } = await supabase
        .from('working_hours')
        .select('*, employees(*)')
        .eq('date', todayDateStr)
        .neq('hours', 0);

      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      return data.map((wh) => {
        return { ...wh.employees, hours: wh.hours };
      });
    });

    const clickupUserIds = employeesThatShouldWork
      .map((user) => user.clickup_user_id)
      .filter((id) => id)
      .map((id) => id!);

    const earliestTimeEntriesPerUser = await io.runTask('fetch-time-entries-today', async () => {
      const start = new Date().setHours(5, 0, 0, 0);
      const clickupClient = new ClickUpClient();
      const timeEntries = await clickupClient.getAllTimeEntriesFrom(clickupUserIds.join(','), start);

      if (isClickUpError(timeEntries)) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(timeEntries)}`);
      }

      let allTimeEntries = timeEntries.data;
      for (const userId of clickupUserIds) {
        const timeEntry = await io.runTask(`fetch-running-time-entry-${userId}`, async () => {
          const runningTimeEntry = await clickupClient.getRunningTimeEntry(userId);
          if (isClickUpError(runningTimeEntry)) {
            throw new Error(`Error fetching running time entry: ${JSON.stringify(runningTimeEntry)}`);
          }

          return runningTimeEntry.data;
        });

        if (timeEntry) {
          allTimeEntries = allTimeEntries.concat(timeEntry);
        }
      }

      await io.logger.info(`Fetched ${allTimeEntries.length} time entries`);

      // filter time entries where start is between start and end or end is between start and end
      const dataWoPTO = allTimeEntries.filter(
        (te) => String(te.task_location?.list_id) !== String(globalSettings.pto_list_id),
      );

      // Get the earliest start time for each user
      return dataWoPTO?.reduce(
        (acc, curr) => {
          const name = String(curr.user.username);
          if (!acc[name]) {
            acc[name] = Number(curr.start);
          } else {
            acc[name] = Math.min(acc[name], Number(curr.start));
          }
          return acc;
        },
        {} as { [key: string]: number },
      );
    });

    const ptoTasksForToday = await io.runTask('fetch-pto-tasks-for-today', async () => {
      const start = new Date().setHours(0, 0, 0, 0);
      const end = new Date().setHours(23, 59, 59, 999);

      const clickupClient = new ClickUpClient();
      const tasksResponse = await clickupClient.getCustomTasksQuery(String(globalSettings.pto_list_id), 0, {
        include_closed: true,
        subtasks: true,
        due_date_gt: start,
        due_date_lt: end,
      });

      if (isClickUpError(tasksResponse)) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)}`);
      }

      return tasksResponse.tasks;
    });

    const usersSummary = await io.runTask('build-users-summary', async () => {
      return employeesThatShouldWork.map((employee) => {
        const name = String(employee.name);
        const startTime = earliestTimeEntriesPerUser[name] || null;
        const ptoTask = ptoTasksForToday.find((task) =>
          task.assignees.map((a) => a.id).includes(Number(employee.clickup_user_id)),
        );
        const ptoOptions = ptoTask?.custom_fields?.find((field) => field.name === 'PTO Typ')?.type_config?.options;
        const ptoTypeIndex = ptoTask?.custom_fields?.find((field) => field.name === 'PTO Typ')?.value;
        const isFreeTimePTO =
          ptoOptions?.find((option) => Number(option.orderindex) === Number(ptoTypeIndex))?.name === 'Freizeit';

        return {
          name,
          startTime,
          hasFreeTimePTO: isFreeTimePTO,
          ptoTask: ptoTask ? { name: ptoTask.name, url: ptoTask.url, id: ptoTask.id } : null,
        };
      });
    });

    await io.runTask('send-clickup-notification', async () => {
      // 09:00 (+ 5 min slack)
      const officialStart = new Date().setHours(9, 5, 0, 0);

      for (const user of usersSummary) {
        if (user.startTime && user.startTime <= officialStart) {
          await io.logger.info(`Skipping user ${user.name} - has correct start time ${user.startTime}`);
          continue;
        }

        if (user.ptoTask && !user.hasFreeTimePTO) {
          await io.logger.info(`Skipping user ${user.name} - has PTO task but no free time PTO`);
          continue;
        }

        const clickupClient = new ClickUpClient();
        const startDate = prettyPrintTsToDate(user.startTime || new Date());
        await clickupClient.createTask(String(globalSettings.hr_list_id), {
          name: `hr: Zu spät - ${user.name}`,
          description: `${user.name} kam gem. System zu spät oder ist womöglich krank.

    User: ${user.name}
    Datum: ${startDate}
    Erster Timeentrystart: ${user.startTime ? prettyPrintTsToTime(user.startTime) : '-'}
    PTO Aufgabe Freizeit vorhanden?: ${user.hasFreeTimePTO ? 'Ja' : 'Nein'} ${user.ptoTask ? user.ptoTask.url : ''}

    Person krank gemeldet?
    Wenn ja, dann Eingabe via PTO für kranke Person machen um Workflow auszulösen.
    Zum PTO Formular: https://app.clickup.com/9010042446/v/fm/8cgmnje-45635

    Keine Infos zum Verbleib der Person?
    Person anzurufen. Erreichst du  die Person nicht bis um 11 Uhr und man hat keine Informationen, ist der Notfallkontakt zu kontaktieren (den Notfallkontakt kriegt ihr beim Personalverantwortlichen).
        `,
          assignees: [process.env.CLICKUP_ADMIN_USER_ID],
          priority: 1,
          time_estimate: 10 * 60 * 1000,
          due_date: new Date().getTime(),
        });
      }
    });
  },
});
