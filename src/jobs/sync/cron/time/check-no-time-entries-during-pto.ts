import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask, isClickUpError } from '@/data/types/clickup.types';
import {
  convertCronToUTC,
  convertUNIXTimestampToDateString,
  getISODateString,
  msToHours,
  prettyPrintTsToDate,
} from '@/lib/utils';
import { getAllPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';

export const checkNoTimeEntriesDuringPto = client.defineJob({
  id: 'check-no-time-entries-during-pto',
  name: 'Check No Time Entries During PTO',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('30 11 * * *') }), // every day at 11:30
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const workingHoursEmployeesLast3Months = await io.runTask(
      'fetch-working-hours-employees-last-3-months',
      async () => {
        const supabase = createClient();
        const threeMonthsAgo = new Date(new Date().setDate(new Date().getDate() - 90)).setHours(0, 0, 0, 0);
        const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
        const { data, error } = await supabase
          .from('working_hours')
          .select('*, employees(*)')
          .gte('date', getISODateString(new Date(threeMonthsAgo)))
          .lte('date', getISODateString(new Date(yesterday)));

        if (error) {
          throw new Error(`Error fetching working hours: ${JSON.stringify(error)}`);
        }

        const workingHoursEmployeeMap = new Map<string, { date: string; hours: number }[]>();
        for (const wh of data || []) {
          const userId = wh.employees?.clickup_user_id || 'unknown';
          if (!workingHoursEmployeeMap.has(userId)) {
            workingHoursEmployeeMap.set(userId, [{ date: wh.date, hours: wh.hours }]);
            continue;
          }

          const prev = workingHoursEmployeeMap.get(userId)!;
          const newEntry = prev.concat([{ date: wh.date, hours: wh.hours }]);
          workingHoursEmployeeMap.set(userId, newEntry);
        }

        return Object.fromEntries(workingHoursEmployeeMap);
      },
    );

    const ptoTimesLast3Months = await io.runTask('fetch-pto-times-last-3-months', async () => {
      const allPtoTimes = await getAllPTOTimes(globalSettings.pto_list_id!);
      const threeMonthsAgo = new Date(new Date().setDate(new Date().getDate() - 90)).setHours(0, 0, 0, 0);
      const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
      return allPtoTimes.filter(
        (ptoTime) =>
          ptoTime.start_date && Number(ptoTime.start_date) > threeMonthsAgo && Number(ptoTime.start_date) < yesterday,
      );
    });

    const employeePtoDaysForEachDay = await io.runTask('calc-employee-pto-days-for-each-day', async () => {
      const employeePtoDays = new Map<string, ClickUpTask[]>(); // Date -> Employee Clickup User IDs
      for (const ptoTask of ptoTimesLast3Months) {
        if (!ptoTask.start_date) {
          await io.logger.info(`No start date found for pto task: ${JSON.stringify(ptoTask)}`);
          continue;
        }

        const date = getISODateString(new Date(Number(ptoTask.start_date)));
        if (!employeePtoDays.has(date)) {
          employeePtoDays.set(date, [ptoTask]);
          continue;
        }

        const curTasks = employeePtoDays.get(date)!;
        employeePtoDays.set(date, curTasks.concat(ptoTask));
      }

      return Object.fromEntries(employeePtoDays);
    });

    const trackedTimeLast3Months = await io.runTask('fetch-tracked-time-last-3-months', async () => {
      const supabase = createClient();
      const threeMonthsAgo = new Date(new Date().setDate(new Date().getDate() - 90)).setHours(0, 0, 0, 0);
      const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999);
      const { data, error } = await supabase
        .from('time_entries')
        .select(
          'clickup_task_id, clickup_user_id, clickup_start, clickup_end, clickup_duration, tasks(clickup_list_id)',
        )
        .gte('clickup_start', threeMonthsAgo)
        .lte('clickup_start', yesterday);

      const dataFilter = data?.filter((te) => String(te.tasks?.clickup_list_id) !== String(globalSettings.pto_list_id));

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      const trackedTimeMap = new Map<string, { [key: string]: number }>();
      for (const te of dataFilter || []) {
        const userId = te.clickup_user_id || 'unknown';
        const curDate = convertUNIXTimestampToDateString(Number(te.clickup_start));
        if (!trackedTimeMap.has(userId)) {
          const newEntry = { [curDate]: Number(te.clickup_duration) };
          trackedTimeMap.set(userId, newEntry);
          continue;
        }

        // if it has the user but not the current date
        if (!trackedTimeMap.get(userId)![curDate]) {
          const newEntry = { [curDate]: Number(te.clickup_duration) };
          let prev = trackedTimeMap.get(userId)!;
          prev = { ...prev, ...newEntry };
          trackedTimeMap.set(userId, prev);
          continue;
        }

        // if it has the user and the current date
        let prev = trackedTimeMap.get(userId)!;
        const prevDuration = prev[curDate] || 0;
        prev = { ...prev, [curDate]: Number(te.clickup_duration) + prevDuration };
        trackedTimeMap.set(userId, prev);
      }

      return Object.fromEntries(trackedTimeMap);
    });

    const ptoTypesForNotification = [
      'Ferien',
      'Krankheit',
      'Unfall',
      'Mutterschaft/Vaterschaft',
      'Weiterbildung',
      'Militär/ZS',
    ];

    function getPTOTypesOfTasks(ptoTasks: ClickUpTask[]) {
      const ptoTypes = new Set<string>();
      for (const task of ptoTasks) {
        const ptoOptions = task?.custom_fields?.find((field) => field.name === 'PTO Typ')?.type_config?.options;
        const ptoTypeIndex = task?.custom_fields?.find((field) => field.name === 'PTO Typ')?.value;
        const ptoName = ptoOptions?.find((option) => Number(option.orderindex) === Number(ptoTypeIndex))?.name;
        if (ptoName) {
          ptoTypes.add(ptoName);
        }
      }

      return Array.from(ptoTypes);
    }

    function getPTOTrackedTime(clickupTasks: ClickUpTask[]) {
      return msToHours(
        clickupTasks.reduce((acc, task) => {
          return acc + Number(task.time_spent || 0) / task.assignees.length;
        }, 0),
      );
    }

    await io.runTask('check-no-tracked-time-on-pto-days', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();
      for (const [date, ptoTasks] of Object.entries(employeePtoDaysForEachDay)) {
        const clickupUserIds = ptoTasks.flatMap((ptoTask) => ptoTask.assignees.map((a) => String(a.id)));
        const uniqueClickupUserIds = Array.from(new Set(clickupUserIds));
        const { data: employees, error } = await supabase
          .from('employees')
          .select('clickup_user_id')
          .eq('allow_work_during_pto', false);

        if (error) {
          throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
        }

        const userIdsToCheck = uniqueClickupUserIds.filter((id) =>
          employees.some((e) => String(e.clickup_user_id) === String(id)),
        );
        for (const userId of userIdsToCheck) {
          const ptoTasksForUser = ptoTasks.filter((ptoTask) =>
            ptoTask.assignees.map((a) => a.id).includes(Number(userId)),
          );

          const ptoNames = getPTOTypesOfTasks(ptoTasksForUser);
          const validPTOTypes = ptoNames.filter((ptoName) => ptoTypesForNotification.includes(ptoName));
          if (validPTOTypes.length === 0) {
            // SKIPPING USERS WITHOUT PTO TYPE OR NOT IN NOTIFICATION LIST
            continue;
          }

          const trackedPTOTimeHrs = getPTOTrackedTime(ptoTasksForUser);
          const trackedClickupTimeHrs = msToHours(trackedTimeLast3Months[userId]?.[date] || 0);
          const workHours = workingHoursEmployeesLast3Months[userId]?.filter((wh) => wh.date === date)?.[0]?.hours || 0;
          if (
            trackedPTOTimeHrs === 0 ||
            trackedClickupTimeHrs === 0 ||
            trackedPTOTimeHrs + trackedClickupTimeHrs <= workHours + 0.034
          ) {
            // SKIPPING USERS WITHOUT TRACKED TIME OR NOT ENOUGH TRACKED TIME (+ 2min Puffer)
            continue;
          }

          // check if notification has already been sent
          const notificationResponse = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `NoTimeEntriesDuringPTO#${userId}#${date}`);

          if (notificationResponse.data?.length) {
            await io.logger.info(
              `Notification already sent for user ${userId} on date ${date} | Tracked PTO Time: ${trackedPTOTimeHrs}, Tracked Clickup Time: ${trackedClickupTimeHrs}, Working Hours: ${workHours}`,
            );
            continue;
          }

          const userInfo = ptoTasksForUser[0].assignees.find((a) => a.id === Number(userId))!;
          const response = await clickupClient.createTask(String(globalSettings.hr_list_id), {
            name: `hr: PTO und Timeentries - ${userInfo.username}`,
            description: `Trotz PTO weitere Zeiteinträge vorhanden - Bitte kontrollieren und überprüfen.

User: ${userInfo.username}
Sollzeit: ${workHours.toFixed(2)} h
Total Time-Entries: ${(trackedPTOTimeHrs + trackedClickupTimeHrs).toFixed(2)} h
Total PTO Time: ${trackedPTOTimeHrs.toFixed(2)} h
Total other Time-Entries: ${trackedClickupTimeHrs.toFixed(2)} h
PTO Type: ${validPTOTypes.join(', ')}
Date: ${prettyPrintTsToDate(new Date(date))}
`,
            assignees: [process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(response)) {
            throw new Error(`Error creating notification task: ${JSON.stringify(response)}`);
          }

          const { error } = await supabase.from('error_notifications').upsert({
            message_hash: `NoTimeEntriesDuringPTO#${userId}#${date}`,
          });

          if (error) {
            throw new Error(`Error saving notification to db: ${JSON.stringify(error)}`);
          }

          await io.logger.info(`Notification created for user ${userId} on ${date}`);
        }
      }
    });
  },
});
