import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC, convertUNIXTimestampToDateString } from '@/lib/utils';
import { getAllPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';
import { getPTOTypeName } from '@/lib/pto-helpers';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

// Define the structure for categorized task estimates
type CategorizedEstimates = {
  regular: number;
  regularNoRecurring: number;
  vacation: number; // <PERSON>rien
  sick: number; // Krankheit
  compensation: number; // Kompensation
  training: number; // Weiterbildung
  military: number; // Militär/ZS
  accident: number; // Unfall
  parental: number; // Mutterschaft/Vaterschaft
  holiday: number; // Feiertag
  other: number; // Sonstige
  freetime: number; // Freizeit
};

// Helper function to map PTO type names to category keys
function getPTOCategory(ptoTypeName: string | false): keyof CategorizedEstimates | 'regular' {
  if (!ptoTypeName) return 'regular';

  switch (ptoTypeName) {
    case 'Ferien':
      return 'vacation';
    case 'Krankheit':
      return 'sick';
    case 'Kompensation':
      return 'compensation';
    case 'Weiterbildung':
      return 'training';
    case 'Militär/ZS':
      return 'military';
    case 'Unfall':
      return 'accident';
    case 'Mutterschaft/Vaterschaft':
      return 'parental';
    case 'Feiertag':
      return 'holiday';
    case 'Sonstige':
      return 'other';
    case 'Freizeit':
      return 'freetime';
    default:
      return 'regular';
  }
}

// Helper function to create empty categorized estimates
function createEmptyCategorizedEstimates(): CategorizedEstimates {
  return {
    regular: 0,
    regularNoRecurring: 0,
    vacation: 0,
    sick: 0,
    compensation: 0,
    training: 0,
    military: 0,
    accident: 0,
    parental: 0,
    holiday: 0,
    other: 0,
    freetime: 0,
  };
}

// Vorlagen, SOP, Sandbox, Agency Management, Marketing Team Operations
const IGNORED_SPACES = [90152928450, 90100131196, 90100201833, 90152721646, 90152721648];

export const checkOvercapacity = client.defineJob({
  id: 'check-overcapacity',
  name: 'Check Overcapacity',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 15 * * *') }), // every day at 15:00
  run: async (payload, io, _) => {
    // Check if today is a weekday (Monday-Friday)
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    if (dayOfWeek === 0 || dayOfWeek === 6) {
      await io.logger.info('Skipping overcapacity check - job only runs Monday to Friday');
      return;
    }

    const overcapacitySettings = await io.runTask('fetch-overcapacity-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employee_capacity_settings').select('*, employees(name)');

      if (error) {
        throw new Error(`Error fetching overcapacity settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    // Calculate date range: start from the beginning of this week (Monday) to 180 days from now
    const now = new Date();
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - daysToSubtract);
    startOfWeek.setHours(0, 0, 0, 0);
    const startDate = startOfWeek.getTime();
    const endDate = new Date(new Date().setDate(new Date().getDate() + 180)).getTime();
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayStartTime = todayStart.getTime();

    const workingHoursByEmployeeByDate = await io.runTask('fetch-working-hours-next-180-days', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('working_hours')
        .select('*, employees(*)')
        .gte('date', convertUNIXTimestampToDateString(startDate))
        .lte('date', convertUNIXTimestampToDateString(endDate));

      if (error) {
        throw new Error(`Error fetching working hours: ${JSON.stringify(error)}`);
      }

      // group by employee and date
      return data?.reduce(
        (acc, curr) => {
          const name = String(curr.employees?.name);
          if (!acc[name]) {
            acc[name] = {};
          }
          acc[name][curr.date] = curr.hours;
          return acc;
        },
        {} as { [key: string]: { [key: string]: number } },
      );
    });

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const settings = await getGlobalSettings();
      if (!settings.data?.pto_list_id) {
        throw new Error('PTO list ID not found in global settings');
      }
      return settings.data;
    });

    const recurringTaskIds = await io.runTask('fetch-recurring-task-ids', async () => {
      const supabase = createClient();
      const { data: recurringTasks, error } = await supabase.from('recurring_tasks').select('clickup_task_id');

      if (error) {
        await io.logger.error(`Error fetching recurring task IDs from database: ${JSON.stringify(error)}`);
        return [];
      }

      const taskIds = recurringTasks?.map((task) => task.clickup_task_id) || [];
      await io.logger.info(`Fetched ${taskIds.length} recurring task IDs from database`);
      return taskIds;
    });

    const ptoTasksNext180Days = await io.runTask('fetch-pto-tasks-next-180-days', async () => {
      const allPtoTasks = await getAllPTOTimes(String(globalSettings.pto_list_id));

      return allPtoTasks.filter((task) => {
        if (!task.start_date || !task.due_date) return false;
        return Number(task.start_date) >= startDate && Number(task.start_date) <= endDate;
      });
    });

    const estimatedOpenTasks = await io.runTask('fetch-estimated-tasks-next-180-days', async () => {
      const supabase = createClient();
      // fetch all open tasks with clickup_time_estimate > 0 and clickup_due_date in the next 180 days
      // exclude tasks from ignored spaces
      const { data, error } = await supabase
        .from('tasks')
        .select(
          'clickup_task_id, name, clickup_time_estimate, clickup_start_date, clickup_due_date, clickup_assignees, clickup_space_id, clickup_list_id, projects(name), employees(name, user_id, clickup_user_id)',
        )
        .gt('clickup_time_estimate', 0)
        .is('clickup_date_closed', null)
        .gte('clickup_due_date', startDate)
        .lte('clickup_due_date', endDate)
        .not('clickup_space_id', 'in', `(${IGNORED_SPACES.join(',')})`);

      if (error) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
      }

      // Process regular tasks (non-PTO)
      const regularTasksEstimates =
        data?.reduce(
          (acc, curr) => {
            if (ptoTasksNext180Days.some((task) => task.id === curr.clickup_task_id)) return acc;
            // Additional safety check: skip tasks from ignored spaces
            if (curr.clickup_space_id && IGNORED_SPACES.includes(curr.clickup_space_id)) {
              console.log('ERROR: THIS SHOULD NEVER HAPPEN');
              return acc;
            }
            // Skip OKRs tasks in the Kundenportal space
            if (
              curr.projects?.name === 'OKRs' &&
              Number(curr.clickup_space_id) === Number(globalSettings?.client_portal_space_id)
            ) {
              return acc;
            }

            // Skip if not date information
            if (!curr.clickup_due_date && !curr.clickup_start_date) {
              return acc;
            }

            let days = 1;
            if (curr.clickup_start_date && curr.clickup_due_date) {
              const startDate = new Date(curr.clickup_start_date!);
              const endDate = new Date(curr.clickup_due_date!);
              days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            }

            const taskDate = curr.clickup_start_date
              ? new Date(curr.clickup_start_date)
              : new Date(curr.clickup_due_date!);
            const numAssignees = curr.clickup_assignees?.length || 1;
            for (let i = 0; i < days; i++) {
              const date = new Date(taskDate.getTime() + i * (1000 * 60 * 60 * 24));
              const dateStr = convertUNIXTimestampToDateString(date.getTime());
              if (!acc[dateStr]) {
                acc[dateStr] = {};
              }
              const name = String(curr.employees?.name);
              if (!acc[dateStr][name]) {
                acc[dateStr][name] = createEmptyCategorizedEstimates();
              }
              const timeEstimateHours = curr.clickup_time_estimate! / days / 1000 / 60 / 60 / numAssignees;
              acc[dateStr][name].regular += timeEstimateHours;

              // Add to regularNoRecurring only if this task is not a recurring task
              if (!recurringTaskIds.includes(curr.clickup_task_id!)) {
                acc[dateStr][name].regularNoRecurring += timeEstimateHours;
              }
            }
            return acc;
          },
          {} as { [key: string]: { [key: string]: CategorizedEstimates } },
        ) || {};

      // Log overcapacity cases for regular tasks
      for (const [dateStr, employees] of Object.entries(regularTasksEstimates)) {
        for (const [employeeName, estimates] of Object.entries(employees)) {
          if (estimates.regular > 8.6) {
            // Get all tasks for this employee on this date to show project/list details
            const tasksForEmployeeOnDate =
              data?.filter((curr) => {
                if (ptoTasksNext180Days.some((task) => task.id === curr.clickup_task_id)) return false;
                if (curr.clickup_space_id && IGNORED_SPACES.includes(curr.clickup_space_id)) return false;
                if (
                  curr.projects?.name === 'OKRs' &&
                  Number(curr.clickup_space_id) === Number(globalSettings?.client_portal_space_id)
                )
                  return false;
                if (String(curr.employees?.name) !== employeeName) return false;

                // Use the same logic as in regularTasksEstimates calculation
                const startDate = new Date(curr.clickup_due_date!);
                const endDate = new Date(curr.clickup_due_date! + curr.clickup_time_estimate!);
                const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

                // Check if the current dateStr falls within any of the task's distributed days
                for (let i = 0; i < days; i++) {
                  const taskDay = new Date(startDate.getTime() + i * (1000 * 60 * 60 * 24));
                  const taskDayStr = convertUNIXTimestampToDateString(taskDay.getTime());
                  if (taskDayStr === dateStr) {
                    return true;
                  }
                }
                return false;
              }) || [];

            const projectDetails = tasksForEmployeeOnDate
              .map((task) => task.projects?.name || `List ID: ${task.clickup_list_id}`)
              .join(', ');

            await io.logger.info(
              `Overcapacity detected - Employee: ${employeeName}, Date: ${dateStr}, Hours: ${estimates.regular.toFixed(2)} (${estimates.regularNoRecurring.toFixed(2)} excl. recurring), List Count: ${tasksForEmployeeOnDate.length}, Projects/Lists: ${projectDetails}`,
            );

            // Special log for extreme overcapacity (>12 hours) - show all individual tasks
            if (estimates.regular > 12) {
              const taskDetails = tasksForEmployeeOnDate
                .map((task) => {
                  // Calculate hours per day for this specific task
                  let days = 1;
                  if (task.clickup_start_date && task.clickup_due_date) {
                    const startDate = new Date(task.clickup_start_date);
                    const endDate = new Date(task.clickup_due_date);
                    days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                  }
                  const numAssignees = task.clickup_assignees?.length || 1;
                  const hoursPerDay = task.clickup_time_estimate! / days / 1000 / 60 / 60 / numAssignees;
                  return `"${task.name || 'Unnamed Task'}": ${hoursPerDay.toFixed(2)}h (ID: ${task.clickup_task_id})`;
                })
                .join(', ');

              await io.logger.info(
                `EXTREME Overcapacity (>12h) - Employee: ${employeeName}, Date: ${dateStr}, Total Hours: ${estimates.regular.toFixed(2)} (${estimates.regularNoRecurring.toFixed(2)} excl. recurring), All Tasks for this day: ${taskDetails}`,
              );
            }
          }
        }
      }

      // Process PTO tasks
      const ptoTasksEstimates = ptoTasksNext180Days.reduce(
        (acc, curr) => {
          if (!curr.start_date || !curr.due_date || !curr.time_estimate) return acc;

          const startDate = new Date(Number(curr.start_date));
          const endDate = new Date(Number(curr.due_date));
          const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) || 1;

          // Get PTO type for this task
          const ptoTypeName = getPTOTypeName(curr);
          const ptoCategory = getPTOCategory(ptoTypeName);

          for (let i = 0; i < days; i++) {
            const date = new Date(startDate.getTime() + i * (1000 * 60 * 60 * 24));
            const dateStr = convertUNIXTimestampToDateString(date.getTime());

            // Process each assignee
            for (const assignee of curr.assignees) {
              const name = String(assignee.username);

              if (!acc[dateStr]) {
                acc[dateStr] = {};
              }
              if (!acc[dateStr][name]) {
                acc[dateStr][name] = createEmptyCategorizedEstimates();
              }

              // Add estimate to the appropriate PTO category
              const estimatePerDayPerAssignee = curr.time_estimate / days / curr.assignees.length / 1000 / 60 / 60;
              acc[dateStr][name][ptoCategory] += estimatePerDayPerAssignee;
            }
          }
          return acc;
        },
        {} as { [key: string]: { [key: string]: CategorizedEstimates } },
      );

      // Merge regular tasks and PTO tasks estimates
      const mergedEstimates: { [key: string]: { [key: string]: CategorizedEstimates } } = {};

      // Add regular tasks
      for (const [dateStr, employees] of Object.entries(regularTasksEstimates)) {
        if (!mergedEstimates[dateStr]) {
          mergedEstimates[dateStr] = {};
        }
        for (const [employeeName, estimates] of Object.entries(employees)) {
          mergedEstimates[dateStr][employeeName] = { ...estimates };
        }
      }

      // Add PTO tasks
      for (const [dateStr, employees] of Object.entries(ptoTasksEstimates)) {
        if (!mergedEstimates[dateStr]) {
          mergedEstimates[dateStr] = {};
        }
        for (const [employeeName, estimates] of Object.entries(employees)) {
          if (!mergedEstimates[dateStr][employeeName]) {
            mergedEstimates[dateStr][employeeName] = createEmptyCategorizedEstimates();
          }
          // Merge PTO estimates with existing estimates
          for (const [category, value] of Object.entries(estimates)) {
            mergedEstimates[dateStr][employeeName][category as keyof CategorizedEstimates] += value;
          }
        }
      }

      return mergedEstimates;
    });

    await io.runTask('notify-daily-overcapacity', async () => {
      const clickupClient = new ClickUpClient();
      const supabase = createClient();

      // Iterate through all dates and employees in estimatedOpenTasks
      for (const [dateStr, employees] of Object.entries(estimatedOpenTasks)) {
        // Only create error notifications for today and future dates
        const dateTime = new Date(dateStr).getTime();
        if (dateTime < todayStartTime) {
          continue;
        }

        for (const [employeeName, estimates] of Object.entries(employees)) {
          // Get working hours for this employee on this date
          const workingHours = workingHoursByEmployeeByDate?.[employeeName]?.[dateStr] || 0;

          // Check if there are any PTO estimates
          const hasPtoEstimates =
            estimates.vacation > 0 ||
            estimates.sick > 0 ||
            estimates.compensation > 0 ||
            estimates.training > 0 ||
            estimates.military > 0 ||
            estimates.accident > 0 ||
            estimates.parental > 0 ||
            estimates.holiday > 0 ||
            estimates.other > 0 ||
            estimates.freetime > 0;

          // Calculate total estimated time based on PTO presence
          let totalEstimatedTime;
          if (hasPtoEstimates) {
            // Use regularNoRecurring and ignore regular when PTO exists
            totalEstimatedTime = Object.values(estimates).reduce((sum, value) => sum + value, 0) - estimates.regular;
          } else {
            // Use regular and ignore regularNoRecurring when no PTO
            totalEstimatedTime =
              Object.values(estimates).reduce((sum, value) => sum + value, 0) - estimates.regularNoRecurring;
          }

          // Find overcapacity setting for this employee
          const employeeSetting = overcapacitySettings.find((setting) => setting.employees?.name === employeeName);

          if (!employeeSetting) {
            // Skip if no overcapacity setting found for this employee
            // await io.logger.info(`No overcapacity setting found for employee: ${employeeName}`);
            continue;
          }

          // Check if daily warnings are enabled for this employee
          if (!employeeSetting.warning_day_active) {
            await io.logger.info(`Daily warning disabled for employee: ${employeeName} on ${dateStr}`);
            continue;
          }

          // Determine the appropriate threshold based on PTO types present
          let threshold = employeeSetting.overcapacity_day_with_target_hours_h || 0;

          // Check if any PTO categories have values > 0 and use specific thresholds
          if (estimates.vacation > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_vacation_h || 0;
          } else if (estimates.sick > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_sick_h || 0;
          } else if (estimates.compensation > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_compensation_h || 0;
          } else if (estimates.training > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_training_h || 0;
          } else if (estimates.military > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_military_cs_h || 0;
          } else if (estimates.accident > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_accident_h || 0;
          } else if (estimates.parental > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_parental_leave_h || 0;
          } else if (estimates.holiday > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_holiday_h || 0;
          } else if (estimates.other > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_other_h || 0;
          } else if (estimates.freetime > 0) {
            threshold = employeeSetting.overcapacity_day_with_target_hours_and_pto_freetime_h || 0;
          }

          // If no working hours defined for this day, use the "without target hours" threshold
          if (workingHours === 0) {
            threshold = employeeSetting.overcapacity_day_without_target_hours_h || 0;
          }

          const maxAllowedTime = workingHours + threshold;

          if (totalEstimatedTime > maxAllowedTime + 0.01) {
            // Check if notification already sent for this employee and date
            const messageHash = `DailyOvercapacity#${employeeName}#${dateStr}`;
            const { data: existingNotification } = await supabase
              .from('error_notifications')
              .select('*')
              .eq('message_hash', messageHash)
              .single();

            if (existingNotification) {
              // Check if 7 days have passed since last notification
              const lastSent = new Date(existingNotification.last_sent);
              const now = new Date();
              const daysSinceLastSent = Math.floor((now.getTime() - lastSent.getTime()) / (1000 * 60 * 60 * 24));

              if (daysSinceLastSent < 7) {
                await io.logger.info(
                  `Daily overcapacity notification already sent for ${employeeName} on ${dateStr} (${daysSinceLastSent} days ago, waiting for 7 days)`,
                );
                continue;
              }

              await io.logger.info(
                `Re-sending daily overcapacity notification for ${employeeName} on ${dateStr} (${daysSinceLastSent} days since last notification)`,
              );
            }

            // Format date for display (DD.MM.YY)
            const date = new Date(dateStr);
            const formattedDate = date.toLocaleDateString('de-DE', {
              weekday: 'short',
              day: '2-digit',
              month: '2-digit',
              year: '2-digit',
            });

            // Create ClickUp task for overcapacity notification
            const taskName = `hr: ${employeeName} - Überkapazität ${formattedDate}`;

            const taskDescription = `⚠️ Überkapazität erkannt am ${formattedDate}

Am ${formattedDate} wurde beim Mitarbeitenden **${employeeName}** eine Überkapazität festgestellt. Die Workload für diesen Tag überschreitet die maximale Belastung.

🔍 Details zur Überkapazität

Mitarbeitender: ${employeeName}
Datum: ${formattedDate}
Zeitschätzung: ${totalEstimatedTime.toFixed(2)} h
Sollzeit: ${workingHours.toFixed(2)} h

✅ Was jetzt zu tun ist

1. Prüfen, ob die Taskplanung an diesem Tag sinnvoll verteilt wurde.
2. Anpassung der Planung, um Überlast zu vermeiden.
3. Kontakt mit ${employeeName} aufnehmen, falls unklar ist, wie die Planung zustande kam.
4. Bei wiederholten Fällen Rücksprache mit Teamleitung oder Planung.

📌 Hinweis
Dieser Task wurde automatisch erstellt durch das Monitoring-Szenario. Sollte der Fall bereits geklärt sein, kann dieser Task geschlossen werden.`;

            await io.logger.info(
              `Creating daily overcapacity notification task for ${employeeName} on ${formattedDate} (${totalEstimatedTime.toFixed(2)}h > ${maxAllowedTime.toFixed(2)}h =? ${(workingHours + threshold).toFixed(2)}) | Workinghours: ${workingHours.toFixed(2)}h | Threshold: ${threshold.toFixed(2)}h`,
            );
            // TODO REMOVE LATER
            continue;

            try {
              const task = await clickupClient.createTask(String(globalSettings.pm_list_id), {
                name: taskName,
                description: taskDescription,
                due_date: new Date().getTime(),
                time_estimate: 5 * 60 * 1000, // 5 minutes estimate
                priority: 1,
                assignees: [process.env.CLICKUP_ADMIN_USER_ID],
              });

              if (isClickUpError(task)) {
                await io.logger.error(
                  `Failed to create overcapacity task for ${employeeName} on ${formattedDate}: ${JSON.stringify(task)}`,
                );
              } else {
                // Log the notification in error_notifications table (upsert to update last_sent if exists)
                await supabase.from('error_notifications').upsert(
                  {
                    message_hash: messageHash,
                    last_sent: new Date().toISOString(),
                  },
                  {
                    onConflict: 'message_hash',
                  },
                );

                await io.logger.info(
                  `Created daily overcapacity notification task for ${employeeName} on ${formattedDate} (${totalEstimatedTime.toFixed(1)}h > ${maxAllowedTime.toFixed(1)}h)`,
                );
              }
            } catch (error) {
              await io.logger.error(
                `Error creating overcapacity task for ${employeeName} on ${formattedDate}: ${error}`,
              );
            }
          }
        }
      }
    });

    await io.runTask('notify-weekly-overcapacity', async () => {
      const clickupClient = new ClickUpClient();
      const supabase = createClient();

      // Group estimates by week and employee
      const weeklyEstimates: { [key: string]: { [key: string]: number } } = {};

      for (const [dateStr, employees] of Object.entries(estimatedOpenTasks)) {
        const date = new Date(dateStr);

        // Calculate week number (ISO week)
        const startOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date.getTime() - startOfYear.getTime()) / 86400000;
        const weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
        const weekKey = `${date.getFullYear()}-W${weekNumber.toString().padStart(2, '0')}`;

        if (!weeklyEstimates[weekKey]) {
          weeklyEstimates[weekKey] = {};
        }

        for (const [employeeName, estimates] of Object.entries(employees)) {
          if (!weeklyEstimates[weekKey][employeeName]) {
            weeklyEstimates[weekKey][employeeName] = 0;
          }

          // Check if there are any PTO estimates
          const hasPtoEstimates =
            estimates.vacation > 0 ||
            estimates.sick > 0 ||
            estimates.compensation > 0 ||
            estimates.training > 0 ||
            estimates.military > 0 ||
            estimates.accident > 0 ||
            estimates.parental > 0 ||
            estimates.holiday > 0 ||
            estimates.other > 0 ||
            estimates.freetime > 0;

          // Sum all estimates for this employee on this day based on PTO presence
          let totalDayEstimate;
          if (hasPtoEstimates) {
            // Use regularNoRecurring and ignore regular when PTO exists
            totalDayEstimate = Object.values(estimates).reduce((sum, value) => sum + value, 0) - estimates.regular;
          } else {
            // Use regular and ignore regularNoRecurring when no PTO
            totalDayEstimate =
              Object.values(estimates).reduce((sum, value) => sum + value, 0) - estimates.regularNoRecurring;
          }
          weeklyEstimates[weekKey][employeeName] += totalDayEstimate;
        }
      }

      // Check weekly overcapacity for each week and employee
      for (const [weekKey, employees] of Object.entries(weeklyEstimates)) {
        for (const [employeeName, totalWeeklyEstimate] of Object.entries(employees)) {
          // Find employee setting
          const employeeSetting = overcapacitySettings.find((setting) => setting.employees?.name === employeeName);

          if (!employeeSetting) {
            // await io.logger.info(`No overcapacity setting found for employee: ${employeeName} (weekly check)`);
            continue;
          }

          // Check if weekly warnings are enabled for this employee
          if (!employeeSetting.warning_week_active) {
            await io.logger.info(`Weekly warning disabled for employee: ${employeeName} in week ${weekKey}`);
            continue;
          }

          // Check if weekly warning should be blocked due to PTO tasks > 2h in any day of the week
          const weekHasPtoOver2h = Object.entries(estimatedOpenTasks).some(([dateString, employeeEstimates]) => {
            const dayEstimates = employeeEstimates[employeeName];
            if (!dayEstimates) return false;

            const totalPtoHours = Object.entries(dayEstimates)
              .filter(([type]) => type.startsWith('pto_'))
              .reduce((sum, [, hours]) => sum + hours, 0);

            return totalPtoHours > 2;
          });

          if (weekHasPtoOver2h) {
            await io.logger.info(
              `Weekly warning blocked for ${employeeName} in week ${weekKey}: PTO tasks > 2h found in week`,
            );
            continue;
          }

          // Get weekly working hours for this employee
          const employeeWorkingHours = workingHoursByEmployeeByDate?.[employeeName];
          if (!employeeWorkingHours) {
            await io.logger.info(`No working hours found for employee: ${employeeName} (weekly check)`);
            continue;
          }

          // Calculate weekly target hours by summing actual working hours for this specific week
          const [year, week] = weekKey.split('-W');
          const weekNumber = parseInt(week);
          const yearNumber = parseInt(year);

          // Get the start date of this ISO week
          const jan4 = new Date(yearNumber, 0, 4);
          const weekStart = new Date(jan4.getTime() + (weekNumber - 1) * 7 * 24 * 60 * 60 * 1000);
          weekStart.setDate(weekStart.getDate() - jan4.getDay() + 1); // Monday of the week

          // Sum working hours for this specific week (Monday to Friday)
          let weeklyTargetHours = 0;
          let weekdates = [];
          let dayhours = [];
          for (let i = 0; i < 5; i++) {
            // Monday to Friday
            const currentDate = new Date(weekStart);
            currentDate.setDate(weekStart.getDate() + i);
            const dateString = convertUNIXTimestampToDateString(currentDate.getTime());
            weekdates.push(dateString);

            if (employeeWorkingHours[dateString]) {
              weeklyTargetHours += employeeWorkingHours[dateString];
              dayhours.push(employeeWorkingHours[dateString]);
            }
          }

          // Fallback to 43 hours (8.6 hours * 5 days) if no working hours data found
          if (weeklyTargetHours === 0) {
            weeklyTargetHours = 43;
          }

          // Check if weekly estimated time exceeds weekly threshold
          const weeklyThreshold = employeeSetting.overcapacity_week_h || 0;
          const maxAllowedWeeklyTime = weeklyTargetHours + weeklyThreshold;

          if (totalWeeklyEstimate > maxAllowedWeeklyTime) {
            // Check if notification already sent for this employee and week
            const messageHash = `WeeklyOvercapacity#${employeeName}#${weekKey}`;
            const { data: existingNotification } = await supabase
              .from('error_notifications')
              .select('*')
              .eq('message_hash', messageHash)
              .single();

            if (existingNotification) {
              // Check if 7 days have passed since last notification
              const lastSent = new Date(existingNotification.last_sent);
              const now = new Date();
              const daysSinceLastSent = Math.floor((now.getTime() - lastSent.getTime()) / (1000 * 60 * 60 * 24));

              if (daysSinceLastSent < 7) {
                await io.logger.info(
                  `Weekly overcapacity notification already sent for ${employeeName} in week ${weekKey} (${daysSinceLastSent} days ago, waiting for 7 days)`,
                );
                continue;
              }

              await io.logger.info(
                `Re-sending weekly overcapacity notification for ${employeeName} in week ${weekKey} (${daysSinceLastSent} days since last notification)`,
              );
            }

            // Format week for display (KW format)
            const [_, week] = weekKey.split('-W');
            const weekDisplay = `KW${parseInt(week)}`;

            const taskName = `hr: ${employeeName} – Überkapazität ${weekDisplay}`;
            const taskDescription = `⚠️ Überkapazität erkannt in der Woche ${weekDisplay}

In der ${weekDisplay} wurde beim Mitarbeitenden **${employeeName}** eine Überkapazität festgestellt. Die Workload für diese Woche überschreitet die maximale Belastung.

🔍 Details zur Überkapazität

Mitarbeitender: ${employeeName}
Woche: ${weekDisplay}
Zeitschätzung gesamte Woche: ${totalWeeklyEstimate.toFixed(2)} h
Sollzeit gesamte Woche: ${weeklyTargetHours.toFixed(2)} h

✅ Was jetzt zu tun ist

1. Prüfen, ob die Taskplanung in dieser Woche sinnvoll verteilt wurde.
2. Anpassung der Planung, um Überlast zu vermeiden.
3. Kontakt mit ${employeeName} aufnehmen, falls unklar ist, wie die Planung zustande kam.
4. Bei wiederholten Fällen Rücksprache mit Teamleitung oder Planung.

📌 Hinweis
Dieser Task wurde automatisch erstellt durch das Monitoring-Szenario. Sollte der Fall bereits geklärt sein, kann dieser Task geschlossen werden.`;

            // Debug logging to investigate the calculation discrepancy
            await io.logger.info(
              `Creating weekly overcapacity notification task for ${employeeName} in ${weekDisplay} (${totalWeeklyEstimate.toFixed(2)}h > ${maxAllowedWeeklyTime.toFixed(2)}h) | Weekly target: ${weeklyTargetHours}h | Threshold: ${weeklyThreshold}h`,
            );

            // TODO REMOVE LATER
            continue;

            try {
              const task = await clickupClient.createTask(globalSettings.pm_list_id!, {
                name: taskName,
                description: taskDescription,
                time_estimate: 5 * 60 * 1000, // 5 minutes in milliseconds
                priority: 1,
                assignees: [process.env.CLICKUP_ADMIN_USER_ID],
              });

              if (isClickUpError(task)) {
                await io.logger.error(
                  `Error creating weekly overcapacity task for ${employeeName} in ${weekDisplay}: ${JSON.stringify(task)}`,
                );
              } else {
                // Log the notification in error_notifications table (upsert to update last_sent if exists)
                await supabase.from('error_notifications').upsert(
                  {
                    message_hash: messageHash,
                    last_sent: new Date().toISOString(),
                  },
                  {
                    onConflict: 'message_hash',
                  },
                );

                await io.logger.info(
                  `Created weekly overcapacity notification task for ${employeeName} in ${weekDisplay} (${totalWeeklyEstimate.toFixed(1)}h > ${maxAllowedWeeklyTime.toFixed(1)}h)`,
                );
              }
            } catch (error) {
              await io.logger.error(
                `Error creating weekly overcapacity task for ${employeeName} in ${weekDisplay}: ${error}`,
              );
            }
          }
        }
      }
    });
  },
});
