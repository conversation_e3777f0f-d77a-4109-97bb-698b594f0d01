import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import {
  convertCronToUTC,
  convertUNIXTimestampToDateTimeString,
  prettyPrintTime,
  prettyPrintTsToDate,
} from '@/lib/utils';
import { isClickUpError } from '@/data/types/clickup.types';

export const checkTimeMaxDuration = client.defineJob({
  id: 'check-time-max-duration',
  name: 'Check Time Max Duration',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('30 10 * * *') }), // every day at 10:30
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const longTimeEntries = await io.runTask('fetch-long-time-entries', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select('*, employees(name), tasks(name, projects(clickup_name))');

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      await io.logger.info(`Fetched ${data?.length} time entries`);

      const tenHrs = 10 * 60 * 60 * 1000;
      const longTimeEntries = data?.filter((te) => te.clickup_duration && te.clickup_duration > tenHrs);

      return longTimeEntries.map((te) => {
        return {
          id: te.clickup_time_entry_id,
          clickup_task_id: te.clickup_task_id,
          task_name: te.tasks?.name,
          project_name: te.tasks?.projects?.clickup_name,
          clickup_start: te.clickup_start,
          clickup_end: te.clickup_end,
          clickup_duration: te.clickup_duration,
          username: te?.employees?.name,
        };
      });
    });

    await io.runTask('send-clickup-notifications', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();
      for (const timeEntry of longTimeEntries) {
        await io.runTask(`send-clickup-notification-${timeEntry.id}`, async () => {
          const { data, error } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `MaxDuration#${timeEntry.id}`);

          if (error) {
            throw new Error(`Error fetching error notifications: ${JSON.stringify(error)}`);
          }

          if (data?.length) {
            await io.logger.info(`Notification already sent.`);
            return;
          }

          const clickupResponse = await clickupClient.createTask(String(globalSettings.hr_list_id), {
            name: `hr: Hoher Zeiteintrag - ${timeEntry.username}`,
            description: `Ein Zeiteintrag mit über 10 Stunden getrackter Zeit wurde hinzugefügt. Checke den Zeiteintrag auf Richtigkeit:

Timeentry von: ${timeEntry.username}

Datum : ${prettyPrintTsToDate(timeEntry.clickup_start)}

Timentry ID: ${timeEntry.id}

Liste: ${timeEntry.project_name}

Task: ${timeEntry.task_name}

Task Link: https://app.clickup.com/t/${timeEntry.clickup_task_id}

Zeiteintrag: ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end))} | ${prettyPrintTime(Number(timeEntry.clickup_duration))}
`,
            assignees: [process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 5 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(clickupResponse)) {
            throw new Error(`Error while creating clickup task: ${JSON.stringify(clickupResponse)}`);
          }

          const { error: insertNotificationError } = await supabase.from('error_notifications').insert({
            message_hash: `MaxDuration#${timeEntry.id}`,
            last_sent: new Date().toISOString(),
          });

          if (insertNotificationError) {
            throw new Error(`Error while inserting notification: ${JSON.stringify(insertNotificationError)}`);
          }
        });
      }
    });
  },
});
