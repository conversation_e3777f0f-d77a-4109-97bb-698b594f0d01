import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { convertCronToUTC, convertUNIXTimestampToDateTimeString, prettyPrintTime } from '@/lib/utils';

export const checkTimeNightwork = client.defineJob({
  id: 'check-time-nightwork',
  name: 'Check Time Nightwork',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 10 * * *') }), // every day at 10:00
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const nightworkTimeEntries = await io.runTask('fetch-nightwork-time-entries', async () => {
      const yesterday = new Date(new Date().setDate(new Date().getDate() - 1));

      // supabase query select time entries where start is between start and end or end is between start and end
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select('*, employees(name, min_work_start, max_work_end), tasks(projects(clickup_list_id))')
        .gte('clickup_start', yesterday.getTime());

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      await io.logger.info(`Fetched ${data?.length} time entries`);

      // filter time entries where start is between start and end or end is between start and end
      const dataWoPTO = data?.filter(
        (te) => te.tasks?.projects?.clickup_list_id !== String(globalSettings.pto_list_id),
      );

      const lateWorkTimeEntries = dataWoPTO?.filter((te) => {
        const startHours = Number(te.employees?.min_work_start.split(':')[0]);
        const endHours = Number(te.employees?.max_work_end.split(':')[0]);
        const startMinutes = Number(te.employees?.min_work_start.split(':')[1]);
        const endMinutes = Number(te.employees?.max_work_end.split(':')[1]);
        const startInterval = new Date(yesterday).setHours(endHours, endMinutes, 0, 0);
        const endInterval = new Date().setHours(startHours, startMinutes, 0, 0);
        return (
          (Number(te.clickup_start) >= startInterval && Number(te.clickup_start) <= endInterval) ||
          (Number(te.clickup_end) >= startInterval && Number(te.clickup_end) <= endInterval)
        );
      });

      return lateWorkTimeEntries.map((te) => {
        return {
          id: te.id,
          clickup_task_id: te.clickup_task_id,
          clickup_start: te.clickup_start,
          clickup_end: te.clickup_end,
          clickup_duration: te.clickup_duration,
          user_id: te.clickup_user_id,
          name: te?.employees?.name,
        };
      });
    });

    const timeEntriesByUser =
      nightworkTimeEntries?.reduce(
        (acc, curr) => {
          const name = String(curr.name);
          if (!acc[name]) {
            acc[name] = [];
          }
          acc[name].push(curr);
          return acc;
        },
        {} as { [key: string]: any[] },
      ) || {};

    await io.runTask('send-clickup-notification', async () => {
      for (const [name, timeEntries] of Object.entries(timeEntriesByUser)) {
        const clickupClient = new ClickUpClient();
        const startDate = new Date(timeEntries[0].clickup_start).toLocaleString('de', {
          weekday: 'long',
          day: 'numeric',
          month: 'numeric',
          year: 'numeric',
        });
        const endDate = new Date(timeEntries[0].clickup_end).toLocaleString('de', {
          weekday: 'long',
          day: 'numeric',
          month: 'numeric',
          year: 'numeric',
        });
        const dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
        await clickupClient.createTask(String(globalSettings.hr_list_id), {
          name: `hr: Nachtarbeit - ${name}`,
          description: `${name} hat im "verbotenen" Zeitbereich zwischen 20 Uhr Abends und 7 Uhr Morgens gearbeitet. Prüfe ob das genehmigt war.

    User: ${name}
    Datum: ${dateRange}
    Timeentries: ${timeEntries.map((te) => `${convertUNIXTimestampToDateTimeString(te.clickup_start)} - ${convertUNIXTimestampToDateTimeString(te.clickup_end)} | ${prettyPrintTime(te.clickup_duration)}`).join('\n')}`,
          assignees: [process.env.CLICKUP_ADMIN_USER_ID],
          priority: 1,
          time_estimate: 10 * 60 * 1000,
          due_date: new Date().getTime(),
        });
      }
    });
  },
});
