import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC, convertUNIXTimestampToDateTimeString, prettyPrintTime } from '@/lib/utils';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { isClickUpError } from '@/data/types/clickup.types';
import { Tables } from '@/types/gen/database-table';

export const createTasksForFaultyTimeEntriesJob = client.defineJob({
  id: 'create-tasks-for-faulty-time-entries',
  name: 'Create tasks for faulty time entries',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('35 12 * * *') }), // every day at 12:30
  run: async (payload, io, _) => {
    // 1 FETCH GLOBAL SETTINGS
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const { data, error } = await getGlobalSettings();
      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const fstAprilDate = new Date('2024-04-01T00:00:00.000Z').getTime();
    const todayMinus24h = new Date(new Date().getTime() - 12 * 60 * 60 * 1000).getTime();
    const fiveDaysAgo = new Date(new Date().setDate(new Date().getDate() - 5));

    // 2 FETCH ALL UNASSIGNED TIME ENTRIES
    const unassignedTimeEntries = await io.runTask('fetch-all-unassigned-time-entries', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select('id, clickup_start, clickup_end, clickup_duration, clickup_user_id')
        .gte('clickup_start', fstAprilDate)
        .lte('clickup_end', todayMinus24h)
        .is('clickup_task_id', null)
        .or('error_reminder_sent_date.is.null,error_reminder_sent_date.lt.' + fiveDaysAgo.toISOString());

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    // 3 CREATE TASKS TIME ENTRIES WITH NO ASSOCIATED TASK
    const createdTaskTEIds = await io.runTask('create-tasks-for-unnamed-time-entries', async () => {
      if (!globalSettings) {
        return;
      }

      const listId = String(globalSettings.false_timeentries_list_id);
      const createdTaskTEIds: number[] = [];
      for (const timeEntry of unassignedTimeEntries) {
        await io.runTask(`create-task-for-unnamed-time-entry-${timeEntry.id}`, async () => {
          // 3.1 CREATE TASK ON CLICKUP
          await io.runTask(`clickup-create-task-for-time-entry-${timeEntry.id}`, async () => {
            const clickupClient = new ClickUpClient();
            const task = await clickupClient.createTask(listId, {
              name: `adm: Timeentry - nicht zugewiesen: ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end))} | ${prettyPrintTime(timeEntry.clickup_duration)}`,
              description: `Unvollständiger Zeiteintrag:
${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start))} - ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end))} | ${prettyPrintTime(timeEntry.clickup_duration)}
Gehe zu deinem Dashboard für die Zeiterfassung und suche am ${convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start))} den Eintrag «nicht zugewiesen» und weise diesen dem korrekten Task zu.`,
              assignees: [Number(timeEntry.clickup_user_id)],
              time_estimate: 5 * 60 * 1000,
              due_date: new Date().getTime(),
            });

            if (isClickUpError(task)) {
              throw new Error(`Error creating task: ${JSON.stringify(task)}`);
            }
          });
          createdTaskTEIds.push(timeEntry.id);
        });
      }

      return createdTaskTEIds;
    });

    // 4 CREATE TASKS FOR TIME ENTRIES WITHOUT TAGS
    const timeEntriesWithoutTags = await io.runTask('fetch-time-entries-without-tags', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select('id,clickup_user_id, tasks(name, clickup_task_id, clickup_url)')
        .gte('clickup_start', fstAprilDate)
        .lte('clickup_end', todayMinus24h)
        .eq('clickup_task_tag', 'null')
        .eq('billable', true)
        .or('error_reminder_sent_date.is.null,error_reminder_sent_date.lt.' + fiveDaysAgo.toISOString())
        .neq('clickup_task_id', null);

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const createdTaskTEIdsWithoutTags = await io.runTask('create-tasks-for-time-entries-without-tags', async () => {
      if (!globalSettings) {
        return;
      }

      const listId = String(globalSettings.false_timeentries_list_id);
      const createdTaskTEIds: number[] = [];
      for (const timeEntry of timeEntriesWithoutTags) {
        await io.runTask(`create-task-for-time-entry-without-tags-${timeEntry.id}`, async () => {
          // 4.1 CREATE TASK ON CLICKUP
          await io.runTask(`clickup-create-task-for-time-entry-without-tags-${timeEntry.id}`, async () => {
            const clickupClient = new ClickUpClient();
            const task = await clickupClient.createTask(listId, {
              name: `adm: Timentry - ${timeEntry.tasks?.name}`,
              description: `Kein Label gesetzt (bspw. edit, avor, etc.) bei «${timeEntry.tasks?.name}»
Hier geht's direkt zum fehlerhaften Task: https://app.clickup.com/t/${timeEntry.tasks?.clickup_task_id}
Zeiteinträge auf Kundenprojekt brauchen immer ein Label!`,
              assignees: [Number(timeEntry.clickup_user_id)],
              time_estimate: 5 * 60 * 1000,
              due_date: new Date().getTime(),
            });

            if (isClickUpError(task)) {
              throw new Error(`Error creating task: ${JSON.stringify(task)}`);
            }
          });

          createdTaskTEIds.push(timeEntry.id);
        });
      }

      return createdTaskTEIds;
    });

    // 5 UPDATE IN DB
    await io.runTask('update-time-entries-task-creation-in-db', async () => {
      let ids: number[] = [];
      if (createdTaskTEIds) {
        ids = [...createdTaskTEIds];
      }
      if (createdTaskTEIdsWithoutTags) {
        ids = [...ids, ...createdTaskTEIdsWithoutTags];
      }
      const teIds = Array.from(new Set(ids));
      const supabase = createClient();
      const { error } = await supabase
        .from('time_entries')
        .update({ error_reminder_sent_date: new Date().toISOString() })
        .in('id', teIds);

      if (error) {
        throw new Error(`Error updating time entries: ${JSON.stringify(error)}`);
      }
    });

    // fetch error notifications
    const errorNotifications = await io.runTask('fetch-error-notifications', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('error_notifications').select('*');

      if (error) {
        throw new Error(`Error fetching error notifications: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const employeeNames = await io.runTask('fetch-employee-names', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*');
      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      // Map from userId and name
      return data?.reduce(
        (acc, employee) => {
          acc[String(employee.clickup_user_id)] = employee.name;
          return acc;
        },
        {} as { [key: string]: string },
      );
    });

    // PRINT OUT USERS WITH TOTAL TIME ENTRY PER DAY > MAX hours
    await io.runTask('print-out-users-with-total-time-entry-per-day-more-than-max-hours', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select(
          'clickup_user_id, clickup_start, clickup_end, clickup_duration, employees(clickup_user_id, name, max_working_hours)',
        )
        .gte('clickup_start', fstAprilDate)
        .lte('clickup_end', todayMinus24h);

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      const userTimeEntries = data || [];

      const userMaxWorkingTime = userTimeEntries.reduce(
        (acc, curr) => {
          const userId = curr.employees?.clickup_user_id || 'unknown';
          if (!acc[userId]) {
            acc[userId] = curr.employees?.max_working_hours || 10;
          }
          return acc;
        },
        {} as Record<string, number>,
      );

      const userTimeEntriesPerDay: Record<string, Record<string, number>> = {};
      for (const te of userTimeEntries) {
        const date = new Date(Number(te.clickup_start)).toISOString().slice(0, 10);
        const userId = te.employees?.clickup_user_id || 'unknown';
        if (!userTimeEntriesPerDay[date]) {
          userTimeEntriesPerDay[date] = { [userId]: 0 };
        }

        if (!userTimeEntriesPerDay[date][userId]) {
          userTimeEntriesPerDay[date][userId] = 0;
        }
        userTimeEntriesPerDay[date][userId] += Number(te.clickup_duration);
      }

      const usersWithMoreThanMaxHours: Record<string, any[]> = {};
      for (const [date, record] of Object.entries(userTimeEntriesPerDay)) {
        for (const [userId, duration] of Object.entries(record)) {
          const maxHours = userMaxWorkingTime[userId] || 10;
          if (duration > maxHours * 60 * 60 * 1000) {
            usersWithMoreThanMaxHours[userId] = [date, duration];
          }
        }
      }

      const clickupClient = new ClickUpClient();
      // create task for each user with more than max hours
      for (const [userId, [date, duration]] of Object.entries(usersWithMoreThanMaxHours)) {
        await io.runTask(`create-task-for-user-with-more-than-max-hours-${userId}-${date}`, async () => {
          const errorAlreadySent = errorNotifications.some((en) => en.message_hash === `${userId}#${date}#MaxHours`);
          if (errorAlreadySent) {
            await io.logger.info(`Error notification already sent.`);
            return;
          }

          const userName = employeeNames[userId];
          await clickupClient.createTask(String(globalSettings?.hr_list_id), {
            name: `hr: Überstundenwarnung - ${userName}`,
            description: `User: ${userName}
Total Timeentries: ${(duration / (60 * 60 * 1000)).toFixed(2)} h
Date: ${new Date(date).toISOString().slice(0, 10)}

Bitte kontrollieren und überprüfen.
`,
            assignees: [process.env.CLICKUP_ADMIN_USER_ID],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          await supabase.from('error_notifications').insert({ message_hash: `${userId}#${date}#MaxHours` });
        });
      }
    });

    // CHECK FOR OVERLAPPING TIME ENTRIES
    await io.runTask('check-for-overlapping-time-entries', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('time_entries')
        .select(
          'id, clickup_start, clickup_end, clickup_user_id, clickup_duration,clickup_task_id, employees(clickup_user_id, name), tasks(clickup_list_id)',
        )
        .gte('clickup_start', fstAprilDate)
        .lte('clickup_end', todayMinus24h);

      if (error) {
        throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
      }

      const timeEntries =
        data?.filter((entry) => entry.tasks?.clickup_list_id !== String(globalSettings.pto_list_id)) || [];
      const overlappingTimeEntries = [];
      for (const te of timeEntries) {
        const overlapping = timeEntries.filter(
          (t) =>
            t.clickup_user_id === te.clickup_user_id &&
            t.id !== te.id &&
            Number(t.clickup_start) <= Number(te.clickup_start) &&
            Number(t.clickup_end) >= Number(te.clickup_start) &&
            Number(t.clickup_end) - Number(te.clickup_start) >= 60 * 1000,
        );

        if (overlapping.length > 0) {
          overlappingTimeEntries.push(overlapping.concat([te]));
        }
      }

      // list of overlapping time entries for each user and date
      const overlappingTimeEntriesPerUser: Record<string, Record<string, any[]>> = {};
      for (const te of overlappingTimeEntries) {
        const date = new Date(Number(te[0].clickup_start)).toISOString().slice(0, 10);
        const userId = te[0].employees?.clickup_user_id || 'unknown';
        if (!overlappingTimeEntriesPerUser[userId]) {
          overlappingTimeEntriesPerUser[userId] = { [date]: [] };
        }

        if (!overlappingTimeEntriesPerUser[userId][date]) {
          overlappingTimeEntriesPerUser[userId][date] = [];
        }
        overlappingTimeEntriesPerUser[userId][date].push(te);
      }

      const clickupClient = new ClickUpClient();
      // create task for each user with overlapping time entries
      for (const [userId, record] of Object.entries(overlappingTimeEntriesPerUser)) {
        for (const [date, tes] of Object.entries(record)) {
          await io.runTask(`create-task-for-user-with-overlapping-time-entries-${userId}-${date}`, async () => {
            const errorAlreadySent = errorNotifications.some(
              (en) => en.message_hash === `${userId}#${date}#overlapping` && en.last_sent >= fiveDaysAgo.toISOString(),
            );
            if (errorAlreadySent) {
              await io.logger.info(`Error notification already sent.`);
              return;
            }

            const timeEntries = tes.map((otes: Tables<'time_entries'>[]) =>
              otes
                .map((te) => {
                  return `${convertUNIXTimestampToDateTimeString(Number(te.clickup_start))} - ${convertUNIXTimestampToDateTimeString(Number(te.clickup_end))} | ${prettyPrintTime(Number(te.clickup_duration))} | ${te.clickup_task_id ? `https://app.clickup.com/t/${te.clickup_task_id}` : 'no task'}`;
                })
                .sort()
                .join('\n'),
            );
            const userName = employeeNames[userId];
            await clickupClient.createTask(String(globalSettings?.false_timeentries_list_id), {
              name: `hr: Time-Entries overlapping - ${userName}`,
              description: `Es gibt bei folgendem User: ${userName} überlagernde Time-Entries am ${new Date(date).toISOString().slice(0, 10)}:
${timeEntries.join('\n\n')}`,
              assignees: [process.env.CLICKUP_ADMIN_USER_ID],
              priority: 1,
              time_estimate: 10 * 60 * 1000,
              due_date: new Date().getTime(),
            });

            await supabase
              .from('error_notifications')
              .upsert(
                { message_hash: `${userId}#${date}#overlapping`, last_sent: new Date().toISOString() },
                { onConflict: 'message_hash' },
              );
          });
        }
      }
    });
  },
});
