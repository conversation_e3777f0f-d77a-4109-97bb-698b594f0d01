import { cronTrigger, isTriggerError } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertCronToUTC, convertUNIXTimestampToDateTimeString, shallowEqualIgnoreProperties } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { getClickupTimeEntriesJob } from '@/jobs/shared/clickup/get-clickup-time-entries-job';
import { notifyWebmasterJob, taskCreatedJob } from '@/jobs';
import { newTeAfter5Days } from '@/jobs/sync/cron/task-notifications/new-te-after-5-days';
import { modifiedTeAfter5Days } from '@/jobs/sync/cron/task-notifications/modified-te-after-5-days';
import { tempUnarchive } from '@/jobs/shared/bexio-projects/temp-unarchive';
import { updateBillableAmount } from '@/jobs/sync/cron/time/update-billable-amount';

export const syncClickupTimeEntriesToBexioTimesheetJob = client.defineJob({
  id: 'sync-clickup-time-entries-to-bexio-timesheet',
  name: 'Sync clickup time entries to bexio timesheet',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('*/5 6-23 * * *') }), // every 15 minutes from 6am to 11pm
  run: async (payload, io, _) => {
    try {
      // 0 PREPARE CACHED CONSTANTS
      const [startUnixMs, endUnixMs] = await io.runTask('prepare-cached-constants', async () => {
        const today = new Date();
        let startUnixMs = new Date().setDate(today.getDate() - 150);
        let endUnixMs = new Date().getTime();

        // sync past 300 to 150 days instead
        if ([20, 35, 50, 5].includes(new Date().getMinutes())) {
          startUnixMs = new Date().setDate(today.getDate() - 300);
          endUnixMs = new Date().setDate(today.getDate() - 150);
        }

        if ([10, 25].includes(new Date().getMinutes())) {
          startUnixMs = new Date().setDate(today.getDate() - 450);
          endUnixMs = new Date().setDate(today.getDate() - 300);
        }

        if ([40, 55].includes(new Date().getMinutes())) {
          startUnixMs = new Date().setDate(today.getDate() - 600);
          endUnixMs = new Date().setDate(today.getDate() - 450);
        }

        return [startUnixMs, endUnixMs];
      });

      const globalSettings = await io.runTask('get-global-settings', async () => {
        const supabase = createClient();
        const { data, error } = await supabase.from('global_settings').select('*').single();

        if (error) {
          throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
        }

        return data;
      });

      const savedTaskIds = await io.runTask('get-saved-task-ids', async () => {
        const supabase = createClient();
        const { data, error } = await supabase.from('tasks').select('clickup_task_id');

        if (error) {
          throw new Error(`Error fetching saved task ids: ${JSON.stringify(error)}`);
        }

        return data?.filter((id) => !!id.clickup_task_id).map((task) => String(task.clickup_task_id));
      });

      // 1 FETCH CLICKUP TIME ENTRIES (FROM 30 DAYS AGO until 24 HOURS AGO)
      const [
        timeEntriesToInsert,
        timeEntriesToUpdate,
        timeEntriesToDelete,
        timeEntriesToDeleteOnBexio,
        timeEntriesLocationObj,
      ] = await io.runTask('fetch-clickup-time-entries', async () => {
        // 1.1 FETCH CLICKUP TIME ENTRIES IN RANGE
        const job = await getClickupTimeEntriesJob.invokeAndWaitForCompletion('fetch-time-entries', {
          startMs: startUnixMs,
          endMs: endUnixMs,
        });
        if (!job.ok) {
          throw new Error(`Error fetching clickup time entries: ${JSON.stringify(job)}`);
        }

        const { billableTimeEntries, nonBillableTimeEntries } = job.output;

        // 1.2 FETCH TIME ENTRIES FROM DB BEFORE MUTATION (in range)
        const { data, error } = await createClient()
          .from('time_entries')
          .select('*')
          .gte('clickup_start', startUnixMs)
          .lte('clickup_end', endUnixMs);

        if (error) {
          throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
        }
        if (!data) {
          throw new Error(`No data returned from fetching time entries from db: ${JSON.stringify(data)}`);
        }
        const timeEntriesBeforeMutation = data || [];

        // 1.3 DETERMINE WHICH TIME ENTRIES TO INSERT, UPDATE OR DELETE
        return await io.runTask('determine-time-entries-to-insert-update-or-delete', async () => {
          const freshTimeEntries = billableTimeEntries.map((timeEntry) => {
            return {
              clickup_time_entry_id: timeEntry.id,
              clickup_description: timeEntry.description,
              clickup_task_id: timeEntry.task_id,
              clickup_user_id: String(timeEntry.user_id),
              clickup_duration: timeEntry.duration ? Number(timeEntry.duration) : 0,
              clickup_start: Number(timeEntry.start),
              clickup_end: Number(timeEntry.end),
              clickup_task_tag: String(timeEntry.first_tag_name),
              billable: true,
            };
          });

          const allFreshTimeEntries = nonBillableTimeEntries
            .map((timeEntry) => {
              return {
                clickup_time_entry_id: timeEntry.id,
                clickup_description: timeEntry.description,
                clickup_task_id: timeEntry.task_id,
                clickup_user_id: String(timeEntry.user_id),
                clickup_duration: timeEntry.duration ? Number(timeEntry.duration) : 0,
                clickup_start: Number(timeEntry.start),
                clickup_end: Number(timeEntry.end),
                clickup_task_tag: String(timeEntry.first_tag_name),
                billable: false,
              };
            })
            .concat(freshTimeEntries);

          const timeEntriesLocationMap = new Map<string, { task_name: string; list_name: string }>();
          for (const timeEntry of billableTimeEntries.concat(nonBillableTimeEntries)) {
            timeEntriesLocationMap.set(String(timeEntry.id), {
              task_name: timeEntry.task_name,
              list_name: timeEntry.list_name,
            });
          }

          await io.runTask('create-tasks-with-new-task-ids', async () => {
            // check if any of the fresh time entries has a task id that is not in the saved task ids
            const freshTimeEntriesWithNewTaskIds = allFreshTimeEntries
              .filter((timeEntry) => {
                return !savedTaskIds.includes(String(timeEntry.clickup_task_id));
              })
              .map((timeEntry) => timeEntry.clickup_task_id);
            const uniqueNewTaskIds = freshTimeEntriesWithNewTaskIds.filter(
              (id, index, self) => self.indexOf(id) === index,
            );

            if (uniqueNewTaskIds.length == 0) {
              await io.logger.info(`No time entries with new task ids found`);
              return;
            }

            for (const taskId of uniqueNewTaskIds) {
              await taskCreatedJob.invokeAndWaitForCompletion(`task-created-${taskId}`, {
                createdTaskId: taskId,
              });
            }
          });

          // Fresh entries that are not in the db
          const timeEntriesToInsert = allFreshTimeEntries.filter((timeEntryBeforeMutation) => {
            return !timeEntriesBeforeMutation.some(
              (timeEntry) => timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id,
            );
          });

          // Fresh entries that in the db and are different than the db
          const timeEntriesToUpdate = allFreshTimeEntries.filter((timeEntryBeforeMutation) => {
            return timeEntriesBeforeMutation.some(
              (timeEntry) =>
                timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id &&
                !shallowEqualIgnoreProperties(timeEntryBeforeMutation, timeEntry, [
                  'id',
                  'bexio_timesheet_id',
                  'error_reminder_sent_date',
                  'billable_amount',
                ]),
            );
          });

          // Entries in the db that are not in fresh entries
          const timeEntriesToDelete = timeEntriesBeforeMutation.filter((timeEntryBeforeMutation) => {
            return !allFreshTimeEntries.some(
              (timeEntry) => timeEntry.clickup_time_entry_id == timeEntryBeforeMutation.clickup_time_entry_id,
            );
          });

          // DELETE TIME ENTRIES ON BEXIO WHEN BILLABLE IS FALSE AND BEXIO ID IS THERE
          const timeEntriesToDeleteOnBexio = timeEntriesBeforeMutation.filter((timeEntry) => {
            return timeEntry.billable == false && timeEntry.bexio_timesheet_id;
          });
          return [
            timeEntriesToInsert,
            timeEntriesToUpdate,
            timeEntriesToDelete,
            timeEntriesToDeleteOnBexio,
            Object.fromEntries(timeEntriesLocationMap.entries()),
          ];
        });
      });

      const timeEntriesLocationMap = new Map(Object.entries(timeEntriesLocationObj));

      // 2.1 INSERT NEW CLICKUP TIME ENTRIES INTO DB AND PUSH TO BEXIO
      await io.runTask('insert-new-clickup-time-entries-into-db-and-bexio', async () => {
        const missingTE = await io.runTask('fetch-additional-missing-time-entries-from-db', async () => {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('time_entries')
            .select(
              '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(name, bexio_project_id)), employees(name, bexio_user_id)',
            )
            .is('bexio_timesheet_id', null)
            .eq('billable', true);

          if (error) {
            throw new Error(`Error fetching time entries from db: ${JSON.stringify(error)}`);
          }

          return data || [];
        });

        if (timeEntriesToInsert.length == 0 && missingTE.length == 0) {
          await io.logger.info(`No time entries to insert`);
          return;
        }

        await io.logger.info(`Inserting ${timeEntriesToInsert.length} new time entries`);
        await io.logger.info(`Inserting ${missingTE.length} missing time entries`);

        // 2.1.1 INSERT NEW CLICKUP TIME ENTRIES INTO DB
        let timeEntriesToBeCreated = await io.runTask('insert-new-clickup-time-entries-into-db', async () => {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('time_entries')
            .upsert(timeEntriesToInsert, { onConflict: 'clickup_time_entry_id' })
            .select(
              '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(name, bexio_project_id)), employees(name, bexio_user_id)',
            );

          if (error) {
            throw new Error(`Error saving time entries to db: ${JSON.stringify(error)}`);
          }
          await updateBillableAmount(data);

          return data.filter((timeEntry) => timeEntry.billable && timeEntry.tasks?.bexio_work_package_id);
        });

        await io.runTask('notify-new-te-after-5-days', async () => {
          const teForNotification = timeEntriesToBeCreated.filter(
            (te) => Number(te.clickup_start) <= Date.now() - 5 * 24 * 60 * 60 * 1000,
          );

          await io.logger.info(`Notifying ${teForNotification.length} time entries after more than 5 days`);

          for (const te of teForNotification) {
            await io.logger.info(`Notifying new time entry ${te.id} after more than 5 days`);
            await newTeAfter5Days(te, String(globalSettings.false_timeentries_list_id), io);
          }
        });

        timeEntriesToBeCreated = timeEntriesToBeCreated.concat(missingTE);

        // 2.1.2 PUSH NEW CLICKUP TIME ENTRIES TO BEXIO
        const bexioClient = new BexioClient();
        for (const timeEntry of timeEntriesToBeCreated) {
          await io.runTask(`push-new-clickup-time-entries-to-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
            if (!timeEntry.business_activities?.bexio_business_activity_id) {
              await io.logger.info(
                `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio business activity`,
              );
              return;
            }

            if (!timeEntry.tasks?.projects?.bexio_project_id) {
              await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio project`);
              return;
            }

            const timeSheet = {
              user_id: timeEntry.employees?.bexio_user_id || 1,
              client_service_id: timeEntry.business_activities?.bexio_business_activity_id,
              text: timeEntry.tasks?.name,
              allowable_bill: true,
              pr_project_id: timeEntry.tasks?.projects?.bexio_project_id,
              pr_package_id: timeEntry.tasks?.bexio_work_package_id,
              tracking: {
                type: 'range',
                start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
                end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
              },
            };

            let createdTimeSheet = await bexioClient.createTimesheet(timeSheet);
            if (isBexioError(createdTimeSheet)) {
              if (createdTimeSheet.error_code == 422 || createdTimeSheet.error_code == 404) {
                createdTimeSheet = await io.runTask(
                  `unarchive-bexio-project-for-update-te-${timeEntry.clickup_time_entry_id}`,
                  () =>
                    tempUnarchive(Number(timeEntry.tasks?.projects?.bexio_project_id), () =>
                      bexioClient.createTimesheet(timeSheet),
                    ),
                );

                if (isBexioError(createdTimeSheet)) {
                  throw new Error(`Error pushing timesheet to bexio: ${JSON.stringify(createdTimeSheet)}`);
                }
              } else {
                throw new Error(`Error pushing timesheet to bexio: ${JSON.stringify(createdTimeSheet)}`);
              }
            }

            const supabase = createClient();
            const updatedTimeEntry = await supabase
              .from('time_entries')
              .update({ bexio_timesheet_id: createdTimeSheet.id })
              .eq('clickup_time_entry_id', String(timeEntry.clickup_time_entry_id));

            if (updatedTimeEntry.error) {
              throw new Error(`Error updating time entry in db: ${JSON.stringify(updatedTimeEntry.error)}`);
            }
          });
        }
      });

      // 2.2 UPDATE CLICKUP TIME ENTRIES IN DB
      await io.runTask('update-clickup-time-entries-in-db-and-bexio', async () => {
        if (timeEntriesToUpdate.length == 0) {
          await io.logger.info(`No time entries to update`);
          return;
        }

        await io.logger.info(`Updating ${timeEntriesToUpdate.length} time entries`);

        await io.runTask('notify-modified-te-after-5-days', async () => {
          const teForNotification = timeEntriesToUpdate.filter(
            (te) => Number(te.clickup_start) <= Date.now() - 5 * 24 * 60 * 60 * 1000,
          );

          await io.logger.info(`Notifying ${teForNotification.length} time entries after more than 5 days`);
          for (const te of teForNotification) {
            await io.logger.info(`Notifying new time entry ${te.clickup_time_entry_id} after more than 5 days`);
            const supabase = createClient();
            const { data: prevTE } = await supabase
              .from('time_entries')
              .select('*, tasks(name, projects(name)), employees(name)')
              .eq('clickup_time_entry_id', String(te.clickup_time_entry_id))
              .single();
            await modifiedTeAfter5Days(
              prevTE,
              te,
              timeEntriesLocationMap,
              String(globalSettings.false_timeentries_list_id),
              io,
            );
          }
        });

        // 2.2.1 UPDATE CLICKUP TIME ENTRIES IN DB
        const savedTimeEntriesToUpdate = await io.runTask('update-clickup-time-entries-in-db', async () => {
          const supabase = createClient();
          const { data: timeToPush, error } = await supabase
            .from('time_entries')
            .upsert(timeEntriesToUpdate as any, { onConflict: 'clickup_time_entry_id' })
            .select(
              '*, business_activities(bexio_business_activity_id), tasks(name, bexio_work_package_id, projects(bexio_project_id)), employees(bexio_user_id)',
            );
          if (error) {
            throw new Error(`Error updating time entry in db: ${JSON.stringify(error)}`);
          }

          await updateBillableAmount(timeToPush);

          return (
            timeToPush?.filter(
              (timeEntry) =>
                timeEntry.billable && timeEntry.clickup_time_entry_id && timeEntry.tasks?.bexio_work_package_id,
            ) || []
          );
        });

        // 2.2.2 PUSH UPDATED CLICKUP TIME ENTRIES TO BEXIO
        const bexioClient = new BexioClient();
        for (const timeEntry of savedTimeEntriesToUpdate) {
          await io.runTask(
            `push-updated-clickup-time-entries-to-bexio-${timeEntry.clickup_time_entry_id}`,
            async () => {
              if (!timeEntry.business_activities?.bexio_business_activity_id) {
                await io.logger.info(
                  `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio business activity`,
                );
                return;
              }

              if (!timeEntry.tasks?.projects?.bexio_project_id) {
                await io.logger.info(`Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio project`);
                return;
              }

              if (!timeEntry.bexio_timesheet_id) {
                await io.logger.info(
                  `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`,
                );
                return;
              }

              const timeSheet = {
                user_id: timeEntry.employees?.bexio_user_id || 1,
                client_service_id: timeEntry.business_activities?.bexio_business_activity_id,
                text: timeEntry.tasks?.name,
                allowable_bill: true,
                pr_project_id: timeEntry.tasks?.projects?.bexio_project_id,
                pr_package_id: timeEntry.tasks?.bexio_work_package_id,
                tracking: {
                  type: 'range',
                  start: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_start)),
                  end: convertUNIXTimestampToDateTimeString(Number(timeEntry.clickup_end)),
                },
              };

              let updatedTimeSheet = await bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet);
              if (isBexioError(updatedTimeSheet)) {
                if (updatedTimeSheet.error_code == 422 || updatedTimeSheet.error_code == 404) {
                  updatedTimeSheet = await io.runTask(
                    `unarchive-bexio-project-for-update-te-${timeEntry.clickup_time_entry_id}`,
                    () =>
                      tempUnarchive(Number(timeEntry.tasks?.projects?.bexio_project_id), () =>
                        bexioClient.updateTimesheet(Number(timeEntry.bexio_timesheet_id), timeSheet),
                      ),
                  );

                  if (isBexioError(updatedTimeSheet)) {
                    throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
                  }
                } else {
                  throw new Error(`Error updating timesheet to bexio: ${JSON.stringify(updatedTimeSheet)}`);
                }
              }
            },
          );
        }
      });

      // DELETE BEXIO TIMESHEET WHEN MOVED FROM BILLABLE TO NOT BILLABLE
      await io.runTask('delete-bexio-timesheet-when-moved-from-billable-to-not-billable', async () => {
        if (timeEntriesToDeleteOnBexio.length == 0) {
          await io.logger.info(`No time entries to delete on bexio`);
          return;
        }

        await io.logger.info(`Deleting ${timeEntriesToDeleteOnBexio.length} time entries on bexio`);

        const bexioClient = new BexioClient();
        for (const timeEntry of timeEntriesToDeleteOnBexio) {
          await io.runTask(`delete-bexio-timesheet-${timeEntry.clickup_time_entry_id}`, async () => {
            if (!timeEntry.bexio_timesheet_id) {
              await io.logger.info(
                `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`,
              );
              return;
            }

            const deletedTimeSheet = await bexioClient.deleteTimesheet(Number(timeEntry.bexio_timesheet_id));
            if (isBexioError(deletedTimeSheet) || !deletedTimeSheet.success) {
              throw new Error(`Error deleting timesheet from bexio: ${JSON.stringify(deletedTimeSheet)}`);
            }

            const supabase = createClient();
            await supabase
              .from('time_entries')
              .update({ bexio_timesheet_id: null })
              .eq('clickup_time_entry_id', String(timeEntry.clickup_time_entry_id));
          });
        }
      });

      // 2.3 DELETE CLICKUP TIME ENTRIES FROM DB AND BEXIO
      await io.runTask('delete-clickup-time-entries-from-db-and-bexio', async () => {
        if (timeEntriesToDelete.length == 0) {
          await io.logger.info(`No time entries to delete`);
          return;
        }

        await io.logger.info(`Deleting ${timeEntriesToDelete.length} time entries`);

        // 2.3.1 DELETE CLICKUP TIME ENTRIES FROM DB
        const supabase = createClient();
        const { error } = await supabase
          .from('time_entries')
          .delete()
          .in(
            'clickup_time_entry_id',
            timeEntriesToDelete.map((timeEntry) => timeEntry.clickup_time_entry_id),
          );
        if (error) {
          throw new Error(`Error deleting time entry from db: ${JSON.stringify(error)}`);
        }

        // 2.3.2 DELETE CLICKUP TIME ENTRIES FROM BEXIO
        const bexioClient = new BexioClient();
        for (const timeEntry of timeEntriesToDelete) {
          await io.runTask(`delete-clickup-time-entries-from-bexio-${timeEntry.clickup_time_entry_id}`, async () => {
            if (!timeEntry.bexio_timesheet_id) {
              await io.logger.info(
                `Skipping time entry ${timeEntry.clickup_time_entry_id} - has no bexio timesheet id`,
              );
              return;
            }

            const deletedTimeSheet = await bexioClient.deleteTimesheet(Number(timeEntry.bexio_timesheet_id));
            if (isBexioError(deletedTimeSheet) || !deletedTimeSheet.success) {
              throw new Error(`Error deleting timesheet from bexio: ${JSON.stringify(deletedTimeSheet)}`);
            }
          });
        }
      });
    } catch (error) {
      if (isTriggerError(error)) {
        throw error;
      }

      await notifyWebmasterJob.invoke({
        subject: '[Internal Tools] Fehler in sync job',
        // @ts-ignore
        message: String(error?.message),
      });
      throw error;
    }
  },
});
