import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC } from '@/lib/utils';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { taskCreatedJob } from '@/jobs/sync/tasks/task-created-job';

export const syncRecurringTasksJob = client.defineJob({
  id: 'sync-recurring-tasks',
  name: 'Sync Recurring Tasks',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 6-23 * * *') }), // every hour from 6am to 11pm
  run: async (payload, io, _) => {
    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const settings = await getGlobalSettings();
      if (!settings.data?.pto_list_id) {
        throw new Error('PTO list ID not found in global settings');
      }
      return settings.data;
    });

    // Step 1: Fetch recurring task IDs from ClickUp and sync to database
    await io.runTask('sync-recurring-task-ids', async () => {
      const supabase = createClient();
      if (!globalSettings.recurring_tasks_view_id) {
        await io.logger.info('No recurring tasks view ID found in global settings, skipping recurring tasks sync');
        return [];
      }

      const clickupClient = new ClickUpClient();
      let allTaskIds: string[] = [];
      let page = 0;
      let lastPage = false;

      // Paginate through all pages to get all task IDs
      while (!lastPage) {
        const taskIds = await io.runTask(`fetch-recurring-tasks-page-${page}`, async () => {
          const recurringTasksResponse = await clickupClient.getTasksFromView(
            globalSettings.recurring_tasks_view_id!,
            page,
          );

          if (isClickUpError(recurringTasksResponse)) {
            throw new Error(`Error fetching recurring tasks from view: ${JSON.stringify(recurringTasksResponse)}`);
          }

          // Filter out tasks from ignored list IDs
          const ignoredListIds = globalSettings.ignored_recurring_tasks_list_ids || [];
          const filteredTasks = recurringTasksResponse.tasks.filter(
            (task) => !ignoredListIds.includes(task.list.id)
          );

          const pageTaskIds = filteredTasks.map((task) => task.id);
          await io.logger.info(
            `Fetched ${recurringTasksResponse.tasks.length} recurring task IDs from view ${globalSettings.recurring_tasks_view_id} (page ${page}), filtered to ${pageTaskIds.length} after removing ignored lists`,
          );

          lastPage = recurringTasksResponse.last_page;
          return pageTaskIds;
        });

        allTaskIds = allTaskIds.concat(taskIds);
        page++;
      }

      // Remove duplicates from the collected task IDs
      const uniqueTaskIds = Array.from(new Set(allTaskIds));

      await io.logger.info(
        `Fetched total of ${allTaskIds.length} recurring task IDs (${uniqueTaskIds.length} unique) from view ${globalSettings.recurring_tasks_view_id} across ${page} pages`,
      );

      // Check which task IDs are missing from the tasks table
      if (uniqueTaskIds.length > 0) {
        const { data: existingTasks, error: checkError } = await supabase
          .from('tasks')
          .select('clickup_task_id')
          .in('clickup_task_id', uniqueTaskIds);

        if (checkError) {
          await io.logger.error(`Error checking existing tasks: ${JSON.stringify(checkError)}`);
          return;
        }

        const existingTaskIds = new Set(existingTasks?.map((task) => task.clickup_task_id) || []);
        const missingTaskIds = uniqueTaskIds.filter((taskId) => !existingTaskIds.has(taskId));

        // Trigger taskCreatedJob for missing tasks
        if (missingTaskIds.length > 0) {
          await io.logger.info(`Found ${missingTaskIds.length} missing tasks, triggering taskCreatedJob for each`);

          for (const taskId of missingTaskIds) {
            await io.runTask(`create-missing-task-${taskId}`, async () => {
              await taskCreatedJob.invokeAndWaitForCompletion(`create-task-${taskId}`, {
                createdTaskId: taskId,
              });
              await io.logger.info(`Successfully triggered taskCreatedJob for task ${taskId}`);
            });
          }
        }

        // Insert task IDs into recurring_tasks table (ignore conflicts for existing IDs)
        const insertData = uniqueTaskIds.map((taskId) => ({
          clickup_task_id: taskId,
        }));

        const { error } = await supabase.from('recurring_tasks').upsert(insertData, {
          onConflict: 'clickup_task_id',
        });

        if (error) {
          await io.logger.error(`Error inserting recurring task IDs: ${JSON.stringify(error)}`);
          return;
        }

        await io.logger.info(`Successfully synced ${uniqueTaskIds.length} recurring task IDs to database`);
      }

      return;
    });

    // Step 2: Process recurring tasks with missing schedules
    await io.runTask('process-missing-schedule-recurring-tasks', async () => {
      const supabase = createClient();

      // Get all recurring tasks with missing schedules, including task details
      const { data: tasksWithoutSchedule, error: missingScheduleError } = await supabase
        .from('recurring_tasks')
        .select(
          `
          clickup_task_id,
          tasks(
            name,
            clickup_time_estimate,
            clickup_list_id,
            clickup_assignees
          )
        `,
        )
        .is('schedule', null);

      if (missingScheduleError) {
        await io.logger.error(`Error fetching tasks without schedules: ${JSON.stringify(missingScheduleError)}`);
        return;
      }

      if (!tasksWithoutSchedule || tasksWithoutSchedule.length === 0) {
        await io.logger.info('No recurring tasks with missing schedules found');
        return;
      }

      await io.logger.info(`Found ${tasksWithoutSchedule.length} recurring tasks with missing schedules`);

      // Get all recurring tasks with copy_task enabled and schedules, including task details
      const { data: copyTaskRecurringTasks, error: copyTaskError } = await supabase
        .from('recurring_tasks')
        .select(
          `
          clickup_task_id, 
          schedule, 
          schedule_options, 
          skip_weekends, 
          copy_task, 
          recur_end,
          tasks(
            name,
            clickup_time_estimate,
            clickup_list_id,
            clickup_assignees
          )
        `,
        )
        .eq('copy_task', true);

      if (copyTaskError) {
        await io.logger.error(`Error fetching copy_task recurring tasks: ${JSON.stringify(copyTaskError)}`);
        return;
      }

      if (!copyTaskRecurringTasks || copyTaskRecurringTasks.length === 0) {
        await io.logger.info('No recurring tasks with copy_task enabled found');
        return;
      }

      await io.logger.info(`Found ${copyTaskRecurringTasks.length} recurring tasks with copy_task enabled`);

      // Process each recurring task with missing schedule
      for (const taskWithoutSchedule of tasksWithoutSchedule) {
        await io.runTask(`process-missing-schedule-${taskWithoutSchedule.clickup_task_id}`, async () => {
          // Use the joined task data instead of making a separate query
          const currentTask = taskWithoutSchedule.tasks;

          if (!currentTask) {
            await io.logger.warn(`Task details not found for recurring task ${taskWithoutSchedule.clickup_task_id}`);
            return;
          }

          // Find a matching recurring task with copy_task enabled
          let matchingRecurringTask = null;
          const currentAssignees = (currentTask.clickup_assignees || []).sort();

          for (const copyTaskRecurringTask of copyTaskRecurringTasks) {
            // Use the joined task data instead of making a separate query
            const copyTaskTask = copyTaskRecurringTask.tasks;

            if (
              copyTaskTask &&
              copyTaskTask.name === currentTask.name &&
              copyTaskTask.clickup_time_estimate === currentTask.clickup_time_estimate &&
              copyTaskTask.clickup_list_id === currentTask.clickup_list_id
            ) {
              const copyTaskAssignees = (copyTaskTask.clickup_assignees || []).sort();
              if (JSON.stringify(currentAssignees) === JSON.stringify(copyTaskAssignees)) {
                matchingRecurringTask = copyTaskRecurringTask;
                break;
              }
            }
          }

          // Copy schedule values if a match is found
          if (matchingRecurringTask) {
            await io.logger.info(
              `Found matching recurring task ${matchingRecurringTask.clickup_task_id} for task ${taskWithoutSchedule.clickup_task_id}`,
            );

            const { error: updateError } = await supabase
              .from('recurring_tasks')
              .update({
                schedule: matchingRecurringTask.schedule,
                schedule_options: matchingRecurringTask.schedule_options,
                skip_weekends: matchingRecurringTask.skip_weekends,
                copy_task: matchingRecurringTask.copy_task,
                recur_end: matchingRecurringTask.recur_end,
              })
              .eq('clickup_task_id', taskWithoutSchedule.clickup_task_id);

            if (updateError) {
              await io.logger.error(`Error updating recurring task schedule: ${JSON.stringify(updateError)}`);
            } else {
              await io.logger.info(
                `Copied schedule from ${matchingRecurringTask.clickup_task_id} to ${taskWithoutSchedule.clickup_task_id}`,
              );
            }
          } else {
            await io.logger.info(
              `No matching recurring task with copy_task found for ${taskWithoutSchedule.clickup_task_id}`,
            );
          }
        });
      }
    });

    // Step 3: Final check for remaining tasks without schedules
    await io.runTask('final-check-missing-schedules', async () => {
      const supabase = createClient();
      const { data: tasksWithoutSchedule, error } = await supabase
        .from('recurring_tasks')
        .select('clickup_task_id')
        .is('schedule', null);

      if (error) {
        await io.logger.error(`Error fetching tasks without schedules: ${JSON.stringify(error)}`);
        return;
      }

      if (tasksWithoutSchedule && tasksWithoutSchedule.length > 0) {
        const taskIds = tasksWithoutSchedule.map((task) => task.clickup_task_id);
        await io.logger.warn(
          `Found ${tasksWithoutSchedule.length} recurring tasks still without schedules after copy attempt: ${taskIds.join(', ')}`,
        );
      } else {
        await io.logger.info('All recurring tasks have schedules set');
      }
    });
  },
});
