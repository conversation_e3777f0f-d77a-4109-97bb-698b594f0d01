import { cronTrigger, IO } from '@trigger.dev/sdk';
import { client as triggerClient, client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { C<PERSON>UpGroup, ClickUpGroupMember, isClickUpError } from '@/data/types/clickup.types';
import { getSimpleTimeReportAction } from '@/server-actions/time-reporting/simple-report';
import { EmailClient } from '@/data/email-client';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { convertCronToUTC, getISODateString } from '@/lib/utils';
import { Tables } from '@/types/gen/database-table';
import { notifyWebmasterJob } from '@/jobs';

export const timeReportingJob = client.defineJob({
  id: 'time-reporting',
  name: 'Time Reporting Job',
  version: '0.0.1',
  trigger: cronTrigger({ cron: convertCronToUTC('30 7 * * *') }), // every day at 7:30
  run: async (payload, io, _) => {
    const [isMonday, is4thDayOfMonth] = await io.runTask('get-dates', async () => {
      const isMonday = new Date().getDay() === 1;
      const is4thDayOfMonth = new Date().getDate() === 4;
      return [isMonday, is4thDayOfMonth];
    });

    if (!isMonday && !is4thDayOfMonth) {
      await io.logger.info('Not Monday or 4th day of month. Skipping job.');
      return;
    }

    const [groups, kader] = await io.runTask('fetch-kader', async () => {
      const clickupClient = new ClickUpClient();
      const groups = await clickupClient.getGroups();

      if (isClickUpError(groups)) {
        throw new Error(`Error fetching kader: ${JSON.stringify(groups)}`);
      }

      const kader = groups.groups.filter((g) => g.name === 'Kader').flatMap((g) => g.members);
      return [groups.groups, kader];
    });

    const employees = await io.runTask('fetch-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('clickup_user_id', { ascending: true });

      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const [firstDayOfLastMonth, lastDayOfLastMonth, lastWeekMonday, lastWeekSunday] = await io.runTask(
      'get-start-end-date',
      async () => {
        const firstDayOfLastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1, 0, 0, 0, 0);
        const lastDayOfLastMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 0, 23, 59, 59, 999);

        const lastWeekMonday = new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate() - new Date().getDay() - 6,
          0,
          0,
          0,
          0,
        );
        const lastWeekSunday = new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate() - new Date().getDay(),
          23,
          59,
          59,
          999,
        );

        return [firstDayOfLastMonth, lastDayOfLastMonth, lastWeekMonday, lastWeekSunday];
      },
    );

    if (isMonday) {
      const start = getISODateString(new Date(lastWeekMonday));
      const end = getISODateString(new Date(lastWeekSunday));
      const [timeReportWeekly, productivityReportWeekly] = await runTimeReportingRoutine(
        start,
        end,
        false,
        employees,
        groups,
        io,
      );

      await sendKaderTimeReport(
        timeReportWeekly,
        productivityReportWeekly,
        false,
        lastWeekMonday,
        lastWeekSunday,
        kader,
        io,
      );

      await sendUserTimeReport(timeReportWeekly, false, lastWeekMonday, lastWeekSunday, employees, io);
    }

    if (is4thDayOfMonth) {
      const start = getISODateString(new Date(firstDayOfLastMonth));
      const end = getISODateString(new Date(lastDayOfLastMonth));
      await io.logger.info(`runTimeReportingRoutine ${start}, ${end}, ${true}, ${employees}, ${groups}, ${io}`);
      const [timeReportMonthly, productivityReportMonthly] = await runTimeReportingRoutine(
        start,
        end,
        true,
        employees,
        groups,
        io,
      );

      await sendKaderTimeReport(
        timeReportMonthly,
        productivityReportMonthly,
        true,
        firstDayOfLastMonth,
        lastDayOfLastMonth,
        kader,
        io,
      );

      await sendUserTimeReport(timeReportMonthly, true, firstDayOfLastMonth, lastDayOfLastMonth, employees, io);
    }
  },
  onFailure: async (notification) => {
    if (notification.isTest) {
      return;
    }

    const res = await triggerClient.getRuns(notification.job.id, {
      take: 1,
    });
    const runDetails = await triggerClient.getRun(res.runs[0].id);

    await notifyWebmasterJob.invoke({
      subject: `[Internal Tools] Fehler in Time Reporting Job: ${notification.invocation.id}`,
      message: `
      Link to runs: ${res.runs.map((run) => `<p>${process.env.TRIGGER_API_URL}/orgs/lumeos-gmbh-adc7/projects/internal-tools-PmKb/jobs/${notification.job.id}/runs/${run.id}/trigger </p>`).join('\n')}
      <br>
      <pre>
      ${JSON.stringify(runDetails, null, 2).replace(/\n/g, '<br>')}
      </pre>
      `,
    });
  },
});

async function runTimeReportingRoutine(
  startStr: string,
  endStr: string,
  isMonthly: boolean,
  employees: Tables<'employees'>[],
  groups: ClickUpGroup[],
  io: IO,
): Promise<[TimeReporting, ProductivityReport]> {
  const start = new Date(new Date(startStr).setHours(0, 0, 0, 0));
  const end = new Date(new Date(endStr).setHours(23, 59, 59, 999));

  const [timeReport, productivityReport] = await io.runTask(
    `fetch-time-report-${isMonthly ? 'monthly' : 'weekly'}`,
    async () => {
      await io.logger.info(
        `getSimpleTimeReportAction ${startStr}, ${endStr}, ${start}, ${end}, ${undefined}, ${endStr}`,
      );
      const { data: report } = await getSimpleTimeReportAction(startStr, endStr, start, end, undefined, endStr);

      const inactiveEmployees = employees.filter((e) => e.inactive).map((e) => e.name);
      const reportSummary: TimeReporting = report.filter((r) => !inactiveEmployees.includes(r.name));
      const overHoursAvg = reportSummary.reduce((acc, r) => acc + Number(r.overHours), 0) / reportSummary.length;
      const overHoursSaldoAvg =
        reportSummary.reduce((acc, r) => acc + Number(r.overHoursSaldo), 0) / reportSummary.length;
      const productivityAvg = reportSummary.reduce((acc, r) => acc + Number(r.productivity), 0) / reportSummary.length;
      reportSummary.push({
        name: 'Durchschnitt',
        overHours: overHoursAvg.toFixed(2),
        overHoursSaldo: overHoursSaldoAvg.toFixed(2),
        productivity: productivityAvg.toFixed(2),
      } as any);

      // productivity for each group
      const productivity = groups.map((group) => {
        const groupMembers = group.members.map((m) => m.username);
        const groupTimeReport = report.filter((r) => groupMembers.includes(r.name));
        const productivitySum = groupTimeReport.reduce((acc, r) => acc + Number(r.productivity), 0);
        return { name: group.name, productivity: (productivitySum / groupTimeReport.length).toFixed(2) };
      });

      return [reportSummary, productivity];
    },
  );

  return [timeReport, productivityReport];
}

type TimeReporting = Awaited<ReturnType<typeof getSimpleTimeReportAction>>['data'];
type ProductivityReport = {
  name: string;
  productivity: string;
}[];

async function sendKaderTimeReport(
  timeReport: TimeReporting,
  productivityReport: ProductivityReport,
  isMonthly: boolean,
  start: Date,
  end: Date,
  kader: ClickUpGroupMember[],
  io: IO,
) {
  await io.runTask(`send-kader-time-report-${isMonthly ? 'monthly' : 'weekly'}`, async () => {
    const emailClient = new EmailClient();
    const betreff = `LUMEOS Timereporting ${isMonthly ? format(end, 'MMM yyyy', { locale: de }) : `${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}`}`;
    const content = `<html lang='de'>
<head>
<title>LUMEOS Timereporting</title>
  <style>
    table, th, td {
      border: 1px solid black;
      border-collapse: collapse;
    }
    th, td {
      padding: 5px;
    }
  </style>
</head>
  <body>
    <p>Hey {NAME}</p>
    <p>Nachfolgend findest du das LUMEOS Timereporting für ${isMonthly ? 'den Monat' : 'die Woche'} vom ${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}.</p>
    <h3>Gesammtübersicht:</h3>
    <table>
      <tr>
        <th>Name</th>
        <th>Erfasste Zeit</th>
        <th>Überstunden ${isMonthly ? format(start, 'MMMM', { locale: de }) : `${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}`}</th>
        <th>Überstunden Saldo per ${format(end, 'dd.MM')}</th>
        <th>Produktivität</th>
        <th>Ferien bezogen</th>
        <th>Ferien Guthaben</th>
        <th>Ferien geplant</th>
      </tr>
      ${timeReport.map((r) => `<tr><td>${r.name}</td><td>${r.totalHours ? `${r.totalHours} h` : ''}</td><td>${r.overHours} h</td><td>${r.overHoursSaldo} h</td><td>${r.productivity} %</td><td>${r.usedPTO ? `${r.usedPTO} d` : '-'}</td><td>${r.ptoCreditsSaldo ? `${r.ptoCreditsSaldo} d` : '-'}</td><td>${r.estimatedNotUsedPTO ? `${r.estimatedNotUsedPTO} d` : '-'}</td></tr>`).join('')}
    </table>
    <h3>Produktivität:</h3>
    <table>
      <tr>
        <th>Team</th>
        <th>Produktivität</th>
      </tr>
      ${productivityReport.map((r) => `<tr><td>${r.name}</td><td>${r.productivity} %</td></tr>`).join('')}
    </table>
    <br>
    <p>Bei Fragen oder Unklarheiten melde dich ungeniert bei der Personalabteilung.</p>
    <p>Liebe Grüsse</p>
    <p>Personalabteilung<br>
    LUMEOS GmbH<br>
    <EMAIL></p>
  </body>
</html>
`;
    for (const kaderEmployee of kader) {
      await io.runTask(`send-kader-email-${isMonthly ? 'monthly' : 'weekly'}-${kaderEmployee.email}`, async () => {
        const firstName = kaderEmployee.username.split(' ')[0];
        const contentWithName = content.replace('{NAME}', firstName);
        await emailClient.sendEmail(kaderEmployee.email, betreff, contentWithName);
      });
    }
  });
}

async function sendUserTimeReport(
  timeReport: TimeReporting,
  isMonthly: boolean,
  start: Date,
  end: Date,
  employees: Tables<'employees'>[],
  io: IO,
) {
  await io.runTask(`send-user-time-report-${isMonthly ? 'monthly' : 'weekly'}`, async () => {
    const emailClient = new EmailClient();
    const betreff = `Dein Timereporting ${isMonthly ? format(end, 'MMM yyyy', { locale: de }) : `${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}`}`;
    const content = `<html lang='de'>
<head>
<title>LUMEOS Timereporting</title>
  <style>
    table, th, td {
      border: 1px solid black;
      border-collapse: collapse;
    }
    th, td {
      padding: 5px;
    }
  </style>
</head>
  <body>
    <p>Hey {NAME}</p>
    <p>Nachfolgend findest du dein Timereporting für ${isMonthly ? `den Monat ${format(end, 'MMM yyyy', { locale: de })}` : `die Woche vom ${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}`}.</p>
    <table>
      <tr>
        <th>Name</th>
        <th>Erfasste Zeit</th>
        <th>Überstunden ${isMonthly ? format(start, 'MMMM', { locale: de }) : `${format(start, 'dd.MM')} - ${format(end, 'dd.MM')}`}</th>
        <th>Überstunden Saldo per ${format(end, 'dd.MM')}</th>
        <th>Produktivität</th>
        <th>Ferien bezogen</th>
        <th>Ferien Guthaben</th>
        <th>Ferien geplant</th>
      </tr>
    {HOURS_ROW}
    </table>
    <br>
    <p>Bei Fragen oder Unklarheiten melde dich ungeniert bei der Personalabteilung.</p>
    <p>Liebe Grüsse</p>
    <p>Personalabteilung<br>
    LUMEOS GmbH<br>
    <EMAIL></p>
  </body>
</html>
`;
    for (const employee of employees) {
      await io.runTask(`send-user-email-${isMonthly ? 'monthly' : 'weekly'}-${employee.email}`, async () => {
        const firstName = employee.name.split(' ')[0];
        const contentWithName = content.replace('{NAME}', firstName);
        const timeReportRow = timeReport.find((r) => r.name === employee.name);
        if (!timeReportRow) {
          await io.logger.error(`No time report found for ${employee.name}`);
          return;
        }
        const contentWithHours = contentWithName.replace(
          '{HOURS_ROW}',
          `<tr><td>${timeReportRow.name}</td><td>${timeReportRow.totalHours ? `${timeReportRow.totalHours} h` : ''}</td><td>${timeReportRow.overHours} h</td><td>${timeReportRow.overHoursSaldo} h</td><td>${timeReportRow.productivity} %</td><td>${timeReportRow.usedPTO ? `${timeReportRow.usedPTO} d` : '-'}</td><td>${timeReportRow.ptoCreditsSaldo ? `${timeReportRow.ptoCreditsSaldo} d` : '-'}</td><td>${timeReportRow.estimatedNotUsedPTO ? `${timeReportRow.estimatedNotUsedPTO} d` : '-'}</td></tr>`,
        );
        await emailClient.sendEmail(employee.email, betreff, contentWithHours);
      });
    }
  });
}
