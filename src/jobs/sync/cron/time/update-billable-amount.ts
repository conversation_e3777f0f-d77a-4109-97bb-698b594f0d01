import { createClient } from '@/data/supabase-server';
import { Tables } from '@/types/gen/database-table';

export async function updateBillableAmount(
  timeEntries: Tables<'time_entries'>[],
  clickupTags?: Tables<'business_activities'>[],
) {
  const supabase = createClient();
  let tags: Tables<'business_activities'>[] = clickupTags || [];
  if (!clickupTags) {
    const { data, error } = await supabase.from('business_activities').select('*');

    if (error) {
      throw new Error(`Error fetching business activities: ${JSON.stringify(error)}`);
    }
    tags = data || [];
  }

  const updatedValues = timeEntries
    .filter((entry) => entry.billable)
    .map((entry) => {
      const tag = tags.find((tag) => tag.clickup_task_tag === entry.clickup_task_tag);
      if (!tag) {
        return { clickup_time_entry_id: entry.clickup_time_entry_id, billable_amount: entry.billable_amount };
      }

      const timeInHours = (entry.clickup_duration || 0) / 1000 / 60 / 60;
      const newBillableAmount = timeInHours * (tag?.hourly_rate || 0);
      return { clickup_time_entry_id: entry.clickup_time_entry_id, billable_amount: newBillableAmount };
    });

  const { error } = await supabase.from('time_entries').upsert(updatedValues, { onConflict: 'clickup_time_entry_id' });

  if (error) {
    throw new Error(`Error updating time entry: ${JSON.stringify(error)}`);
  }

  return;
}
