import { cronTrigger } from '@trigger.dev/sdk';
import { client, client as triggerClient } from '@/trigger';
import * as AllJobs from '@/jobs';
import { notify<PERSON>eb<PERSON><PERSON><PERSON> } from '@/jobs';
import { convertCronToUTC } from '@/lib/utils';

export const webmasterReportsJob = client.defineJob({
  id: 'webmaster-reports',
  name: 'Webmaster Reports',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 * * * *') }), // every hour
  run: async (payload, io, _) => {
    await io.runTask('general-job-report', async () => {
      // Run only if its 4 am
      if (new Date().getHours() !== 4) {
        return;
      }

      const jobs = Object.keys(AllJobs).map((jobName) => {
        return AllJobs[jobName as keyof typeof AllJobs];
      });

      let jobStatusReports: Record<string, Record<string, number>> = {};
      for (const job of jobs) {
        if ('id' in job) {
          jobStatusReports[job.id] = await io.runTask(`report-job-${job.id}`, async () => {
            const before24hrs = new Date(new Date().getTime() - 1000 * 60 * 60 * 24);
            const res = await triggerClient.getRuns(job.id, {
              take: 50,
            });
            let runs = res.runs.filter((run) => run.startedAt && run.startedAt >= before24hrs).map((run) => run.status);
            let lastRun = res.runs[res.runs.length - 1];
            while (lastRun && lastRun.startedAt && lastRun.startedAt >= before24hrs) {
              const res = await triggerClient.getRuns(job.id, {
                take: 50,
                cursor: lastRun.id,
              });
              lastRun = res.runs[res.runs.length - 1];
              const newRuns = res.runs
                .filter((run) => run.startedAt && run.startedAt >= before24hrs)
                .map((run) => run.status);
              runs = runs.concat(newRuns);
            }

            return runs.reduce(
              (acc, status) => {
                if (!acc[status]) {
                  acc[status] = 0;
                }
                acc[status]++;
                return acc;
              },
              {} as Record<string, number>,
            );
          });
          await io.wait(`yield-for-job-${job.id}`, 3);
        }
      }

      await notifyWebmasterJob.invokeAndWaitForCompletion('notify-webmaster-job-full-report', {
        subject: `[Internal Tools] Job Reports`,
        message: `Job Reports:
        <br>
        <br>
        ${JSON.stringify(jobStatusReports, null, 2).replace(/\n/g, '<br>')}
        `,
      });
    });

    await io.runTask('check-job-health-and-notify-webmaster', async () => {
      const jobsToCheck = [{ id: 'sync-clickup-time-entries-to-bexio-timesheet', takes: 4 }];

      for (const { id: jobId, takes: jobTake } of jobsToCheck) {
        await io.runTask(`check-job-${jobId}`, async () => {
          const res = await triggerClient.getRuns(jobId, {
            take: jobTake,
          });
          const containsFailure = res.runs.some((run) => run.status === 'FAILURE');
          const failedRuns = res.runs.filter((run) => run.status === 'FAILURE');
          if (containsFailure) {
            await io.logger.error(`Job ${jobId} has failed: ${JSON.stringify(failedRuns)}`);
            const runDetails = await triggerClient.getRun(failedRuns[0].id);

            await notifyWebmasterJob.invokeAndWaitForCompletion(`notify-webmaster-job-${jobId}`, {
              subject: `[Internal Tools] Fehler in Job ${res.runs[0].id}`,
              message: `Fehler in JOB(s):
              \n
              Link to runs: ${failedRuns.map((run) => `${process.env.TRIGGER_API_URL}/orgs/lumeos-gmbh-adc7/projects/internal-tools-PmKb/jobs/${jobId}/runs/${run.id}/trigger`).join('\n')}
              
              \n\n
              ${JSON.stringify(runDetails)}`,
            });
          }
        });
      }
    });
  },
});
