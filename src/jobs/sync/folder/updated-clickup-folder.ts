import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpFolder, isClickUpError } from '@/data/types/clickup.types';
import { updateClientDropboxFoldersJob } from '@/jobs/shared/dropbox/update-client-dropbox-folders-job';

export const updatedClickupFolderJob = client.defineJob({
  id: 'updated-clickup-folder',
  name: 'Updated clickup folder',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const { updatedFolderId } = payload;

    // @ts-ignore
    const updatedFolder: ClickUpFolder = await io.runTask('fetch-clickup-folder', async () => {
      const clickupClient = new ClickUpClient();
      const folder = await clickupClient.getFolder(updatedFolderId);

      if (isClickUpError(folder)) {
        throw new Error(`Error fetching folder: ${JSON.stringify(folder)}`);
      }

      return folder;
    });

    const [isWrongArchival, meaningfulChange] = await io.runTask('should-update', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('clickup_folder_id', updatedFolderId)
        .single();

      if (error) {
        throw new Error(`Error fetching project: ${JSON.stringify(error)}`);
      }

      const meaningfulChange =
        data &&
        (data.name !== updatedFolder.name ||
          data.clickup_folder_assignee_id != updatedFolder.assignee?.id ||
          data.clickup_archived !== updatedFolder.archived);

      if (!data || !updatedFolder.archived) {
        return [false, meaningfulChange];
      }

      // pc: is client folder and archived
      return [updatedFolder.archived && updatedFolder.lists.length > 0, meaningfulChange];
    });

    if (!meaningfulChange) {
      await io.logger.info('No meaningful change detected, skipping update');
      return;
    }

    if (isWrongArchival) {
      const globalSettings = await io.runTask('fetch-global-settings', async () => {
        const supabase = createClient();
        const { data } = await supabase.from('global_settings').select('*').single();

        return data;
      });

      await io.runTask('send-email-notification', async () => {
        const clickupClient = new ClickUpClient();
        await clickupClient.createTask(String(globalSettings?.pm_list_id), {
          name: `adm: Kundenordner mit aktiver Liste archiviert`,
          description: `Jemand hat fälschlicherweise den Kundenordner ${updatedFolder.name} archiviert, obwohl der Kunde noch aktive Listen hat.
Reaktivere den Kundenordner auf Clickup!`,
          assignees: [process.env.CLICKUP_ADMIN_USER_ID],
          priority: 1,
          time_estimate: 10 * 60 * 1000,
          due_date: new Date().getTime(),
        });
      });
    }

    const newFolder = await io.runTask('update-folder', async () => {
      const newClient = {
        clickup_folder_id: updatedFolder.id,
        name: updatedFolder.name,
        clickup_folder_assignee_id: updatedFolder.assignee?.id,
        clickup_archived: updatedFolder.archived,
      };

      const supabase = createClient();
      const { data, error: updateClickupFoldersError } = await supabase
        .from('clients')
        .upsert(newClient, { onConflict: 'clickup_folder_id' })
        .select()
        .single();

      if (updateClickupFoldersError) {
        throw new Error(`Error updating folder: ${JSON.stringify(updateClickupFoldersError)}`);
      }

      return data;
    });

    await updateClientDropboxFoldersJob.invokeAndWaitForCompletion('update-dropbox-folder', { folders: [newFolder] });
  },
});
