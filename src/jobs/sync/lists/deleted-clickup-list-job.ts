import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { DropboxClient } from '@/data/dropbox-client';
import { buildDropboxProjectFolderPath } from '@/lib/utils';
import { ClickUpClient } from '@/data/clickup-client';
import { markDeletionConfirmed, markForDeletion } from '@/jobs/shared/delete-logs';

export const deletedClickupListJob = client.defineJob({
  id: 'deleted-clickup-list',
  name: 'Deleted clickup list',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { deletedListId } = payload;

    await io.wait('init-wait-race-cond', 5);

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    // FETCH DATA FROM DB
    const list = await io.runTask('fetch-data-from-db', async () => {
      const supabase = createClient();
      const { data: clientData, error: clientError } = await supabase
        .from('projects')
        .select('*, clients(*)')
        .eq('clickup_list_id', deletedListId)
        .eq('deleted', false)
        .single();

      if (clientError) {
        throw new Error(`Error fetching client: ${JSON.stringify(clientError)} | Already deleted?`);
      }

      return clientData;
    });

    await io.runTask('notify-project-deleted', async () => {
      if (!list.is_client_project) {
        await io.logger.info(`Skipping notification. ${list.clickup_list_id} - is not a client project`);
        return;
      }

      const clickupClient = new ClickUpClient();
      await clickupClient.createTask(String(globalSettings?.pm_list_id), {
        name: `adm: Liste "${list.name}" gelöscht`,
        description: `Jemand hat die Liste "${list.name}" gelöscht.
Kontrolliere ob die Liste korrekterweise gelöscht wurde:
- Keine Tasks mit Time-Entries auf der Liste
- Im Dropboxordner (zdeleted) schauen ob irgendwelche Dateien im Projektordner sind

Stelle die Liste aus dem Trash wieder her, wenn nötig: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/settings/trash
`,
        assignees: [process.env.CLICKUP_ADMIN_USER_ID],
        priority: 1,
        time_estimate: 10 * 60 * 1000,
        due_date: new Date().getTime(),
      });
    });

    // DELETE PROJECT IN BEXIO
    await io.runTask('delete-project-in-bexio', async () => {
      if (!list?.bexio_project_id) {
        await io.logger.info(`No bexio project found for list: ${JSON.stringify(list)}`);
        return;
      }

      // DELETE PROJECT IN BEXIO
      const delMark = await markForDeletion('bexio_project', list.bexio_project_id);
      const bexioClient = new BexioClient();
      const deleteResponse = await bexioClient.deleteProject(list.bexio_project_id);

      if (isBexioError(deleteResponse) && deleteResponse.error_code !== 404) {
        throw Error(`Error deleting project in bexio: ${JSON.stringify(deleteResponse)}`);
      }

      if (!delMark.data) {
        await io.logger.error(`Error marking for deletion: ${JSON.stringify(delMark)}`);
      } else {
        await markDeletionConfirmed(delMark.data.id);
      }

      // MARK PROJECT (BEXIO) AS DELETED IN DB
      const supabase = createClient();
      const { error } = await supabase
        .from('projects')
        .update({ deleted: true, bexio_project_id: null })
        .eq('clickup_list_id', deletedListId);

      if (error) {
        throw new Error(`Error deleting bexio project in db: ${JSON.stringify(error)}`);
      }
    });

    // DELETE DROPBOX FOLDER
    const newFolderPath = await io.runTask('delete-dropbox-folder', async () => {
      if (!list.dropbox_folder_path || !list.clients) {
        await io.logger.info(`No dropbox folder found for project: ${JSON.stringify(list)}`);
        return;
      }
      const dropboxClient = new DropboxClient();
      const newFolderPath = buildDropboxProjectFolderPath(list, list.clients, true);
      await io.try(
        async () => {
          if (!list.dropbox_folder_path) {
            return;
          }
          await dropboxClient.renameFolder(list.dropbox_folder_path, newFolderPath, true);
        },
        async (error) => {
          // @ts-ignore
          if (error?.status >= 400 && error?.status != 409) {
            throw new Error(`Error deleting dropbox folder: ${JSON.stringify(error)}`);
          }
        },
      );

      return newFolderPath.toLowerCase();
    });

    // DELETE PROJECT IN DB
    await io.runTask('delete-project-in-db', async () => {
      const supabase = createClient();
      const { error } = await supabase
        .from('projects')
        .update({ deleted: true, dropbox_folder_path: newFolderPath })
        .eq('clickup_list_id', deletedListId);

      if (error) {
        throw new Error(`Error deleting project: ${JSON.stringify(error)}`);
      }
    });
  },
});
