import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { ClickUpClient } from '@/data/clickup-client';
import { isBexioError } from '@/data/types/bexio.types';
import { createOrRenameProjectDropBoxFoldersJob } from '@/jobs/shared/dropbox/create-or-rename-project-drop-box-folders-job';
import { cleanProjectName, convertUNIXTimestampToDateTimeString, stripProjectPrefix } from '@/lib/utils';

export const newClickupListCreateNewBexioProjectJob = client.defineJob({
  id: 'new-clickup-list-create-new-bexio-project',
  name: 'New clickup list create new bexio project',
  version: '0.0.2',
  trigger: invokeTrigger(),
  run: async (payload, io, ctx) => {
    const { newClickupListId } = payload;

    // 1 FETCH CLICKUP LISTS AND INSERT INTO DB
    const newProjectList = await io.runTask('fetch-clickup-list', async () => {
      // 1.1 FETCH CLICKUP LIST
      const clickupClient = new ClickUpClient();
      const listResponse = await clickupClient.getList(newClickupListId);

      if ('err' in listResponse) {
        throw new Error(`Error fetching list: ${JSON.stringify(listResponse)}`);
      }

      // 1.2 INSERT CLICKUP LIST INTO DB
      const isClientProject = listResponse.space.id === process.env.CLICKUP_WORKSPACE_ID;
      const newList = {
        name: cleanProjectName(listResponse.name),
        clickup_list_id: listResponse.id,
        clickup_name: cleanProjectName(listResponse.name),
        clickup_archived: listResponse.archived,
        clickup_folder_id: isClientProject ? listResponse.folder.id : null,
        clickup_start: Number(listResponse.start_date),
        clickup_end: listResponse.due_date ? Number(listResponse.due_date) : null,
        is_client_project: isClientProject,
        clickup_user_id: listResponse.assignee?.id || null,
      };

      const { data, error: insertClickupListsError } = await supabaseServiceClient()
        .from('projects')
        .insert(newList)
        .select('*, clients(*), employees(*)');

      if (insertClickupListsError) {
        throw new Error(`Error inserting lists: ${JSON.stringify(insertClickupListsError)}`);
      }

      if (!data || !data.length) {
        throw new Error(`No data returned from inserting lists: ${JSON.stringify(data)}`);
      }

      return data;
    });

    const newProject = newProjectList[0];

    // 3 PUSH PROJECT TO BEXIO
    await io.runTask('push-project-to-bexio', async () => {
      if (!newProject.clients || !newProject.clients.bexio_contact_id) {
        await io.logger.log(`Skipping Project ${newProject.clickup_list_id} - has no client`);
        return;
      }

      const start = newProject.clickup_start
        ? convertUNIXTimestampToDateTimeString(newProject.clickup_start)
        : new Date().toISOString().split('T')[0];
      const end = newProject.clickup_end ? convertUNIXTimestampToDateTimeString(newProject.clickup_end) : null;
      const newBexioProject = {
        name: stripProjectPrefix(newProject.clickup_name),
        start_date: start,
        end_date: end,
        pr_state_id: newProject.clickup_is_archived ? 3 : 1, // archived = 3, active = 2, offen = 1
        pr_project_type_id: 2, // internal project = 1, external project = 2
        contact_id: newProject.clients.bexio_contact_id,
        user_id: newProject.employees?.bexio_user_id || 1,
      };

      await io.runTask(`create-bexio-project`, async () => {
        // 3.1 CREATE PROJECT IN BEXIO
        const createdProject = await io.runTask('make-project-bexio-project-request', async () => {
          const bexioClient = new BexioClient();
          const createdProject = await bexioClient.createProject(newBexioProject);

          if (isBexioError(createdProject)) {
            throw new Error(`Error creating project: ${JSON.stringify(createdProject)}`);
          }

          return createdProject;
        });

        // 3.2 UPDATE PROJECT NAME IN DB WITH BEXIO PROJECT ID
        const newProjectName = `${createdProject.nr} ${newProject.name}`;
        await io.runTask(`update-project-bexio-id`, async () => {
          const { error } = await supabaseServiceClient()
            .from('projects')
            .update({
              bexio_project_id: createdProject.id,
            })
            .eq('clickup_list_id', newProject.clickup_list_id);

          if (error) {
            throw new Error(`Error updating project in db: ${JSON.stringify(error)}`);
          }
        });

        // 3.3 RENAME PROJECT (LIST) IN CLICKUP
        const clickupClient = new ClickUpClient();
        const response = await clickupClient.updateList(newProject.clickup_list_id, {
          name: newProjectName,
        });
        if ('err' in response) {
          throw new Error(`Error renaming list: ${JSON.stringify(response)}`);
        }

        // 3.4 UPDATE PROJECT NAME IN DB WITH BEXIO PROJECT ID
        const supabase = createClient();
        const { error: updateProjectNameError, data: updatedProject } = await supabase
          .from('projects')
          .update({
            name: stripProjectPrefix(newProjectName),
            clickup_name: newProjectName,
          })
          .eq('clickup_list_id', newProject.clickup_list_id)
          .select('*, clients(*)');

        if (updateProjectNameError) {
          throw new Error(`Error updating project in db: ${JSON.stringify(updateProjectNameError)}`);
        }

        // 3.5 CREATE DROPBOX FOLDER
        const dropBoxJob = await createOrRenameProjectDropBoxFoldersJob.invokeAndWaitForCompletion(
          'create-dropbox-folder',
          {
            projects: updatedProject,
          },
        );

        if (!dropBoxJob.ok) {
          throw new Error(`Error creating dropbox folder: ${JSON.stringify(dropBoxJob)}`);
        }
      });
    });
  },
});
