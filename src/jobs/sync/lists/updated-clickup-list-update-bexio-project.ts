import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { ClickUpClient } from '@/data/clickup-client';
import { cleanProjectName, convertUNIXTimestampToDateTimeString, stripProjectPrefix } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { createOrRenameProjectDropBoxFoldersJob, deletedClickupListJob } from '@/jobs';
import { isClickUpError } from '@/data/types/clickup.types';

export const updatedClickupListUpdateBexioProjectJob = client.defineJob({
  id: 'updated-clickup-list-update-bexio-project',
  name: 'Updated clickup list update bexio project',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { updatedClickupListId } = payload;

    // 0 TIMEOUT TO CIRCUMVENT RACE CONDITIONS
    await io.wait('init-wait-race-cond', 2);

    // Skip if new archived status is different from bexio archived status
    const [shouldSkip, newArchivedStatus, shouldAbort] = await io.runTask('should-skip', async () => {
      const clickupClient = new ClickUpClient();
      const list = await clickupClient.getList(updatedClickupListId);

      if (isClickUpError(list)) {
        throw new Error(`Error fetching list: ${JSON.stringify(list)}`);
      }

      if (list.deleted) {
        await deletedClickupListJob.invokeAndWaitForCompletion(`delete-clickup-list-${list.id}`, {
          deletedListId: list.id,
        });
        return [true, true, true];
      }

      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('clickup_list_id', updatedClickupListId)
        .eq('is_client_project', true);

      if (error) {
        throw new Error(`Error fetching project: ${JSON.stringify(error)}`);
      }

      return [
        data && data.length && data[0].bexio_archived !== list.archived,
        data && data.length && data[0].bexio_archived,
        false,
      ];
    });

    if (shouldAbort) {
      await io.logger.info(`Aborting update project ${updatedClickupListId}`);
      return;
    }

    if (shouldSkip) {
      await io.logger.info(`Skipping project ${updatedClickupListId} - archived status is different from bexio`);
      await io.runTask('update-archived-status', async () => {
        // IS REDUNDANT?
        const clickupClient = new ClickUpClient();
        const list = await clickupClient.updateList(updatedClickupListId, { archived: newArchivedStatus });

        if (isClickUpError(list)) {
          throw new Error(`Error updating list: ${JSON.stringify(list)}`);
        }
      });
      return;
    }

    // 1 FETCH CLICKUP LISTS AND INSERT INTO DB
    const updatedProject = await io.runTask('fetch-clickup-list', async () => {
      // 1.1 FETCH CLICKUP LIST
      const clickupClient = new ClickUpClient();
      const list = await clickupClient.getList(updatedClickupListId);

      if (isClickUpError(list)) {
        throw new Error(`Error fetching list: ${JSON.stringify(list)}`);
      }

      // 1.2 UPDATE CLICKUP LIST IN DB
      const newList = {
        clickup_list_id: list.id,
        name: cleanProjectName(stripProjectPrefix(list.name)),
        clickup_start: Number(list.start_date),
        clickup_end: list.due_date ? Number(list.due_date) : null,
        clickup_name: list.name,
        clickup_archived: list.archived,
        clickup_folder_id: list.space?.id == process.env.CLICKUP_WORKSPACE_ID ? list.folder?.id : null,
        is_client_project: list.space?.id == process.env.CLICKUP_WORKSPACE_ID,
        clickup_user_id: list.assignee?.id || null,
      };

      const supabase = createClient();
      const { data, error: updateClickupListsError } = await supabase
        .from('projects')
        .update(newList)
        .eq('clickup_list_id', list.id)
        .select('*, clients(*), employees(*)')
        .single();

      if (updateClickupListsError) {
        throw new Error(`Error updating list: ${JSON.stringify(updateClickupListsError)}`);
      }

      if (!data) {
        throw new Error(`No data returned from updating list`);
      }

      return data;
    });

    // 2 UPDATE PROJECT TO BEXIO
    await io.runTask('updated-project-to-bexio', async () => {
      if (!updatedProject.bexio_project_id) {
        await io.logger.log(`Skipping Project ${updatedProject.clickup_list_id} - has no bexio project`);
        return;
      }

      if (!updatedProject.clients || !updatedProject.clients.bexio_contact_id) {
        await io.logger.log(`Skipping Project ${updatedProject.clickup_list_id} - has no client`);
        return;
      }

      const supabase = createClient();
      const { data: bexioProject, error: bexioError } = await supabase
        .from('bexio_projects')
        .select('*')
        .eq('bexio_id', updatedProject.bexio_project_id)
        .single();

      if (bexioError) {
        await io.logger.error(`Error fetching bexio project: ${JSON.stringify(bexioError)}`);
      }

      const prevNotArchivedStatus = bexioProject?.status === 'Offen' ? 1 : 2;
      const bexioUserId = Array.isArray(updatedProject.employees)
        ? updatedProject.employees[0].bexio_user_id
        : // @ts-ignore
          updatedProject.employees?.bexio_user_id;
      const end = updatedProject.clickup_end ? convertUNIXTimestampToDateTimeString(updatedProject.clickup_end) : null;
      let updatedBexioProject: any = {
        name: cleanProjectName(stripProjectPrefix(String(updatedProject.name))),
        end_date: end,
        pr_state_id: updatedProject.clickup_archived ? 3 : prevNotArchivedStatus, // archived = 3, active = 2, offen = 1
        pr_project_type_id: 2, // internal project = 1, external project = 2
        contact_id: updatedProject.clients.bexio_contact_id,
        user_id: bexioUserId || 1, // default user
      };

      if (updatedProject.clickup_start) {
        updatedBexioProject = {
          ...updatedBexioProject,
          start_date: convertUNIXTimestampToDateTimeString(updatedProject.clickup_start),
        };
      }

      await io.logger.info(`Updating project ${JSON.stringify(updatedProject.bexio_project_id)}`);
      const bexioClient = new BexioClient();
      const createdProject = await bexioClient.updateProject(updatedProject.bexio_project_id, updatedBexioProject);

      if (isBexioError(createdProject)) {
        throw new Error(`Error creating project: ${JSON.stringify(createdProject)}`);
      }

      // updating clickup list name if necessary
      if (
        updatedProject.clickup_list_id &&
        updatedProject.clickup_name &&
        !updatedProject.clickup_name.startsWith(String(createdProject.nr))
      ) {
        const cleanName = updatedProject.clickup_name.replace(/^P-\d+-\d+ /, '');
        const newName = `${createdProject.nr} ${cleanName}`;

        const clickupClient = new ClickUpClient();
        await clickupClient.updateList(Number(updatedProject.clickup_list_id), { name: newName });
      }
    });

    // 3 UPDATE OR CREATE DROPBOX FOLDER
    await io.runTask('update-or-create-dropbox-folder', async () => {
      if (!updatedProject.clients || !updatedProject.clients.bexio_contact_id) {
        await io.logger.log(`Skipping Dropbox Folder ${updatedProject.clickup_list_id} - has no client`);
        return;
      }

      await createOrRenameProjectDropBoxFoldersJob.invokeAndWaitForCompletion('create-or-rename-dropbox-folders', {
        projects: [updatedProject],
      });
    });
  },
});
