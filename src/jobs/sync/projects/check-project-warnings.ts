import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { areRotatedNamesEqual, prettyPrintCurrency } from '@/lib/utils';
import { isClickUpError } from '@/data/types/clickup.types';

export const checkProjectWarnings = client.defineJob({
  id: 'check-project-warnings',
  name: 'Check project warnings',
  version: '1.0.0',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const employees = await io.runTask('fetch-employees', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employees').select('*');

      if (error) {
        throw new Error(`Error fetching employees: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    const generalSettings = await io.runTask('fetch-general-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching general settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const [stage1Projects, stage2Projects, stage3Projects, stage4Projects] = await io.runTask(
      'bad-projects',
      async () => {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('bexio_projects')
          .select('*')
          .neq('status', 'Archiviert')
          .gt('budget', 0);

        if (error) {
          throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
        }

        const activeBexioProjects = data || [];

        const stage1Projects = activeBexioProjects.filter(
          (p) =>
            p.difference_estimated_cost_percentage &&
            p.difference_estimated_cost_percentage <= 10 &&
            p.difference_estimated_cost_percentage >= 0,
        );

        const stage2Projects = activeBexioProjects.filter(
          (p) =>
            p.difference_estimated_cost_percentage &&
            p.difference_estimated_cost_percentage <= 0 &&
            p.difference_estimated_cost_percentage >= -2,
        );

        const stage3Projects = activeBexioProjects.filter(
          (p) =>
            p.difference_estimated_cost_percentage &&
            p.difference_estimated_cost_percentage < -2 &&
            p.difference_estimated_cost_percentage >= -10,
        );

        const stage4Projects = activeBexioProjects.filter(
          (p) => p.difference_estimated_cost_percentage && p.difference_estimated_cost_percentage < -10,
        );

        return [stage1Projects, stage2Projects, stage3Projects, stage4Projects];
      },
    );

    await io.logger.info(`Found ${stage1Projects.length} projects with warnings`);
    await io.logger.info(`Found ${stage2Projects.length} projects with warnings`);
    await io.logger.info(`Found ${stage3Projects.length} projects with warnings`);
    await io.logger.info(`Found ${stage4Projects.length} projects with bad budget`);

    await io.runTask('send-warning-notifications-stage-1', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();
      for (const project of stage1Projects) {
        await io.runTask(`send-warning-notification-stage1-${project.bexio_id}`, async () => {
          const { data: notification, error } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `projectWarningStage1#${project.bexio_id}`);

          if (error) {
            throw new Error(`Error fetching notification for ${project.bexio_id}: ${error.message}`);
          }

          if (notification.length) {
            await io.logger.info(`Notification already sent for ${project.bexio_id}`);
            return;
          }

          const assignedEmployee = employees.find((e) => areRotatedNamesEqual(e.name, project.contact_person));
          if (!assignedEmployee?.clickup_user_id) {
            await io.logger.info(`No user id found for project: ${project.nr} ${project.name}`);
            return;
          }

          const projectDb = await supabase
            .from('projects')
            .select('*')
            .eq('bexio_project_id', project.bexio_id)
            .single();

          const taskResponse = await clickupClient.createTask(String(generalSettings.pm_list_id), {
            name: `pm: Budget 90% verplant - ${project.name} - ${project.client_name}`,
            description: `Achtung! Projekt zu 90% verplant, es verbleiben nur noch 10% des Budgets!
Die erwarteten Kosten des Projektes sind bei 90% des vorgegebenen Budgets.
Checke das Projekt bei Bedarf.

Kunde: ${project.client_name}
Projektname: ${project.nr} ${project.name}
Bexio Link: https://office.bexio.com/index.php/pr_project/show/id/${project.bexio_id}
Clickup Link: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${projectDb.data?.clickup_list_id}

Budget: ${prettyPrintCurrency(project.budget)}
Erwartete Kosten: ${prettyPrintCurrency(project.estimated_cost)}
∆ Budget/erw. Kosten: ${prettyPrintCurrency(project.difference_estimated_cost)}
∆ Budget/erw. Kosten %: ${project.difference_estimated_cost_percentage?.toFixed(2)} %
∆ Budget/eff. Kosten: ${prettyPrintCurrency(project.difference_budget)}
∆ Budget/eff. Kosten %: ${project.difference_budget_percentage?.toFixed(2)} %

Fortschritt: ${project.progress_percentage?.toFixed(2)} %

Projektleitung: ${assignedEmployee.name}
`,
            assignees: [Number(assignedEmployee.clickup_user_id)],
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(taskResponse)) {
            throw new Error(`Error creating task: ${JSON.stringify(taskResponse)}`);
          }

          const res = await supabase.from('error_notifications').insert({
            message_hash: `projectWarningStage1#${project.bexio_id}`,
          });

          if (res.error) {
            throw new Error(`Error creating error notification: ${JSON.stringify(res)}`);
          }
        });
      }
    });

    await io.runTask('send-warning-notifications-stage-2', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();
      for (const project of stage2Projects) {
        await io.runTask(`send-warning-notification-stage2-${project.bexio_id}`, async () => {
          const { data: notification, error } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `projectWarningStage2#${project.bexio_id}`);

          if (error) {
            throw new Error(`Error fetching notification for ${project.bexio_id}: ${error.message}`);
          }

          if (notification.length) {
            await io.logger.info(`Notification already sent for ${project.bexio_id}`);
            return;
          }

          const assignedEmployee = employees.find((e) => areRotatedNamesEqual(e.name, project.contact_person));
          if (!assignedEmployee?.clickup_user_id) {
            await io.logger.info(`No user id found for project: ${project.nr} ${project.name}`);
            return;
          }

          const projectDb = await supabase
            .from('projects')
            .select('*')
            .eq('bexio_project_id', project.bexio_id)
            .single();

          const taskResponse = await clickupClient.createTask(String(generalSettings.pm_list_id), {
            name: `pm: Budget erreicht - ${project.name} - ${project.client_name}`,
            description: `Achtung! Projekt zu 100% verplant, es verbleibt kein Budget mehr!
Die erwarteten Kosten des Projektes sind bei 100% des vorgegebenen Budgets.
Checke das Projekt bei Bedarf.

Kunde: ${project.client_name}
Projektname: ${project.nr} ${project.name}
Bexio Link: https://office.bexio.com/index.php/pr_project/show/id/${project.bexio_id}
Clickup Link: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${projectDb.data?.clickup_list_id}

Budget: ${prettyPrintCurrency(project.budget)}
Erwartete Kosten: ${prettyPrintCurrency(project.estimated_cost)}
∆ Budget/erw. Kosten: ${prettyPrintCurrency(project.difference_estimated_cost)}
∆ Budget/erw. Kosten %: ${project.difference_estimated_cost_percentage?.toFixed(2)} %
∆ Budget/eff. Kosten: ${prettyPrintCurrency(project.difference_budget)}
∆ Budget/eff. Kosten %: ${project.difference_budget_percentage?.toFixed(2)} %

Fortschritt: ${project.progress_percentage?.toFixed(2)} %

Projektleitung: ${assignedEmployee.name}
`,
            assignees: [Number(assignedEmployee.clickup_user_id)],
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(taskResponse)) {
            throw new Error(`Error creating task: ${JSON.stringify(taskResponse)}`);
          }

          const res = await supabase.from('error_notifications').insert({
            message_hash: `projectWarningStage2#${project.bexio_id}`,
          });

          if (res.error) {
            throw new Error(`Error creating error notification: ${JSON.stringify(res)}`);
          }
        });
      }
    });

    await io.runTask('send-warning-notifications-stage-3', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();
      for (const project of stage3Projects) {
        await io.runTask(`send-warning-notification-${project.bexio_id}`, async () => {
          const { data: notification, error } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `projectBudgetWarning#${project.bexio_id}`);

          if (error) {
            throw new Error(`Error fetching notification for ${project.bexio_id}: ${error.message}`);
          }

          if (notification.length) {
            await io.logger.info(`Notification already sent for ${project.bexio_id}`);
            return;
          }

          const assignedEmployee = employees.find((e) => areRotatedNamesEqual(e.name, project.contact_person));
          if (!assignedEmployee?.clickup_user_id) {
            await io.logger.info(`No user id found for project: ${project.nr} ${project.name}`);
            return;
          }

          const projectDb = await supabase
            .from('projects')
            .select('*')
            .eq('bexio_project_id', project.bexio_id)
            .single();

          const taskResponse = await clickupClient.createTask(String(generalSettings.pm_list_id), {
            name: `pm: Progn. Budgetüberzug <10% - ${project.name} - ${project.client_name}`,
            description: `Schlechte Finanzprognose! Projekt droht zu überziehen!
Die erwarteten Kosten des Projektes sind um 2-10% höher als das vorgegebene Budget.
Checke das Projekt und triff entsprechende Massnahmen.

Kunde: ${project.client_name}
Projektname: ${project.nr} ${project.name}
Bexio Link: https://office.bexio.com/index.php/pr_project/show/id/${project.bexio_id}
Clickup Link: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${projectDb.data?.clickup_list_id}

Budget: ${prettyPrintCurrency(project.budget)}
Erwartete Kosten: ${prettyPrintCurrency(project.estimated_cost)}
∆ Budget/erw. Kosten: ${prettyPrintCurrency(project.difference_estimated_cost)}
∆ Budget/erw. Kosten %: ${project.difference_estimated_cost_percentage?.toFixed(2)} %
∆ Budget/eff. Kosten: ${prettyPrintCurrency(project.difference_budget)}
∆ Budget/eff. Kosten %: ${project.difference_budget_percentage?.toFixed(2)} %

Fortschritt: ${project.progress_percentage?.toFixed(2)} %

Projektleitung: ${assignedEmployee.name}
`,
            assignees: [Number(assignedEmployee.clickup_user_id)],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(taskResponse)) {
            throw new Error(`Error creating task: ${JSON.stringify(taskResponse)}`);
          }

          const res = await supabase.from('error_notifications').insert({
            message_hash: `projectBudgetWarning#${project.bexio_id}`,
          });

          if (res.error) {
            throw new Error(`Error creating error notification: ${JSON.stringify(res)}`);
          }
        });
      }
    });

    await io.runTask('send-bad-project-budget-notifications', async () => {
      const supabase = createClient();
      const clickupClient = new ClickUpClient();

      for (const project of stage4Projects) {
        await io.runTask(`send-bad-project-budget-notification-${project.bexio_id}`, async () => {
          const { data: notification } = await supabase
            .from('error_notifications')
            .select('*')
            .eq('message_hash', `projectBadBudgetWarning#${project.bexio_id}`);

          if (notification?.length) {
            await io.logger.info(`Notification already sent for ${project.bexio_id}`);
            return;
          }

          const assignedEmployee = employees.find((e) => areRotatedNamesEqual(e.name, project.contact_person));
          if (!assignedEmployee?.clickup_user_id) {
            await io.logger.info(`No user id found for project: ${project.nr} ${project.name}`);
            return;
          }

          const projectDb = await supabase
            .from('projects')
            .select('*')
            .eq('bexio_project_id', project.bexio_id)
            .single();

          const description = `Schlechte Finanzprognose! Projekt droht MASSIV zu überziehen!
Die erwarteten Kosten des Projektes sind um mehr als 10% höher als das vorgegebene Budget.
Checke das Projekt und triff entsprechende Massnahmen. Melde dich beim Management.

Kunde: ${project.client_name}
Projektname: ${project.nr} ${project.name}
Bexio Link: https://office.bexio.com/index.php/pr_project/show/id/${project.bexio_id}
Clickup Link: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${projectDb.data?.clickup_list_id}

Budget: ${prettyPrintCurrency(project.budget)}
Erwartete Kosten: ${prettyPrintCurrency(project.estimated_cost)}
∆ Budget/erw. Kosten: ${prettyPrintCurrency(project.difference_estimated_cost)}
∆ Budget/erw. Kosten %: ${project.difference_estimated_cost_percentage?.toFixed(2)} %
∆ Budget/eff. Kosten: ${prettyPrintCurrency(project.difference_budget)}
∆ Budget/eff. Kosten %: ${project.difference_budget_percentage?.toFixed(2)} %

Fortschritt: ${project.progress_percentage?.toFixed(2)}%

Projektleitung: ${assignedEmployee.name}
          `;

          const clickupResponse1 = await clickupClient.createTask(String(generalSettings.pm_list_id), {
            name: `pm:  Progn. Budgetüberzug über 10% - ${project.name} - ${project.client_name}`,
            description,
            assignees: [Number(assignedEmployee.clickup_user_id)],
            priority: 1,
            time_estimate: 10 * 60 * 1000,
            due_date: new Date().getTime(),
          });

          if (isClickUpError(clickupResponse1)) {
            throw new Error(`Error creating task for ${project.bexio_id}: ${JSON.stringify(clickupResponse1)}`);
          }

          if (Number(assignedEmployee.clickup_user_id) !== Number(process.env.CLICKUP_ADMIN_USER_ID)) {
            const clickupResponse2 = await clickupClient.createTask(String(generalSettings.pm_list_id), {
              name: `pm: Schlechte Prognose - ${project.name}`,
              description,
              assignees: [Number(process.env.CLICKUP_ADMIN_USER_ID)],
              priority: 1,
              time_estimate: 10 * 60 * 1000,
              due_date: new Date().getTime(),
            });

            if (isClickUpError(clickupResponse2)) {
              throw new Error(`Error creating task for ${project.bexio_id}: ${JSON.stringify(clickupResponse2)}`);
            }
          }

          await supabase.from('error_notifications').insert({
            message_hash: `projectBadBudgetWarning#${project.bexio_id}`,
          });
        });
      }
    });
  },
});
