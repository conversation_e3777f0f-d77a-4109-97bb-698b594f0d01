import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import <PERSON> from 'papa<PERSON><PERSON>';
import { BexioCSVExportRow } from '@/data/types/bexio-csv-export.types';
import { updateProjectsReport } from '@/jobs';

export const submitProjectsReportJob = client.defineJob({
  id: 'submit-projects-report',
  name: 'Submit Projects Report from Node Bot',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { csv } = payload;

    await io.runTask('launch-update-action', async () => {
      const result = await new Promise<BexioCSVExportRow[]>((resolve, reject) =>
        Papa.parse(csv, {
          header: true,
          complete: async (result) => {
            await io.logger.info('Parsing complete | submitCSVData');
            resolve(result.data as BexioCSVExportRow[]);
          },
          error: async (error: any) => {
            console.error('Error parsing file:', error);
            await io.logger.error(`Error parsing file: ${JSON.stringify(error)}`);
            reject(new Error(`Error parsing file: ${JSON.stringify(error)}`));
          },
        }),
      );

      await updateProjectsReport.invokeAndWaitForCompletion('project-data-submission', result);
    });
  },
});
