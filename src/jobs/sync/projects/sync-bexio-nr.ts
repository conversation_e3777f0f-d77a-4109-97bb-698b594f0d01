import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertCronToUTC } from '@/lib/utils';

export const syncBexioNrJob = client.defineJob({
  id: 'sync-bexio-nr-job',
  name: 'Sync Bexio Nr Job',
  version: '0.0.2',
  trigger: cronTrigger({ cron: convertCronToUTC('0 6-23 * * *') }), // every hour from 6am to 11pm
  run: async (payload, io, _) => {
    await io.runTask('path-bexio-nr-sync', async () => {
      const supabase = createClient();
      const { data: domainProjects, error } = await supabase
        .from('projects')
        .select('*')
        .eq('is_client_project', true)
        .eq('deleted', false);

      if (error) {
        throw new Error(`Error fetching projects: ${JSON.stringify(error)}`);
      }

      const { data: bexioProjects, error: bexioError } = await supabase.from('bexio_projects').select('*');

      if (bexioError) {
        throw new Error(`Error fetching bexio projects: ${JSON.stringify(bexioError)}`);
      }

      let count = 0;
      for (const project of domainProjects) {
        const bexioProject = bexioProjects.find((p) => p.bexio_id && p.bexio_id == project.bexio_project_id);
        if (bexioProject) {
          if (project.clickup_name && bexioProject.nr && !project.clickup_name.startsWith(bexioProject.nr)) {
            count++;
          }
        }
      }

      await io.logger.info(`Found ${count} projects with out of bexio project nr`);

      for (const project of domainProjects) {
        const bexioProject = bexioProjects.find((p) => p.bexio_id && p.bexio_id == project.bexio_project_id);
        if (!bexioProject) {
          await io.logger.info(`No bexio project found for project: ${JSON.stringify(project)}`);
          continue;
        }

        if (project.clickup_name && bexioProject.nr && !project.clickup_name.startsWith(bexioProject.nr)) {
          const cleanName = project.clickup_name.replace(/^P-\d+-\d+ /, '');
          const newName = `${bexioProject.nr} ${cleanName}`;
          await io.logger.info(`Updating project ${project.clickup_name} name to ${newName}`);
          await io.runTask(`update-project-name-${project.clickup_list_id}`, async () => {
            const clickupClient = new ClickUpClient();
            const response = await clickupClient.updateList(Number(project.clickup_list_id), { name: newName });

            if (isClickUpError(response)) {
              await io.logger.error(`Error updating project name: ${JSON.stringify(response)}`);
            }
          });
        }
      }
    });
  },
});
