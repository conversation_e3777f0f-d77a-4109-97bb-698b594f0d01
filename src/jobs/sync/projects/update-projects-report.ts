import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { submitCSVData } from '@/server-actions/projekt-reporting/simple-report';
import { BexioCSVExportRow } from '@/data/types/bexio-csv-export.types';
import { checkProjectBexioAssigneeChange, checkProjectWarnings } from '@/jobs';

export const updateProjectsReport = client.defineJob({
  id: 'update-projects-report',
  name: 'Update Projects Report from Node Bot',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload: BexioCSVExportRow[], io, _) => {
    await io.runTask('update-projects-report', async () => {
      const mapped = await submitCSVData(payload);

      if ('error' in mapped && mapped.error) {
        throw new Error(`Error submitting data: ${JSON.stringify(mapped.error)}`);
      }

      await io.logger.info(`Successfully updated projects report`);
      await checkProjectBexioAssigneeChange.invokeAndWaitForCompletion('check-project-bexio-assignee-change', {});
      await checkProjectWarnings.invokeAndWaitForCompletion('check-project-warnings', {});
    });
  },
});
