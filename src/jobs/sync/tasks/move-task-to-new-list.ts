import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { isBexioError } from '@/data/types/bexio.types';
import { markDeletionConfirmed, markForDeletion } from '@/jobs/shared/delete-logs';

export const moveTaskToNewList = client.defineJob({
  id: 'move-task-to-new-list',
  name: 'Move task to new list',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { clickupTaskId } = payload;

    const taskData = await io.runTask('fetch-clickup-task', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('tasks')
        .select('*, projects(bexio_project_id), time_entries(*)')
        .eq('clickup_task_id', clickupTaskId)
        .single();

      if (error || !data) {
        await io.logger.error(`Error fetching tasks: ${JSON.stringify(error)}`);
        return data;
      }

      return data;
    });

    const newClickupTask = await io.runTask('fetch-new-task-from-clickup', async () => {
      const clickupClient = new ClickUpClient();
      const newTask = await clickupClient.getTask(clickupTaskId);

      if ('err' in newTask) {
        await io.logger.error(`Error fetching new task: ${JSON.stringify(newTask)}`);
        return null;
      }

      return newTask;
    });

    if (!taskData || !newClickupTask) {
      return;
    }

    const newList = await io.runTask('fetch-new-list-from-db', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('clickup_list_id', newClickupTask.list.id)
        .single();

      if (error || !data) {
        await io.logger.error(`Error fetching new list: ${JSON.stringify(error)}`);
        return data;
      }

      return data;
    });

    if (!newList?.bexio_project_id) {
      await io.logger.error(`No bexio project id found for list: ${newClickupTask.list.id}`);
      return;
    }

    if (newList?.bexio_archived) {
      await io.runTask(`unarchive-bexio-project-${newList.bexio_project_id}`, async () => {
        const bexioClient = new BexioClient();
        const unarchiveResponse = await bexioClient.updateProject(Number(newList.bexio_project_id), {
          pr_state_id: 2,
        });

        if (isBexioError(unarchiveResponse)) {
          throw new Error(`Error unarchiving project in bexio: ${JSON.stringify(unarchiveResponse)}`);
        }
      });
    }

    // CREATE NEW TASK IN BEXIO
    const newBexioTask = await io.runTask('create-new-task-in-bexio', async () => {
      const bexioWorkPackage = {
        name: trimLength(newClickupTask.name, 254), // TRIM TO 255 CHARS
        comment: trimLength(newClickupTask.description, 999), // TRIM TO 1000 CHARS
        estimated_time_in_hours: convertMillisecondsToHours(Number(newClickupTask.time_estimate)),
      };

      const bexioClient = new BexioClient();
      const bexioTask = await bexioClient.createWorkPackage(Number(newList.bexio_project_id), bexioWorkPackage);

      if (isBexioError(bexioTask)) {
        await io.logger.error(`Error creating new task in bexio: ${JSON.stringify(bexioTask)}`);
        return null;
      }

      return bexioTask;
    });

    if (newList?.bexio_archived) {
      await io.runTask(`archive-back-bexio-project-${newList.bexio_project_id}`, async () => {
        const bexioClient = new BexioClient();
        const unarchiveResponse = await bexioClient.updateProject(Number(newList.bexio_project_id), {
          pr_state_id: 3,
        });

        const supabase = createClient();
        await supabase
          .from('bexio_projects')
          .update({
            status: 'Archiviert',
            last_temp_unarchive: new Date().toISOString(),
          })
          .eq('bexio_id', Number(newList.bexio_project_id));

        if (isBexioError(unarchiveResponse)) {
          throw new Error(`Error archiving project back in bexio: ${JSON.stringify(unarchiveResponse)}`);
        }
      });
    }

    if (!newBexioTask) {
      return;
    }

    // UPDATE TIME ENTRIES IN BEXIO
    await io.runTask('create-new-time-entries-in-bexio', async () => {
      const bexioClient = new BexioClient();

      for (const timeEntry of taskData.time_entries) {
        await io.runTask(`update-time-entry-${timeEntry.bexio_timesheet_id}`, async () => {
          if (!timeEntry.bexio_timesheet_id) {
            await io.logger.log(`Skipping time entry ${timeEntry.id} - has no bexio_timesheet_id`);
            return;
          }

          const oldTimeEntry = await io.runTask(`fetch-old-time-entry-${timeEntry.bexio_timesheet_id}`, async () => {
            const oldTimeEntry = await bexioClient.getTimesheet(Number(timeEntry.bexio_timesheet_id));

            if (isBexioError(oldTimeEntry)) {
              throw new Error(`Error fetching time entry: ${JSON.stringify(oldTimeEntry)}`);
            }

            return oldTimeEntry;
          });

          const createdTimeEntry = await io.runTask(`create-new-time-entry-${timeEntry.id}`, async () => {
            if (newList?.bexio_project_id == oldTimeEntry.pr_project_id) {
              await io.logger.info(`Skipping time entry ${timeEntry.id} - has the same bexio project already`);
              return;
            }

            const bexioTimeEntry = {
              user_id: oldTimeEntry.user_id,
              client_service_id: oldTimeEntry.client_service_id,
              text: oldTimeEntry.text,
              allowable_bill: oldTimeEntry.allowable_bill,
              pr_project_id: newList?.bexio_project_id,
              pr_package_id: newBexioTask.id,
              tracking: oldTimeEntry.tracking,
            };
            await io.logger.info(`Updating time new entry: ${JSON.stringify(bexioTimeEntry)} in bexio`);

            let createdTimeEntry = await bexioClient.createTimesheet(bexioTimeEntry);
            if (isBexioError(createdTimeEntry)) {
              createdTimeEntry = await bexioClient.createTimesheet(bexioTimeEntry);
              if (isBexioError(createdTimeEntry)) {
                throw new Error(`Error creating time entry in bexio: ${JSON.stringify(createdTimeEntry)}`);
              }
            }

            return createdTimeEntry;
          });

          if (!createdTimeEntry) {
            await io.logger.info(`No time entry created for time entry ${timeEntry.id}`);
            return;
          }

          // UPDATE TIME ENTRY IN DB
          await io.runTask(`update-time-entry-in-db-${timeEntry.id}`, async () => {
            const supabase = createClient();
            const { error } = await supabase
              .from('time_entries')
              .update({ bexio_timesheet_id: createdTimeEntry.id })
              .eq('id', timeEntry.id);

            if (error) {
              await io.logger.error(`Error updating time entry in db: ${JSON.stringify(error)}`);
            }
          });

          // DELETE OLD TIME ENTRY IN BEXIO
          await io.runTask(`delete-old-time-entry-${timeEntry.bexio_timesheet_id}`, async () => {
            if (!timeEntry.bexio_timesheet_id) {
              await io.logger.log(`Skipping time entry ${timeEntry.id} - has no bexio_timesheet_id`);
              return;
            }

            const delMark = await markForDeletion('bexio_timesheet', timeEntry.bexio_timesheet_id);
            const deletedTimeEntry = await bexioClient.deleteTimesheet(timeEntry.bexio_timesheet_id);

            if (isBexioError(deletedTimeEntry) && deletedTimeEntry.error_code !== 404) {
              await io.logger.error(`Error deleting old time entry in bexio: ${JSON.stringify(deletedTimeEntry)}`);
            }

            if (delMark.error) {
              await io.logger.error(`Error marking for deletion: ${JSON.stringify(delMark)}`);
              return;
            }

            await markDeletionConfirmed(delMark.data.id);
          });
        });
      }
    });

    // UPDATE TASK IN DB
    await io.runTask('update-task-in-db', async () => {
      const supabase = createClient();
      const taskUpdate = { clickup_list_id: newList.clickup_list_id, bexio_work_package_id: newBexioTask.id };
      const { error } = await supabase.from('tasks').update(taskUpdate).eq('clickup_task_id', clickupTaskId);

      if (error) {
        await io.logger.error(`Error updating task in db: ${JSON.stringify(error)}`);
        throw new Error(`Error updating task in db: ${JSON.stringify(error)}`);
      }
    });

    // DELETE OLD TASK IN BEXIO
    await io.runTask('delete-old-task-in-bexio', async () => {
      const delMark = await markForDeletion('bexio_work_package', taskData.id);

      const bexioClient = new BexioClient();
      const deletedTask = await bexioClient.deleteWorkPackage(
        Number(taskData.projects?.bexio_project_id),
        Number(taskData.bexio_work_package_id),
      );

      if (isBexioError(deletedTask) && deletedTask.error_code !== 404) {
        throw new Error(`Error deleting old task in bexio: ${JSON.stringify(deletedTask)}`);
      }

      if (delMark.error) {
        await io.logger.error(`Error marking for deletion: ${JSON.stringify(delMark)}`);
        return;
      }

      await markDeletionConfirmed(delMark.data.id);
    });
  },
});
