import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { ClickUpClient } from '@/data/clickup-client';
import { markDeletionConfirmed, markForDeletion } from '@/jobs/shared/delete-logs';

export const taskDeletedJob = client.defineJob({
  id: 'task-deleted-job',
  name: 'ClickUp Task deleted job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { deletedTaskId } = payload;

    if (!deletedTaskId) {
      await io.logger.info(`No task id provided. Nothing to delete.`);
      return;
    }

    await io.wait('init-wait-race-cond', 10);

    const globalSettings = await io.runTask('get-global-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('global_settings').select('*').single();

      if (error) {
        throw new Error(`Error fetching global settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const taskExists = await io.runTask('check-if-task-exists', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('tasks').select('*').eq('clickup_task_id', deletedTaskId).single();

      if (error) {
        return error.code === 'PGRST116';
      }

      return !!data;
    });

    if (!taskExists) {
      await io.logger.info(`Task does not exist in db: ${deletedTaskId}. Nothing to delete.`);
      return;
    }

    // 1 GET BEXIO WORK PACKAGE ID
    const taskData = await io.runTask('get-db-task-data', async () => {
      const supabase = createClient();
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('name, bexio_work_package_id, projects(bexio_project_id)')
        .eq('clickup_task_id', deletedTaskId)
        .single();

      if (taskError) {
        throw new Error(`Error fetching task: ${JSON.stringify(taskError)}`);
      }

      return taskData;
    });

    if (!taskData) {
      await io.logger.info(`No data returned from fetching task: ${JSON.stringify(taskData)}`);
      return;
    }

    const shouldDelete = await io.runTask('should-delete-task', async () => {
      const supabase = createClient();
      const { data } = await supabase.from('time_entries').select('id').eq('clickup_task_id', deletedTaskId);
      return !data || data?.length === 0;
    });

    if (!shouldDelete) {
      await io.logger.info(`Task has time entries, not deleting: ${deletedTaskId}`);
      const clickupClient = new ClickUpClient();
      await clickupClient.createTask(String(globalSettings.pm_list_id), {
        name: `adm: Task mit Time-Entries gelöscht`,
        description: `Jemand hat einen Task "${taskData.name}" mit Time-Entries gelöscht.
Stelle diesen Task aus dem Trash wieder her: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/settings/trash`,
        assignees: [process.env.CLICKUP_ADMIN_USER_ID],
        priority: 1,
        time_estimate: 10 * 60 * 1000,
        due_date: new Date().getTime(),
      });
      return;
    }

    if (taskData.projects && taskData.projects.bexio_project_id && taskData.bexio_work_package_id) {
      // 2 REMOVE TASK FROM BEXIO
      await io.runTask('delete-bexio-work-package', async () => {
        const delMark = await markForDeletion('bexio_work_package', String(taskData.bexio_work_package_id));

        const bexioClient = new BexioClient();
        const deleteResponse = await bexioClient.deleteWorkPackage(
          Number(taskData.projects?.bexio_project_id),
          Number(taskData.bexio_work_package_id),
        );

        if (isBexioError(deleteResponse) && deleteResponse.error_code !== 404) {
          throw Error(`Error deleting work package in bexio: ${JSON.stringify(deleteResponse)}`);
        }

        if (delMark.error) {
          await io.logger.error(`Error marking for deletion: ${JSON.stringify(delMark)}`);
          return;
        }

        await markDeletionConfirmed(delMark.data.id);
      });
    }

    // 3 REMOVE TASK FROM DB
    await io.runTask('delete-task-from-db', async () => {
      const supabase = createClient();
      const { error: deleteError } = await supabase.from('tasks').delete().eq('clickup_task_id', deletedTaskId);

      if (deleteError) {
        throw new Error(`Error deleting task from db: ${JSON.stringify(deleteError)}`);
      }
    });
  },
});
