import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { taskCreatedJob, updatedClickupListUpdateBexioProjectJob } from '@/jobs';

export const taskUpdatedJob = client.defineJob({
  id: 'task-updated-job',
  name: 'ClickUp Task updated job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const { updatedTaskId } = payload;

    if (!updatedTaskId) {
      await io.logger.info(`No task id provided. Nothing to delete.`);
      return;
    }

    // 0 TIMEOUT TO CIRCUMVENT RACE CONDITIONS
    await io.wait('init-wait-race-cond', 5);

    // 1 GET TASK FROM CLICKUP
    const task = await io.runTask('fetch-clickup-task', async () => {
      const clickupClient = new ClickUpClient();
      const task = await clickupClient.getTask(updatedTaskId);

      if (isClickUpError(task)) {
        throw Error(`Error fetching task: ${JSON.stringify(task)}`);
      }

      return task;
    });

    const taskExists = await io.runTask('check-if-task-exists', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('tasks').select('*').eq('clickup_task_id', updatedTaskId).single();

      if (error) {
        return false;
      }

      return !!data;
    });

    if (!taskExists) {
      await io.logger.info(`Task does not exist yet in db: ${updatedTaskId}. Creating task.`);
      await taskCreatedJob.invokeAndWaitForCompletion('create-new-task', { createdTaskId: updatedTaskId });
      return;
    }

    const [data, oldData] = await io.runTask('update-task-in-db', async () => {
      const supabase = createClient();
      const { data: oldData, error: oldError } = await supabase
        .from('tasks')
        .select('*, projects(bexio_project_id, is_client_project)')
        .eq('clickup_task_id', updatedTaskId)
        .single();

      if (oldError) {
        throw Error(`Error fetching old task data: ${JSON.stringify(oldError)}`);
      }

      // 2 UPDATE TASK IN DB
      const userId = task.assignees[0]?.id && task.assignees[0]?.id > 0 ? task.assignees[0]?.id : task.creator?.id;
      const taskUpdate = {
        clickup_task_id: task.id,
        name: task.name,
        clickup_list_id: task.list.id,
        clickup_due_date: task.due_date ? Number(task.due_date) : null,
        clickup_start_date: task.start_date ? Number(task.start_date) : null,
        clickup_task_description: task.description,
        clickup_time_estimate: task.time_estimate,
        clickup_time_spent: task.time_spent || 0, // just recently added -> needs to be fully migrated
        clickup_user_id: userId < 0 ? null : String(userId),
        is_client_task: oldData.projects?.is_client_project || oldData.is_client_task || false,
        clickup_status: task.status.type === 'closed' ? 'Closed' : task.status.status,
        clickup_date_closed: task.date_closed ? Number(task.date_closed) : null,
        clickup_archived: task.archived,
        clickup_space_id: task.space?.id ? Number(task.space.id) : null,
        clickup_assignees: task.assignees.map((assignee) => assignee.id),
      };

      const { data, error } = await supabase
        .from('tasks')
        .upsert(taskUpdate, { onConflict: 'clickup_task_id' })
        .eq('clickup_task_id', updatedTaskId)
        .select('*, projects(clickup_name, bexio_project_id)')
        .single();

      if (error) {
        throw Error(`Error updating task in db: ${JSON.stringify(error)}`);
      }

      return [data, oldData];
    });

    if (!data) {
      await io.logger.info(`No data returned from updating task into db: ${JSON.stringify(data)}`);
      return;
    }

    if (!data.projects || !data.projects.bexio_project_id || !oldData.projects || !oldData.projects.bexio_project_id) {
      await io.logger.info(`No bexio project id found for task: ${JSON.stringify(data)}`);
      return;
    }

    // 3 UPDATE TASK IN BEXIO (only when name, comment or estimated_time_in_hours has changed)
    await io.runTask('update-task-in-bexio', async () => {
      if (!data.projects) {
        await io.logger.info(`No project found for task: ${JSON.stringify(data)}`);
        return;
      }

      if (
        data.name === oldData.name &&
        data.clickup_task_description === oldData.clickup_task_description &&
        data.clickup_time_estimate === oldData.clickup_time_estimate
      ) {
        await io.logger.info(`No meaningful changes in task: ${JSON.stringify(data)}`);
        return;
      }

      const bexioWorkPackage = {
        name: trimLength(data.name, 254), // TRIM TO 255 CHARS
        comment: trimLength(data.clickup_task_description, 999), // TRIM TO 1000 CHARS
        estimated_time_in_hours: convertMillisecondsToHours(Number(data.clickup_time_estimate)),
      };
      const bexioClient = new BexioClient();
      if (!data.bexio_work_package_id) {
        await io.logger.info(
          `No bexio work package id found for task: ${JSON.stringify(data)} - Creating new work package`,
        );
        const createdWorkPackage = await bexioClient.createWorkPackage(
          Number(data.projects?.bexio_project_id),
          bexioWorkPackage,
        );

        if (isBexioError(createdWorkPackage)) {
          throw Error(`Error updating work package in bexio: ${JSON.stringify(createdWorkPackage)}`);
        }

        // 4 UPDATE TASK WITH BEXIO ID
        const supabase = createClient();
        const { error: insertBexioWPIdError } = await supabase
          .from('tasks')
          .update({
            bexio_work_package_id: createdWorkPackage.id,
            is_client_task: true,
          })
          .eq('clickup_task_id', String(data.clickup_task_id));

        if (insertBexioWPIdError) {
          throw Error(`Error updating task with bexio work package id: ${JSON.stringify(insertBexioWPIdError)}`);
        }
        return;
      }

      const createdWorkPackage = await bexioClient.updateWorkPackage(
        Number(oldData.projects?.bexio_project_id),
        Number(data.bexio_work_package_id),
        bexioWorkPackage,
      );

      if (isBexioError(createdWorkPackage)) {
        throw Error(`Error updating work package in bexio: ${JSON.stringify(createdWorkPackage)}`);
      }
    });

    // BACKUP -> CHECK IF LIST NAME HAS CHANGED
    await io.runTask('backup-check-if-list-name-has-changed', async () => {
      const dbListName = data.projects?.clickup_name;
      if (!dbListName) {
        await io.logger.error('No dbListName found');
        return;
      }

      const clickUpListName = task.list.name;
      if (!clickUpListName) {
        await io.logger.error('No clickUpListName found');
        return;
      }

      if (dbListName !== clickUpListName) {
        await io.logger.info(`List name has changed from ${dbListName} to ${clickUpListName}`);
        await updatedClickupListUpdateBexioProjectJob.invokeAndWaitForCompletion('backup-list-name-change', {
          updatedClickupListId: task.list.id,
        });
      }
    });
  },
});
