import { createClient } from 'redis';

export async function cache<T>(key: string, fn: () => Promise<T>, ttl: number): Promise<T> {
  const redis = await createClient({
    url: process.env.REDIS_URL,
  }).connect();

  const isDev = process.env.NODE_ENV === 'development';
  const cacheKey = isDev ? 'dev-' + key : 'prod-' + key;
  const cached = await redis.get(cacheKey);
  if (cached) {
    await redis.quit();
    return JSON.parse(cached) as T;
  }

  const result = await fn();
  await redis.set(cacheKey, JSON.stringify(result));
  await redis.expire(cacheKey, ttl);
  await redis.quit();
  return result;
}
