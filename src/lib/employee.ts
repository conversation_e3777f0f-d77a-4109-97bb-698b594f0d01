import 'server-only';

import { cache } from '@/lib/cache';
import { createClient } from '@/data/supabase-server';
import { PermissionSelection, permissionsOptions } from '@/app/employees/[id]/_components/permissionsOptions';
import { headers } from 'next/headers';

const getEmployeeCached = (userId: string) =>
  cache(
    `employee-info-${userId}`,
    async () => {
      const supabase = createClient();
      const { data } = await supabase.from('employees').select('*').eq('user_id', userId).single();
      return data;
    },
    0,
  );

export async function getPermissions(userId: string): Promise<PermissionSelection[]> {
  const employee = await getEmployeeCached(userId);
  if (!employee || !employee.access) {
    return permissionsOptions.map((option) => ({ pageKey: option.pageKey, option: 'inactive' }));
  }

  return employee.access as PermissionSelection[];
}

export async function getUserId() {
  const headersList = headers();
  const user = headersList.get('x-user-id');
  return String(user);
}

export async function getPermission(pageKey: string) {
  const userId = await getUserId();
  const permissions = await getPermissions(userId);
  return permissions.find((p) => p.pageKey === pageKey)?.option || 'inactive';
}
