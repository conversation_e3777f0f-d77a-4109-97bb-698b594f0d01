import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';
import { jwtDecode } from 'jwt-decode';
import { createClient } from '@/data/supabase-server';

export async function updateSession(request: NextRequest, token: string | undefined) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    },
  );

  await supabase.auth.getUser();

  if (request.nextUrl.pathname.includes('api') || !token) {
    return response;
  }

  const decoded = jwtDecode(token);
  if ('email' in decoded) {
    const supabase = createClient();
    const email = String(decoded.email).toLowerCase();
    const { data, error } = await supabase.from('employees').select('*').eq('email', email).single();
    if (!error) {
      response.headers.append('x-user-id', data.user_id);
      response.headers.append('x-user-email', data.email.toLowerCase());
      response.headers.append('x-user-name', String(data.name));
      response.headers.append('x-user-image', String(data.profile_picture_url));
      response.headers.append('x-access', JSON.stringify(data.access));
    }
  }

  return response;
}
