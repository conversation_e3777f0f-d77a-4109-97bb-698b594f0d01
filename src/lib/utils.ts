import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format } from 'date-fns';
import convert from 'cron-timezone-converter';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function trimLength(input: any, length: number) {
  if (!input) {
    return '';
  }

  if (input.length > length) {
    return input.slice(0, length);
  }
  return input;
}

export function convertMillisecondsToHours(milliseconds: number | undefined | null) {
  // check if milliseconds is NaN
  if (!milliseconds || isNaN(milliseconds)) {
    return 0;
  }

  return (milliseconds / 1000 / 60 / 60).toFixed(2);
}

export function prettyPrintTime(milliseconds: number | undefined | null) {
  if (!milliseconds) {
    return '-';
  }

  const hours = Math.floor(milliseconds / 1000 / 60 / 60);
  const minutes = Math.floor((milliseconds / 1000 / 60) % 60);
  const seconds = Math.floor((milliseconds / 1000) % 60);

  return `${hours}h ${minutes}m ${seconds}s`;
}

export function convertUNIXTimestampToDateTimeString(unix: number | undefined | null) {
  if (!unix) {
    return '-';
  }
  // should use local timezone
  return format(new Date(unix), 'yyyy-MM-dd HH:mm:ss');
}

export function convertUNIXTimestampToDateString(unix: number | undefined | null) {
  if (!unix) {
    return '-';
  }
  // should use local timezone
  return format(new Date(unix), 'yyyy-MM-dd');
}

export function shallowEqualIgnoreProperties(object1: any, object2: any, ignoredProperties: string[] = []) {
  const keys1 = Object.keys(object1).filter((key) => !ignoredProperties.includes(key));
  const keys2 = Object.keys(object2).filter((key) => !ignoredProperties.includes(key));

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (let key of keys1) {
    if (object1[key] != object2[key]) {
      return false;
    }
  }

  return true;
}

// P-1234-123 Project Name
export function stripProjectPrefix(projectName: string) {
  return projectName?.replace(/P-\d+-\d+ /, '');
}

export function buildDropboxProjectFolderPath(
  project: { clickup_name: string | null; clickup_archived: boolean },
  client: { name: string; clickup_archived: boolean },
  deleted: boolean = false,
) {
  const prefix = process.env.DROPBOX_FOLDER_PREFIX;
  const clientFolderName = client.clickup_archived
    ? `zArchiv/${cleanProjectName(client.name)}`
    : cleanProjectName(client.name);
  const deletedInsert = deleted ? 'zDeleted/' : '';
  const projectFolderName = project.clickup_archived
    ? `zArchiv/${cleanProjectName(project.clickup_name)}`
    : cleanProjectName(project.clickup_name);
  return `${prefix}/${clientFolderName}/${deletedInsert}${projectFolderName}`;
}

export function buildDropboxClientFolderPath(client: { name: string; clickup_archived: boolean }) {
  const prefix = process.env.DROPBOX_FOLDER_PREFIX;
  const clientFolderName = client.clickup_archived
    ? `zArchiv/${cleanProjectName(client.name)}`
    : cleanProjectName(client.name);
  return `${prefix}/${clientFolderName}`;
}

// replace "/" with "-"
export function cleanProjectName(projectName: string | null) {
  if (!projectName) {
    return '';
  }

  return projectName.replace(/\//g, '-');
}

export // Helper function to get the first day of the week (Monday)
function getFirstDayOfWeek(d: Date) {
  const date = new Date(d);
  const day = date.getDay(); // Get current day of the week (0 is Sunday, 1 is Monday)
  const diff = day === 0 ? -6 : 1 - day; // If it's Sunday, go back 6 days. Otherwise, go to the previous Monday
  return new Date(date.setDate(date.getDate() + diff));
}

// Helper function to get the last day of the week (Sunday)
export function getLastDayOfWeek(d: Date) {
  const date = new Date(d);
  const day = date.getDay(); // Get current day of the week (0 is Sunday)
  const diff = day === 0 ? 0 : 7 - day; // If it's Sunday, no adjustment needed. Otherwise, calculate the next Sunday
  return new Date(date.setDate(date.getDate() + diff));
}

export function prettyPrintNumber(n: number | string | undefined | null) {
  if (!n) {
    return '-';
  }

  if (typeof n === 'string') {
    if (n === '-0.00') {
      return '0';
    }

    return n;
  }

  return n.toFixed(2);
}

export function prettyPrintCurrency(n: number | string | undefined | null | unknown): string {
  if (!n) {
    return '-';
  }

  const formattedStr = Number(n).toLocaleString('de-CH', {
    style: 'currency',
    currency: 'CHF',
  });

  if (Number(n) < 0) {
    return formattedStr.replace('-', ' -');
  }

  return formattedStr;
}

export function prettyPrintPercentage(n: number | string | undefined | null | unknown) {
  if (n === undefined || n === null) {
    return '-';
  }

  if (isNaN(Number(n)) || !isFinite(Number(n))) {
    return '-';
  }

  return `${Number(n).toFixed(2)} %`;
}

// 2024-01-01
export function getISODateString(date: Date | undefined | null) {
  if (!date) return '-';
  const pad = (n: number) => (n < 10 ? '0' : '') + n;
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;
}

export function prettyPrintTsToTime(ts: number | Date) {
  return new Date(ts).toLocaleString('de', {
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
  });
}

export function prettyPrintTsToDate(ts: number | Date | undefined | null) {
  if (!ts) return '-';

  return new Date(ts).toLocaleString('de', {
    weekday: 'long',
    day: 'numeric',
    month: 'numeric',
    year: 'numeric',
  });
}

export function prettyPrintDateShort(ts: number | Date | undefined | null) {
  if (!ts) return '-';

  return new Date(ts).toLocaleString('de', {
    day: 'numeric',
    month: 'numeric',
    year: 'numeric',
  });
}

export function msToHours(ms?: number | null) {
  if (!ms) {
    return 0;
  }

  return ms / 1000 / 60 / 60;
}

export function isGenericError(error?: any): error is { error: any } {
  return error && 'error' in error;
}

export function convertCronToUTC(cron: string) {
  return convert(cron, 'Europe/Zurich', 'UTC', false);
}

export function areRotatedNamesEqual(name1: string, name2: string | null | undefined): boolean {
  if (name1 === name2) return true;
  if (!name2) return false;
  if (name1.length !== name2.length) return false;

  const words1 = name1.toLowerCase().trim().split(/\s+/);
  const words2 = name2.toLowerCase().trim().split(/\s+/);

  const len = words1.length;
  // Check each possible rotation
  for (let i = 0; i < len; i++) {
    let isMatch = true;
    // Compare each word in the current rotation
    for (let j = 0; j < len; j++) {
      if (words1[(i + j) % len] !== words2[j]) {
        isMatch = false;
        break;
      }
    }
    if (isMatch) return true;
  }
  return false;
}
