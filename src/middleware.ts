import { NextRequest } from 'next/server';
import { updateSession } from '@/lib/middleware/supabase';
import { PostHog } from 'posthog-node';

process.env.TZ = 'Europe/Zurich';
const posthog = new PostHog(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
  host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
});

export async function middleware(request: NextRequest) {
  const start = Date.now();
  const isDev = process.env.NODE_ENV === 'development';
  const isVercel = process.env.VERCEL === '1';
  const cfToken = request.cookies.get('CF_Authorization');
  const token = isDev ? process.env.DEV_JWT_TOKEN : cfToken?.value;
  let response = await updateSession(request, token);
  const end = Date.now();
  console.info(`${request.method} ${request.nextUrl.pathname} | ${end - start}ms`);

  // skip if healthcheck
  if (request.nextUrl.pathname.includes('healthcheck') || request.nextUrl.pathname.includes('monitor')) {
    return response;
  }

  if (!(process.env.NODE_ENV === 'development')) {
    posthog.capture({
      distinctId: isDev ? 'dev-middleware' : isVercel ? 'vercel-middleware' : 'middleware',
      event: 'Middleware Invocation',
      properties: { url: request.nextUrl.pathname, isDev, duration: end - start },
    });
    await posthog.shutdown();
  }
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
