'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getClientRevenueComparison(
  firstStart: string,
  firstEnd: string,
  secondStart: string,
  secondEnd: string,
) {
  const firstRangeRevenue = await getClientsRevenue(firstStart, firstEnd);
  const secondRangeRevenue = await getClientsRevenue(secondStart, secondEnd);

  if ('error' in firstRangeRevenue) {
    return { error: firstRangeRevenue.error };
  }

  if ('error' in secondRangeRevenue) {
    return { error: secondRangeRevenue.error };
  }

  const aggComparison = Array.from(firstRangeRevenue.entries()).reduce((acc, [client, firstRevenues]) => {
    const secondRevenue = secondRangeRevenue.get(client)?.revenue || 0;
    if (firstRevenues.revenue === 0 && secondRevenue === 0) {
      return acc;
    }

    const diff = secondRevenue - firstRevenues.revenue;
    const diffPercent = (diff / firstRevenues.revenue) * 100;
    return acc.set(client, { firstRevenue: firstRevenues.revenue, secondRevenue: secondRevenue, diff, diffPercent });
  }, new Map<string, { firstRevenue: number; secondRevenue: number; diff: number; diffPercent: number }>());

  const servicesComparison = Array.from(firstRangeRevenue.entries()).reduce((acc, [client, firstRevenues]) => {
    const secondRevenue = secondRangeRevenue.get(client)?.revenue || 0;
    const secondServicesAmount = secondRangeRevenue.get(client)?.servicesAmount || 0;
    if (firstRevenues.revenue === 0 && secondRevenue === 0) {
      return acc;
    }

    const diff = secondServicesAmount - firstRevenues.servicesAmount;
    const diffPercent = (diff / firstRevenues.servicesAmount) * 100;
    return acc.set(client, {
      firstRevenue: firstRevenues.servicesAmount,
      secondRevenue: secondServicesAmount,
      diff,
      diffPercent,
    });
  }, new Map<string, { firstRevenue: number; secondRevenue: number; diff: number; diffPercent: number }>());

  const tradesComparison = Array.from(firstRangeRevenue.entries()).reduce((acc, [client, firstRevenues]) => {
    const secondRevenue = secondRangeRevenue.get(client)?.revenue || 0;
    const secondTradesAmount = secondRangeRevenue.get(client)?.tradesAmount || 0;
    if (firstRevenues.revenue === 0 && secondRevenue === 0) {
      return acc;
    }

    const diff = secondTradesAmount - firstRevenues.tradesAmount;
    const diffPercent = (diff / firstRevenues.tradesAmount) * 100;
    return acc.set(client, {
      firstRevenue: firstRevenues.tradesAmount,
      secondRevenue: secondTradesAmount,
      diff,
      diffPercent,
    });
  }, new Map<string, { firstRevenue: number; secondRevenue: number; diff: number; diffPercent: number }>());

  return {
    data: {
      revenueComparison: Array.from(aggComparison.entries()).map(([client, metrics]) => ({
        client,
        ...metrics,
      })),

      servicesComparison: Array.from(servicesComparison.entries()).map(([client, metrics]) => ({
        client,
        ...metrics,
      })),

      tradesComparison: Array.from(tradesComparison.entries()).map(([client, metrics]) => ({
        client,
        ...metrics,
      })),
    },
  };
}
