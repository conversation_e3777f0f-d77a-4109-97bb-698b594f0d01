'use server';

import { createClient } from '@/data/supabase-server';

export async function getClientsRevenue(start?: string, end?: string) {
  const supabase = createClient();
  const { data: clients, error } = await supabase.from('clients').select('*');

  if (error) {
    return { error: error.message };
  }

  const { data: invoices, error: invoicesError } = await supabase.from('bexio_invoices').select('*');
  if (invoicesError) {
    return { error: invoicesError.message };
  }

  const startDate = new Date(start || '2022-01-01');
  const endDate = new Date(end || new Date());
  const defaultMap = new Map(clients.map((c) => [c.name, { revenue: 0, servicesAmount: 0, tradesAmount: 0 }]));
  return invoices.reduce((acc, invoice) => {
    const client = clients.find((client) => client.bexio_contact_id === invoice.contact_id);
    if (!client) {
      return acc;
    }

    if (invoice.status !== 'paid') {
      return acc;
    }

    const invoiceDate = new Date(invoice.valid_from!);
    if (invoiceDate < startDate || invoiceDate > endDate) {
      return acc;
    }

    const prevRev = acc.get(client.name)?.revenue || 0;
    const prevServicesAmount = acc.get(client.name)?.servicesAmount || 0;
    const prevTradesAmount = acc.get(client.name)?.tradesAmount || 0;
    return acc.set(client.name, {
      revenue: prevRev + invoice.total_net,
      servicesAmount: prevServicesAmount + (invoice.services_amount || 0),
      tradesAmount: prevTradesAmount + (invoice.trades_amount || 0),
    });
  }, defaultMap);
}
