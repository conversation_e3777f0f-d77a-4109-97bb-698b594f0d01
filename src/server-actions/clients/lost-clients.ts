'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getLostClients(lostSinceDate: string) {
  const revenuesBefore = await getClientsRevenue(undefined, lostSinceDate);
  if ('error' in revenuesBefore) {
    return { error: revenuesBefore.error };
  }
  const revenuesAfter = await getClientsRevenue(lostSinceDate, new Date().toISOString());
  if ('error' in revenuesAfter) {
    return { error: revenuesAfter.error };
  }
  const revenuesTotal = await getClientsRevenue();
  if ('error' in revenuesTotal) {
    return { error: revenuesTotal.error };
  }

  const lostClients = Array.from(revenuesBefore.entries()).reduce((acc, [client, revenues]) => {
    const revenueAfter = revenuesAfter.get(client)?.revenue || 0;
    if (revenueAfter === 0 && revenues.revenue > 0) {
      const totalRevenue = revenuesTotal.get(client);
      acc.set(client, {
        revenue: totalRevenue?.revenue || 0,
        servicesAmount: totalRevenue?.servicesAmount || 0,
        tradesAmount: totalRevenue?.tradesAmount || 0,
      });
    }

    return acc;
  }, new Map<string, { revenue: number; servicesAmount: number; tradesAmount: number }>());

  return {
    data: Array.from(lostClients.entries()).map(([client, revenue]) => ({
      client,
      ...revenue,
    })),
  };
}
