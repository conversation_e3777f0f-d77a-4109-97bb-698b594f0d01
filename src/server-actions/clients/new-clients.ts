'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getNewClients(start: string, end: string) {
  const revenueTimeRange = await getClientsRevenue(start, end);

  if ('error' in revenueTimeRange) {
    return { error: revenueTimeRange.error };
  }

  const revenueBeforeTimeRange = await getClientsRevenue('2022-01-01', start);
  if ('error' in revenueBeforeTimeRange) {
    return { error: revenueBeforeTimeRange.error };
  }

  // Clients that have revenue in the time range but not before
  const newClients = Array.from(revenueTimeRange.entries())
    .filter(([client, revenues]) => {
      const revenueBefore = revenueBeforeTimeRange.get(client)?.revenue || 0;
      return revenueBefore === 0 && revenues.revenue >= 0;
    })
    .map(([client, revenues]) => ({
      client,
      ...revenues,
    }));

  return {
    data: newClients,
  };
}
