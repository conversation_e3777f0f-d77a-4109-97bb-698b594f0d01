'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getNoClients() {
  const revenueData = await getClientsRevenue();

  if ('error' in revenueData) {
    return { error: revenueData.error };
  }

  console.log(revenueData);

  const clientsWithoutRevenue = Array.from(revenueData.entries()).filter(([_, rev]) => rev.revenue === 0);

  return {
    data: clientsWithoutRevenue,
  };
}
