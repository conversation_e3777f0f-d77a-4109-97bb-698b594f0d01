'use server';

import { createClient } from '@/data/supabase-server';
import { Tables } from '@/types/gen/database-table';
import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getProfitableClients(start: string, end: string, reverse: boolean = false) {
  const clientsRevenue = await getClientsRevenue(start, end);

  if ('error' in clientsRevenue) {
    return { error: clientsRevenue.error };
  }

  const supabase = createClient();
  const { data: bexioProjects, error } = await supabase.from('bexio_projects').select('*');

  if (error) {
    return { error: error.message };
  }

  const bexioProjectsByClient = bexioProjects.reduce((acc, bexioProject) => {
    if (!bexioProject.client_name) {
      return acc;
    }

    const client = acc.get(bexioProject.client_name) || [];
    return acc.set(bexioProject.client_name, [...client, bexioProject]);
  }, new Map<string, Tables<'bexio_projects'>[]>());

  const startDate = new Date(start);
  const endDate = new Date(end);
  const profitableClientMetrics = Array.from(bexioProjectsByClient.entries()).reduce(
    (acc, [clientName, bexioProjects]) => {
      const qualifyingProjects = bexioProjects.filter((bexioProject) => {
        const projectEnd = bexioProject.close_date || bexioProject.end_date || bexioProject.start_date;
        if (!projectEnd) {
          return false;
        }
        const projectEndDate = new Date(projectEnd);
        if (projectEndDate < startDate || projectEndDate > endDate) {
          return false;
        }

        return bexioProject.status === 'Archiviert' && Number(bexioProject.budget) > 0;
      });

      const totalDiffEffCost = qualifyingProjects.reduce((acc, bexioProject) => {
        return acc + Number(bexioProject.difference_budget);
      }, 0);

      if ((!reverse && totalDiffEffCost <= 0) || (reverse && totalDiffEffCost >= 0)) {
        return acc;
      }

      const negDiffEffCost = qualifyingProjects.reduce((acc, bexioProject) => {
        if (Number(bexioProject.difference_budget) < 0) {
          return acc + Number(bexioProject.difference_budget);
        }
        return acc;
      }, 0);

      const posDiffEffCost = qualifyingProjects.reduce((acc, bexioProject) => {
        if (Number(bexioProject.difference_budget) > 0) {
          return acc + Number(bexioProject.difference_budget);
        }
        return acc;
      }, 0);

      const avgPercentageDiffEffCost =
        qualifyingProjects.reduce((acc, bexioProject) => {
          return acc + Number(bexioProject.difference_budget_percentage);
        }, 0) / qualifyingProjects.length;

      return acc.set(clientName, {
        totalDiffEffCost,
        avgPercentageDiffEffCost,
        posDiffEffCost,
        negDiffEffCost,
        revenue: clientsRevenue.get(clientName)?.revenue || 0,
        servicesAmount: clientsRevenue.get(clientName)?.servicesAmount || 0,
        tradesAmount: clientsRevenue.get(clientName)?.tradesAmount || 0,
      });
    },
    new Map<
      string,
      {
        totalDiffEffCost: number;
        negDiffEffCost: number;
        posDiffEffCost: number;
        avgPercentageDiffEffCost: number;
        revenue: number;
        servicesAmount: number;
        tradesAmount: number;
      }
    >(),
  );

  console.log('profitableClientMetrics', profitableClientMetrics);

  const result = Array.from(profitableClientMetrics.entries())
    .sort((a, b) => {
      if (reverse) {
        return a[1].totalDiffEffCost - b[1].totalDiffEffCost;
      }
      return b[1].totalDiffEffCost - a[1].totalDiffEffCost;
    })
    .map(([client, metrics]) => {
      return {
        ...metrics,
        client: client,
      };
    });
  return { data: result };
}
