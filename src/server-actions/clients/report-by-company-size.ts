'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';
import { createClient } from '@/data/supabase-server';

const companySizeOptions = [
  {
    label: '1-3',
    min: 1,
    max: 3,
  },
  {
    label: '4-10',
    min: 4,
    max: 10,
  },
  {
    label: '11-20',
    min: 11,
    max: 20,
  },
  {
    label: '21-40',
    min: 21,
    max: 40,
  },
  {
    label: '41-80',
    min: 41,
    max: 80,
  },
  {
    label: '81-150',
    min: 81,
    max: 150,
  },
  {
    label: '151-250',
    min: 151,
    max: 250,
  },
  {
    label: '250+',
    min: 250,
    max: Number.MAX_VALUE,
  },
];

export async function getReportByCompanySize(start: string, end: string) {
  const revenue = await getClientsRevenue(start, end);
  const startDate = new Date(start || '2022-01-01');
  const endDate = new Date(end || new Date());

  if ('error' in revenue) {
    return { error: revenue.error };
  }

  const supabase = createClient();
  const { data: clients, error: clientsError } = await supabase.from('clients').select('*');

  if (clientsError) {
    return { error: clientsError.message };
  }

  const { data: bexioProjects, error: bexioClientsError } = await supabase.from('bexio_projects').select('*');

  if (bexioClientsError) {
    return { error: bexioClientsError.message };
  }

  const totalRevenue = Array.from(revenue).reduce((acc, revenures) => {
    return acc + (revenures[1].revenue || 0);
  }, 0);

  const clientsByCompanySize = companySizeOptions.reduce((acc, companySize) => {
    const companySizeClients = clients
      .filter(
        (client) =>
          client.num_employees && client.num_employees >= companySize.min && client.num_employees <= companySize.max,
      )
      .filter((client) => revenue.get(client.name));
    const companySizeRevenue = companySizeClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.revenue || 0);
    }, 0);
    const companySizeTradesAmount = companySizeClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.tradesAmount || 0);
    }, 0);
    const companySizeServicesAmount = companySizeClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.servicesAmount || 0);
    }, 0);

    const companySizeClientNames = companySizeClients.map((client) => client.name);
    const companySizeProjects = bexioProjects.filter((project) =>
      companySizeClientNames.includes(String(project.client_name)),
    );

    const companySizeBudgetStatistics = companySizeProjects.reduce(
      (acc, project) => {
        if (project.status !== 'Archiviert' || !project.budget) {
          return acc;
        }

        const projectsEnd = project.close_date || project.end_date || project.start_date;
        if (!projectsEnd || new Date(projectsEnd) < startDate || new Date(projectsEnd) > endDate) {
          return acc;
        }

        const percentageSum = acc.percentageSum + (project.difference_budget_percentage || 0);
        const percentageCount = acc.percentageCount + (project.difference_budget_percentage ? 1 : 0);
        const budgetDiffSum = acc.budgetDiffSum + (project.difference_budget || 0);
        return {
          percentageSum,
          percentageCount,
          budgetDiffSum,
        };
      },
      { percentageSum: 0, percentageCount: 0, budgetDiffSum: 0 },
    );

    acc.set(companySize.label, {
      revenue: companySizeRevenue,
      percentage: (companySizeRevenue / totalRevenue) * 100,
      revenueByClient: companySizeRevenue / companySizeClients.length,
      numClients: companySizeClients.length,
      avgPercentage: companySizeBudgetStatistics.percentageSum / companySizeBudgetStatistics.percentageCount,
      totalBudgetDiff: companySizeBudgetStatistics.budgetDiffSum,
      servicesAmount: companySizeServicesAmount,
      tradesAmount: companySizeTradesAmount,
    });
    return acc;
  }, new Map<string, { revenue: number; percentage: number; revenueByClient: number; numClients: number; avgPercentage: number; totalBudgetDiff: number; servicesAmount: number; tradesAmount: number }>());

  return {
    data: Array.from(clientsByCompanySize).map(([size, metrics]) => ({
      size,
      ...metrics,
    })),
  };
}
