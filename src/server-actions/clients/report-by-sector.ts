'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';
import { createClient } from '@/data/supabase-server';

export async function getReportBySector(start: string, end: string) {
  const revenue = await getClientsRevenue(start, end);
  const startDate = new Date(start || '2022-01-01');
  const endDate = new Date(end || new Date());

  if ('error' in revenue) {
    return { error: revenue.error };
  }

  const supabase = createClient();
  const { data: sectors, error: sectorsError } = await supabase.from('bexio_contact_sectors').select('*');

  if (sectorsError) {
    return { error: sectorsError.message };
  }

  const { data: clients, error: clientsError } = await supabase.from('clients').select('*');

  if (clientsError) {
    return { error: clientsError.message };
  }

  const { data: bexioProjects, error: bexioClientsError } = await supabase.from('bexio_projects').select('*');

  if (bexioClientsError) {
    return { error: bexioClientsError.message };
  }

  const totalRevenue = Array.from(revenue).reduce((acc, revenures) => {
    return acc + (revenures[1].revenue || 0);
  }, 0);

  const clientsBySector = sectors.reduce((acc, sector) => {
    const sectorClients = clients
      .filter((client) => client.bexio_sector_ids.includes(sector.id))
      .filter((client) => revenue.get(client.name));
    const sectorRevenue = sectorClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.revenue || 0);
    }, 0);

    const sectorTradesAmount = sectorClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.tradesAmount || 0);
    }, 0);

    const sectorServicesAmount = sectorClients.reduce((acc, client) => {
      return acc + (revenue.get(client.name)?.servicesAmount || 0);
    }, 0);

    const sectorClientNames = sectorClients.map((client) => client.name);
    const sectorProjects = bexioProjects.filter((project) => sectorClientNames.includes(String(project.client_name)));

    const sectorBudgetStatistics = sectorProjects.reduce(
      (acc, project) => {
        if (project.status !== 'Archiviert' || !project.budget) {
          return acc;
        }

        const projectsEnd = project.close_date || project.end_date || project.start_date;
        if (!projectsEnd || new Date(projectsEnd) < startDate || new Date(projectsEnd) > endDate) {
          return acc;
        }

        const percentageSum = acc.percentageSum + (project.difference_budget_percentage || 0);
        const percentageCount = acc.percentageCount + (project.difference_budget_percentage ? 1 : 0);
        const budgetDiffSum = acc.budgetDiffSum + (project.difference_budget || 0);
        return {
          percentageSum,
          percentageCount,
          budgetDiffSum,
        };
      },
      { percentageSum: 0, percentageCount: 0, budgetDiffSum: 0 },
    );

    acc.set(sector.name, {
      revenue: sectorRevenue,
      percentage: (sectorRevenue / totalRevenue) * 100,
      revenueByClient: sectorRevenue / sectorClients.length,
      numClients: sectorClients.length,
      avgPercentage: sectorBudgetStatistics.percentageSum / sectorBudgetStatistics.percentageCount,
      totalBudgetDiff: sectorBudgetStatistics.budgetDiffSum,
      servicesAmount: sectorServicesAmount,
      tradesAmount: sectorTradesAmount,
    });
    return acc;
  }, new Map<string, { revenue: number; percentage: number; revenueByClient: number; numClients: number; avgPercentage: number; totalBudgetDiff: number; servicesAmount: number; tradesAmount: number }>());

  return { data: Array.from(clientsBySector).map(([sector, metrics]) => ({ sector, ...metrics })) };
}
