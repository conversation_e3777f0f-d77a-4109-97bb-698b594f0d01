'use server';

import { getClientsRevenue } from '@/server-actions/clients/clients-revenue';

export async function getTop20ClientsByRevenue(start: string, end: string) {
  const revenueByClient = await getClientsRevenue(start, end);

  if ('error' in revenueByClient) {
    return { error: revenueByClient.error };
  }

  const top20Clients = Array.from(revenueByClient.entries())
    .sort((a, b) => b[1].servicesAmount - a[1].servicesAmount)
    .slice(0, 20)
    .map(([client, metrics]) => {
      return {
        client,
        ...metrics,
      };
    });
  return { data: top20Clients };
}
