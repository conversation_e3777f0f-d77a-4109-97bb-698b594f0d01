'use server';

import { checkNewBexioContactsJob, checkProjectClientOwnershipDifferencesJob } from '@/jobs';

export async function checkNexBexioContacts() {
  return await checkNewBexioContactsJob.invoke({
    ts: new Date(),
    lastTimestamp: new Date(),
  });
}

export async function checkProjectClientOwnershipDifferences() {
  return await checkProjectClientOwnershipDifferencesJob.invoke({
    ts: new Date(),
    lastTimestamp: new Date(),
  });
}
