'use server';

import { createClient } from '@/data/supabase-server';

export async function getDomainClients() {
  const supabase = createClient();
  const { data: domainClients, error } = await supabase.from('clients').select('*');

  if (error) {
    return { error };
  }

  const { data: bexioProjects, error: bexioError } = await supabase.from('bexio_projects').select('*');

  if (bexioError) {
    return { error: bexioError };
  }

  const domainClientsWithProjects = domainClients.map((client) => {
    const projects = bexioProjects.filter((project) => project.client_name === client.name);
    const servicesAmount = projects.reduce((acc, project) => acc + Number(project.services_amount || 0), 0);

    return {
      ...client,
      services_amount: servicesAmount,
    };
  });

  return { data: domainClientsWithProjects };
}
