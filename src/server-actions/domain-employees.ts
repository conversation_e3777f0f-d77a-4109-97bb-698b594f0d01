'use server';

import { createClient } from '@/data/supabase-server';
import { revalidatePath } from 'next/cache';

export async function getDomainEmployees(excludeInactive?: boolean, includeTimeDashboard?: boolean) {
  const supabase = createClient();
  const query = supabase.from('employees').select('*').order('clickup_user_id', { ascending: true });
  if (excludeInactive && !includeTimeDashboard) {
    query.eq('inactive', false);
  }

  if (includeTimeDashboard) {
    query.eq('active_time_report', true);
  }
  return query;
}

export async function activateEmployee(clickupUserId: string) {
  const supabase = createClient();
  revalidatePath('/working-hours');
  revalidatePath('/time-reporting');
  return supabase
    .from('employees')
    .update({ inactive: false, active_time_report: true })
    .eq('clickup_user_id', clickupUserId);
}

export async function deactivateEmployee(clickupUserId: string) {
  const supabase = createClient();
  revalidatePath('/working-hours');
  revalidatePath('/time-reporting');
  return supabase
    .from('employees')
    .update({ inactive: true, active_time_report: false })
    .eq('clickup_user_id', clickupUserId);
}

export async function activateEmployeeTimeReport(clickupUserId: string) {
  const supabase = createClient();
  return supabase.from('employees').update({ active_time_report: true }).eq('clickup_user_id', clickupUserId);
}

export async function deactivateEmployeeTimeReport(clickupUserId: string) {
  const supabase = createClient();
  return supabase.from('employees').update({ active_time_report: false }).eq('clickup_user_id', clickupUserId);
}

export async function saveEmployeeStartEndTimes(clickupUserId: string, payload: any) {
  const supabase = createClient();
  return supabase.from('employees').update(payload).eq('clickup_user_id', clickupUserId);
}
