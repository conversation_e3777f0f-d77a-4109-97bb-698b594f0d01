'use server';

import { createClient } from '@/data/supabase-server';
import { Database } from '@/types/gen/database.types';

type EmployeeCapacitySettings = Database['public']['Tables']['employee_capacity_settings']['Row'] & {
  employees: { name: string } | null;
};

export async function getEmployeeCapacitySettings() {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('employee_capacity_settings')
      .select('*, employees(name)')
      .order('employees(name)');

    if (error) {
      console.error('Error fetching employee capacity settings:', error);
      return { data: null, error: error.message };
    }

    return { data: data as EmployeeCapacitySettings[], error: null };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { data: null, error: 'An unexpected error occurred' };
  }
}

export async function getAllEmployees() {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('employees')
      .select('user_id, name')
      .eq('inactive', false)
      .order('name');

    if (error) {
      console.error('Error fetching employees:', error);
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { data: null, error: 'An unexpected error occurred' };
  }
}

export async function saveEmployeeCapacitySettings(
  settings: Array<{
    id: string;
    overcapacity_day_with_target_hours_h: number;
    overcapacity_day_without_target_hours_h: number;
    overcapacity_week_h: number;
    warning_day_active: boolean;
    warning_week_active: boolean;
    overcapacity_day_with_target_hours_and_pto_vacation_h: number;
    overcapacity_day_with_target_hours_and_pto_sick_h: number;
    overcapacity_day_with_target_hours_and_pto_holiday_h: number;
    overcapacity_day_with_target_hours_and_pto_freetime_h: number;
    overcapacity_day_with_target_hours_and_pto_compensation_h: number;
    overcapacity_day_with_target_hours_and_pto_training_h: number;
    overcapacity_day_with_target_hours_and_pto_military_cs_h: number;
    overcapacity_day_with_target_hours_and_pto_parental_leave_h: number;
    overcapacity_day_with_target_hours_and_pto_accident_h: number;
    overcapacity_day_with_target_hours_and_pto_other_h: number;
  }>
) {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('employee_capacity_settings')
      .upsert(settings, { onConflict: 'id' });

    if (error) {
      console.error('Error saving employee capacity settings:', error);
      return { error: error.message };
    }

    return { error: null };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { error: 'An unexpected error occurred' };
  }
}