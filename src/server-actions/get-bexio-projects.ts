'use server';

import { BexioClient } from '@/data/bexio-clients';
import { supabaseServiceClient } from '@/data/supabase-server';
import { isBexioError } from '@/data/types/bexio.types';

export async function getBexioUsers() {
  const bc = new BexioClient();
  return await bc.getUsers();
}

export async function mergeBexioUsers() {
  const users = await getBexioUsers();
  if (isBexioError(users)) return { error: users };

  const usersUpsert = users.map((user) => {
    return {
      bexio_user_id: user.id,
      email: user.email,
      name: user.firstname + ' ' + user.lastname,
    };
  });

  const { error } = await supabaseServiceClient().from('users').upsert(usersUpsert, { onConflict: 'email' });
  console.log(error);
}
