'use server';

import { createClient } from '@/data/supabase-server';
import { revalidatePath } from 'next/cache';

export async function getGlobalSettings() {
  const supabase = createClient();
  return supabase.from('global_settings').select('*').eq('id', 1).single();
}

export async function saveNewFalseTimeEntriesListIdAction(formData: FormData) {
  const newFalseTimeentriesListId = String(formData.get('false-timeentries-list-id'));
  const newPtoCalendarListId = String(formData.get('pto-calendar-list-id'));
  const newPmListId = String(formData.get('pm-list-id'));
  const newHourlyRate = Number(formData.get('hourly-rate'));
  const supabase = createClient();
  const response = await supabase.from('global_settings').upsert(
    {
      id: 1,
      false_timeentries_list_id: newFalseTimeentriesListId,
      pto_list_id: newPtoCalendarListId,
      pm_list_id: newPmListId,
      general_hourly_rate: newHourlyRate,
    },
    { onConflict: 'id' },
  );
  revalidatePath('/settings');
  return response;
}
