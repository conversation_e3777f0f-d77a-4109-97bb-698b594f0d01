'use server';

import { supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { ClickUpClient } from '@/data/clickup-client';

export async function getTimeLabels() {
  return supabaseServiceClient().from('business_activities').select('*');
}

export async function getBexioBusinessActivities() {
  if (process.env.NODE_ENV === 'development') {
    return [
      {
        id: 1,
        name: 'Eins',
        default_is_billable: true,
        default_price_per_hour: 145,
        account_id: 1,
      },
      {
        id: 3,
        name: '<PERSON>ei',
        default_is_billable: true,
        default_price_per_hour: 145,
        account_id: 1,
      },
      {
        id: 4,
        name: 'Vier',
        default_is_billable: true,
        default_price_per_hour: 145,
        account_id: 1,
      },
      {
        id: 5,
        name: 'Fünf',
        default_is_billable: true,
        default_price_per_hour: 145,
        account_id: 1,
      },
    ];
  }

  const bexioClient = new BexioClient();
  return bexioClient.getBusinessActivities();
}

export async function getClickUpTimeEntriesTags() {
  const clickupClient = new ClickUpClient();
  return clickupClient.getTimeEntriesTags();
}

export async function updateTimeLabels(timeLabels: any[]) {
  return supabaseServiceClient().from('business_activities').upsert(timeLabels, { onConflict: 'clickup_task_tag' });
}
