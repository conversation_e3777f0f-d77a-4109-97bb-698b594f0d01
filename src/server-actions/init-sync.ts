'use server';

import {
  importBexioContactsImportClickupFoldersJob,
  importClickupListsCreateBexioProjectsJob,
  importClickupTasksCreateBexioWorkPackagesJob,
  importClickupTimeEntriesCreateBexioTimeEntriesJob,
} from '@/jobs';

export async function initImportClients() {
    return await importBexioContactsImportClickupFoldersJob.invoke({});
}

export async function initImportClickUpLists() {
  return await importClickupListsCreateBexioProjectsJob.invoke({});
}

export async function initImportClickUpTasks() {
  return await importClickupTasksCreateBexioWorkPackagesJob.invoke({});
}

export async function initImportClickUpTimeEntries() {
  return await importClickupTimeEntriesCreateBexioTimeEntriesJob.invoke({});
}