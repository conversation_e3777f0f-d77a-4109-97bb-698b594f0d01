'use server';

import { nukeClientsJob, nukeProjects<PERSON>ob, nukeTasksJob, nukeTimeEntriesJob } from '@/jobs';

export async function nukeClients() {
  return await nukeClientsJob.invoke({});
}

export async function nukeProjects() {
  return await nukeProjectsJob.invoke({});
}

export async function nukeTasks() {
  return await nukeTasksJob.invoke({});
}

export async function nukeTimeEntries() {
  return await nukeTimeEntriesJob.invoke({});
}