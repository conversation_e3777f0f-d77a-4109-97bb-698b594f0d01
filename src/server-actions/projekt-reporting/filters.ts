'use server';

import { createClient } from '@/data/supabase-server';

export async function getAllFiltersByType(type: string) {
  const supabase = createClient();
  return supabase.from('custom_filters').select('*').eq('type', type);
}

export async function addNewFilterByType(name: string, filter: any[], type: string) {
  const supabase = createClient();
  return supabase.from('custom_filters').insert({ name, filter, type }).select('*').single();
}

export async function deleteFilter(id: number) {
  const supabase = createClient();
  return supabase.from('custom_filters').delete().eq('id', id);
}
