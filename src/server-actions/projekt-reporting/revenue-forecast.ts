'use server';

import { createClient } from '@/data/supabase-server';

export async function getRevenueForecast() {
  const supabase = createClient();
  const { error, data } = await supabase.from('bexio_projects').select('*');

  if (error) {
    return { error: error.message };
  }

  // only project with substatus "27 - Angehalten", "30 - Projektvorbereitung", "40 - Projekt in Arbeit", "50 - Projekt Endphase", "60 - Dokumentation" or "75 - Fakturierung"
  const filteredData = data.filter(
    (p) =>
      String(p.substatus).includes('27') ||
      String(p.substatus).includes('30') ||
      String(p.substatus).includes('40') ||
      String(p.substatus).includes('50') ||
      String(p.substatus).includes('60') ||
      String(p.substatus).includes('75'),
  );

  const totalBudget = filteredData.reduce((acc, p) => acc + Number(p.budget || 0), 0);

  const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1, 0, 0, 0);
  const endOfMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59);
  const currMonthBudget = filteredData
    .filter((p) => p.end_date != null && new Date(p.end_date) >= startOfMonth && new Date(p.end_date) <= endOfMonth)
    .reduce((acc, p) => acc + Number(p.budget || 0), 0);

  const startOfNextMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1, 0, 0, 0);
  const endOfNextMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 2, 0, 23, 59, 59);
  const nextMonthBudget = filteredData
    .filter(
      (p) => p.end_date != null && new Date(p.end_date) >= startOfNextMonth && new Date(p.end_date) <= endOfNextMonth,
    )
    .reduce((acc, p) => acc + Number(p.budget || 0), 0);

  const startOfNextNextMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 2, 1, 0, 0, 0);
  const endOfNextNextMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 3, 0, 23, 59, 59);
  const nextNextMonthBudget = filteredData
    .filter(
      (p) =>
        p.end_date != null &&
        new Date(p.end_date) >= startOfNextNextMonth &&
        new Date(p.end_date) <= endOfNextNextMonth,
    )
    .reduce((acc, p) => acc + Number(p.budget || 0), 0);

  const startOfCurYear = new Date(new Date().getFullYear(), 0, 1, 0, 0, 0);
  const endOfCurYear = new Date(new Date().getFullYear(), 11, 31, 23, 59, 59);
  const curYearBudget = filteredData
    .filter((p) => p.end_date != null && new Date(p.end_date) >= startOfCurYear && new Date(p.end_date) <= endOfCurYear)
    .reduce((acc, p) => acc + Number(p.budget || 0), 0);

  return {
    data: { total: totalBudget, currMonthBudget, nextMonthBudget, nextNextMonthBudget, curYearBudget },
    error: null,
  };
}
