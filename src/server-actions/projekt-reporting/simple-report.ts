'use server';

import { createClient } from '@/data/supabase-server';
import { BexioCSVExportRow } from '@/data/types/bexio-csv-export.types';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

export type ProjectReportReturn = Awaited<ReturnType<typeof getProjectReport>>;

export async function getProjectReport(contactPersonName?: string) {
  const supabase = createClient();
  let query = supabase.from('bexio_projects').select('*');
  if (contactPersonName) {
    query = query.ilike('contact_person', `%${contactPersonName}%`);
  }
  const { data, error } = await query;

  if (error) {
    return { error: error.message };
  }

  return (
    data.map((row) => {
      const isTempUnArchived =
        !!row.last_temp_unarchive && new Date().getTime() - new Date(row.last_temp_unarchive).getTime() < 3 * 60 * 1000;
      return {
        ...row,
        status: isTempUnArchived ? 'Archiviert' : row.status || '',
      };
    }) || []
  );
}

export async function submitCSVData(data: BexioCSVExportRow[]) {
  if (!data || data.length === 0) {
    return { error: new Error('No data to submit') };
  }

  if (!['Archiviert', 'Aktiv', 'Offen'].includes(data[0].Status)) {
    return { error: new Error('WRONG data to submit') };
  }

  const supabase = createClient();
  const { data: existingProjects, error: projectsError } = await supabase.from('bexio_projects').select('*');

  if (projectsError) {
    return { error: projectsError };
  }

  if (new Date().getHours() >= 23) {
    // Check for projects with invalid status combinations
    const openOrActiveProjects = data.filter((p) => p.Status.includes('Offen') || p.Status.includes('Aktiv'));
    console.log(`Found ${openOrActiveProjects.length} projects with open/active status`);
    const projectsWithInvalidStatus = openOrActiveProjects.filter(
      (p) =>
        p.Substatus.includes('90') ||
        p.Substatus.includes('76') ||
        p.Substatus.includes('25') ||
        p.Substatus.includes('75'),
    );
    console.log(`Found ${projectsWithInvalidStatus.length} projects with invalid status`);
    for (const project of projectsWithInvalidStatus) {
      await createInvalidProjectStatusNotification(project);
    }
  }

  // Check for projects with substatus starting with 75 before and now in status 25 or 76 or 90
  const projectsInFakturierung = existingProjects
    .filter((p) => p.bexio_id && p.substatus?.startsWith('75'))
    .filter((p) => !p.close_date);
  const projectsThatAreClosed = data
    .filter((p) => projectsInFakturierung.some((pf) => Number(pf.bexio_id) === Number(p.ID)))
    .filter((p) => p.Substatus.includes('25') || p.Substatus.includes('76') || p.Substatus.includes('90'));
  if (projectsThatAreClosed.length > 0) {
    const supabase = createClient();
    for (const project of projectsThatAreClosed) {
      const { error } = await supabase
        .from('bexio_projects')
        .update({ close_date: new Date().toISOString() })
        .eq('bexio_id', project.ID);
      if (error) {
        console.error('ERROR CLOSE DATE', error);
      }
    }
  }

  const upsertData = data
    .filter((p) => p.Projekt)
    .map((row) => {
      return {
        bexio_id: Number(row.ID),
        substatus: row.Substatus || '',
        bexio_effective_cost: Number(row['Total Kosten ']),
        contact_person: row.Ansprechpartner || '',
        client_name: row.Kontakt || '',
      };
    });

  const { error } = await supabase.from('bexio_projects').upsert(upsertData, { onConflict: 'bexio_id' });
  if (error) {
    console.error('error', error);
    return { error: error.message };
  }

  const projectsToDelete = existingProjects.filter((p) => !data.some((d) => Number(d.ID) === p.bexio_id));
  const { error: deleteError } = await supabase
    .from('bexio_projects')
    .delete()
    .in(
      'bexio_id',
      projectsToDelete.map((p) => p.bexio_id),
    );

  return { error: error || deleteError };
}

async function createInvalidProjectStatusNotification(project: BexioCSVExportRow) {
  const supabase = createClient();
  const { data: notification } = await supabase
    .from('error_notifications')
    .select('*')
    .eq('message_hash', `InvalidStatus#${project.ID}`)
    .single();

  if (notification?.last_sent) {
    console.log(`Notification already sent for Project: ${project.ID}`);
    return;
  }

  const { data: generalSettings } = await supabase.from('global_settings').select('*').single();
  if (!generalSettings?.pm_list_id) {
    return;
  }

  const { data: projectDb, error } = await supabase
    .from('projects')
    .select('clickup_list_id, clickup_user_id')
    .eq('bexio_project_id', project.ID)
    .single();

  if (error) {
    throw new Error(`Error fetching project: ${JSON.stringify(error)}`);
  }

  const clickupClient = new ClickUpClient();
  const newTask = await clickupClient.createTask(String(generalSettings.pm_list_id), {
    name: `pm: Bexio Status falsch - ${project.Projekt}`,
    description: `Status - Substatuskombination falsch! Korrigiere dies in Bexio!

Projekt: ${project.Projekt}
Status: ${project.Status}
Substatus: ${project.Substatus}
Bexio Projekt: https://office.bexio.com/index.php/pr_project/show/id/${project.ID}
Clickup Liste: https://app.clickup.com/${process.env.CLICKUP_TEAM_ID}/v/li/${projectDb.clickup_list_id}

Wenn einer der folgenden Substatus ausgewählt ist, muss der Status auf "Archiviert" sein:
Ablage 
Projekt verloren  
Nicht verrechenbar  
Fakturierung

Gehe in Bexio und korrigiere den Fehler! Verfahre ggf. erneut nach dem aktuellen PM Workflow.`,
    assignees: [projectDb.clickup_user_id || process.env.CLICKUP_ADMIN_USER_ID],
    priority: 1,
    time_estimate: 5 * 60 * 1000,
    due_date: new Date().getTime(),
  });

  if (isClickUpError(newTask)) {
    throw new Error(`Error creating task: ${JSON.stringify(newTask)}`);
  }

  console.log(`Notification created for Project: ${project.ID}`);
  await supabase
    .from('error_notifications')
    .upsert(
      { message_hash: `InvalidStatus#${project.ID}`, last_sent: new Date().toISOString() },
      { onConflict: 'message_hash' },
    );
}
