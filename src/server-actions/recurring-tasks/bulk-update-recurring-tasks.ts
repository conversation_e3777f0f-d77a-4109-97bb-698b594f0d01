'use server';

import { createClient } from '@/data/supabase-server';
import { revalidatePath } from 'next/cache';

export interface BulkUpdateRecurringTasksData {
  task_ids: string[];
  schedule: string;
  skip_weekends: boolean;
  copy_task: boolean;
  recur_end?: string | null;
  schedule_options?: any;
}

export async function bulkUpdateRecurringTasks(data: BulkUpdateRecurringTasksData) {
  const supabase = createClient();
  
  const baseUpdateData: any = {
    schedule: data.schedule,
    skip_weekends: data.skip_weekends,
    copy_task: data.copy_task,
    schedule_options: data.schedule_options || {
      skip_weekends: data.skip_weekends
    }
  };

  // Only include recur_end if it's provided
  if (data.recur_end !== undefined) {
    baseUpdateData.recur_end = data.recur_end;
  }

  // Create an array of records to upsert, one for each task_id
  const recordsToUpsert = data.task_ids.map(taskId => ({
    clickup_task_id: taskId,
    ...baseUpdateData
  }));

  const { error } = await supabase
    .from('recurring_tasks')
    .upsert(recordsToUpsert, {
      onConflict: 'clickup_task_id'
    });

  if (error) {
    console.error('Error bulk updating recurring tasks:', error);
    return { error: error.message };
  }

  revalidatePath('/projects/recurring-tasks');
  return { success: true, updated_count: data.task_ids.length };
}