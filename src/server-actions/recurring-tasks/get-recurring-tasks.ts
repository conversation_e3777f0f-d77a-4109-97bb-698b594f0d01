'use server';

import { createClient } from '@/data/supabase-server';

export async function getRecurringTasks() {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('recurring_tasks')
    .select(`
      clickup_task_id,
      schedule,
      schedule_options,
      skip_weekends,
      recur_end,
      copy_task,
      tasks (
        name,
        clickup_task_id,
        clickup_assignees
      )
    `)
    .order('clickup_task_id');

  if (error) {
    console.error('Error fetching recurring tasks:', error);
    return { error: error.message };
  }

  // Fetch all employees to map assignees
  const { data: employees, error: employeesError } = await supabase
    .from('employees')
    .select('clickup_user_id, name, profile_picture_url');

  if (employeesError) {
    console.error('Error fetching employees:', employeesError);
    return { error: employeesError.message };
  }

  // Create a map of clickup_user_id to employee data
  const employeeMap = new Map(
    employees?.map(emp => [emp.clickup_user_id, emp]) || []
  );

  // Add assignee details to each task
  const tasksWithAssignees = data?.map(task => ({
    ...task,
    tasks: task.tasks ? {
      ...task.tasks,
      assignees: task.tasks.clickup_assignees?.map((assigneeId: number) => {
        const employee = employeeMap.get(assigneeId.toString());
        return employee ? {
          clickup_user_id: employee.clickup_user_id,
          name: employee.name,
          profile_picture_url: employee.profile_picture_url
        } : null;
      }).filter((assignee): assignee is { clickup_user_id: string | null; name: string; profile_picture_url: string | null; } => assignee !== null) || []
    } : null
  }));

  return { data: tasksWithAssignees };
}