'use server';

import { createClient } from '@/data/supabase-server';
import { revalidatePath } from 'next/cache';

export interface UpdateIgnoredListIdsData {
  ignored_list_ids: string[];
}

export async function updateIgnoredListIds(data: UpdateIgnoredListIdsData) {
  try {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('global_settings')
      .update({
        ignored_recurring_tasks_list_ids: data.ignored_list_ids,
      })
      .eq('id', 1);

    if (error) {
      console.error('Error updating ignored list IDs:', error);
      return { error: error.message };
    }

    revalidatePath('/recurring-tasks');
    return { success: true };
  } catch (error) {
    console.error('Unexpected error updating ignored list IDs:', error);
    return { error: 'Ein unerwarteter Fehler ist aufgetreten' };
  }
}

export async function getIgnoredListIds() {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('global_settings')
      .select('ignored_recurring_tasks_list_ids')
      .eq('id', 1)
      .single();

    if (error) {
      console.error('Error fetching ignored list IDs:', error);
      return { error: error.message };
    }

    return { data: data?.ignored_recurring_tasks_list_ids || [] };
  } catch (error) {
    console.error('Unexpected error fetching ignored list IDs:', error);
    return { error: 'Ein unerwarteter Fehler ist aufgetreten' };
  }
}