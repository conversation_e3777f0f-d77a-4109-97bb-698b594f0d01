'use server';

import { createClient } from '@/data/supabase-server';
import { revalidatePath } from 'next/cache';

export interface UpdateRecurringTaskData {
  clickup_task_id: string;
  schedule: string;
  skip_weekends: boolean;
  copy_task: boolean;
  recur_end?: string | null;
  schedule_options?: any;
}

export async function updateRecurringTask(data: UpdateRecurringTaskData) {
  const supabase = createClient();
  
  const updateData: any = {
    schedule: data.schedule,
    skip_weekends: data.skip_weekends,
    copy_task: data.copy_task,
    schedule_options: data.schedule_options || {
      skip_weekends: data.skip_weekends
    }
  };

  // Only include recur_end if it's provided
  if (data.recur_end !== undefined) {
    updateData.recur_end = data.recur_end;
  }

  const { error } = await supabase
    .from('recurring_tasks')
    .update(updateData)
    .eq('clickup_task_id', data.clickup_task_id);

  if (error) {
    console.error('Error updating recurring task:', error);
    return { error: error.message };
  }



  revalidatePath('/projects/recurring-tasks');
  return { success: true };
}