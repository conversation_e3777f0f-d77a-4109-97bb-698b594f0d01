'use server';

import { supabaseServiceClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { revalidatePath } from 'next/cache';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';

export async function getClickupUsers() {
  const clickupClient = new ClickUpClient();
  const foldersResponse = await clickupClient.getAllFolders();

  if ('err' in foldersResponse) {
    console.error(foldersResponse);
    return [];
  }

  const listId = foldersResponse.folders[0].lists[0].id;
  const memberResponse = await clickupClient.getAllListMembers(Number(listId));

  if ('err' in memberResponse) {
    console.error(memberResponse);
    return [];
  }

  return memberResponse.members;
}

export async function getBexioUsers() {
  const bexioClient = new BexioClient();
  const userResponse = await bexioClient.getUsers();

  if (isBexioError(userResponse)) {
    console.error(userResponse);
    return [];
  }

  return userResponse;
}

export async function syncEmployeesAction() {
  const clickUpUsers = await getClickupUsers();
  const bexioUsers = await getBexioUsers();

  const usersUpsert = clickUpUsers.map((member) => {
    return {
      clickup_user_id: member.id,
      name: member.username || 'Unknown',
      email: member.email,
      profile_picture_url: member.profilePicture,
      bexio_user_id: bexioUsers.find((user) => user.email === member.email)?.id || null,
    };
  });

  const { error: usersError } = await supabaseServiceClient()
    .from('employees')
    .upsert(usersUpsert, { onConflict: 'clickup_user_id' });

  if (usersError) {
    return { error: usersError };
  }

  revalidatePath('/employees');

  return { success: true };
}
