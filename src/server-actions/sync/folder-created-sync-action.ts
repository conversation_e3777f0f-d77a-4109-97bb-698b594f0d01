'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { isClickUpError } from '@/data/types/clickup.types';
import { DropboxClient } from '@/data/dropbox-client';
import { buildDropboxClientFolderPath } from '@/lib/utils';

export async function folderCreatedSyncAction(folderId: string) {
  // 1.1 FETCH CLICKUP FOLDER
  const clickupClient = new ClickUpClient();
  const folder = await clickupClient.getFolder(folderId);

  if (isClickUpError(folder)) {
    return new Error(`Error fetching folder: ${JSON.stringify(folder)}`);
  }

  // 1.2 INSERT CLICKUP FOLDER INTO DB
  const newClient = {
    clickup_folder_id: folder.id,
    name: folder.name,
    clickup_folder_assignee_id: folder.assignee?.id,
    clickup_archived: folder.archived,
  };

  // UPSERT - IN ORDER TO MERGE WITH EXISTING DATA FROM BEXIO
  const supabase = createClient();
  const { error: insertClickupFoldersError } = await supabase.from('clients').upsert(newClient, { onConflict: 'name' });

  if (insertClickupFoldersError) {
    return new Error(`Error inserting folder: ${JSON.stringify(insertClickupFoldersError)}`);
  }

  // 2 CREATE DROPBOX FOLDER
  const dropboxClient = new DropboxClient();
  const folderPath = buildDropboxClientFolderPath({ name: folder.name, clickup_archived: folder.archived });
  try {
    const { result } = await dropboxClient.createFolder(folderPath);
    await supabase
      .from('clients')
      .update({ dropbox_folder_path: result.metadata.path_lower })
      .eq('clickup_folder_id', folder.id);
  } catch (error) {
    // @ts-ignore
    const status = error?.status;

    if (status == 409) {
      console.log(`Folder already exists: ${folderPath}`);
      return null;
    }

    if (status >= 400) {
      return new Error(`Error creating folder: ${JSON.stringify(error)}`);
    }
  }

  return null;
}
