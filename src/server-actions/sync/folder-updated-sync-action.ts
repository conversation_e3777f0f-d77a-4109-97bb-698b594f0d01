'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { isClickUpError } from '@/data/types/clickup.types';
import { DropboxClient } from '@/data/dropbox-client';
import { buildDropboxClientFolderPath } from '@/lib/utils';

export async function folderUpdatedSyncAction(folderId: string) {
  console.log(`FOLDER UPDATED: ${folderId}`);

  // 1.1 FETCH CLICKUP FOLDER
  const clickupClient = new ClickUpClient();
  const folder = await clickupClient.getFolder(folderId);

  if (isClickUpError(folder)) {
    return new Error(`Error fetching folder: ${JSON.stringify(folder)}`);
  }

  // 1.2 UPDATE CLICKUP FOLDER IN DB
  const newClient = {
    clickup_folder_id: folder.id,
    name: folder.name,
    clickup_folder_assignee_id: folder.assignee?.id,
    clickup_archived: folder.archived,
  };

  // UPSERT - IN ORDER TO MERGE WITH EXISTING DATA FROM BEXIO
  const supabase = createClient();
  const { data, error: updateClickupFoldersError } = await supabase
    .from('clients')
    .upsert(newClient, { onConflict: 'clickup_folder_id' })
    .select()
    .single();

  if (updateClickupFoldersError) {
    return new Error(`Error updating folder: ${JSON.stringify(updateClickupFoldersError)}`);
  }

  // 2 UPDATE DROPBOX FOLDER
  const dropboxClient = new DropboxClient();
  const folderPath = buildDropboxClientFolderPath(data);

  if (data.dropbox_folder_path === folderPath.toLowerCase()) {
    console.log(`Folder already up to date: ${folderPath}`);
    return null;
  }

  let savedFolderPath;
  if (data.dropbox_folder_path) {
    try {
      const { result } = await dropboxClient.renameFolder(data.dropbox_folder_path, folderPath);

      savedFolderPath = result.metadata.path_lower;
    } catch (error) {
      // @ts-ignore
      const status = error?.status;
      if (status >= 400 && status != 409) {
        return new Error(`Error updating folder: ${status}`);
      }

      if (status == 409) {
        console.error(`Folder already exists: ${folderPath}`);
        return null;
      }
    }
  } else {
    try {
      const { result, status } = await dropboxClient.createFolder(folderPath);
      savedFolderPath = result.metadata.path_lower;
    } catch (error) {
      // @ts-ignore
      const status = error?.status;

      if (status >= 400 && status != 409) {
        return new Error(`Error creating folder: ${JSON.stringify(error)}`);
      }

      if (status == 409) {
        console.error(`Folder already exists: ${folderPath}`);
        return null;
      }
    }
  }

  await supabase.from('clients').update({ dropbox_folder_path: savedFolderPath }).eq('clickup_folder_id', folder.id);

  return null;
}
