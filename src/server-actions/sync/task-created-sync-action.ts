'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { isClickUpError } from '@/data/types/clickup.types';
import { isBexioError } from '@/data/types/bexio.types';

export async function taskCreatedSyncAction(taskId: string) {
  // 1 GET TASK FROM CLICKUP
  const clickupClient = new ClickUpClient();
  const task = await clickupClient.getTask(taskId);

  if (isClickUpError(task)) {
    return new Error(`Error fetching task: ${JSON.stringify(task)}`);
  }

  // 2 INSERT TASK INTO DB
  const userId = task.assignees[0]?.id && task.assignees[0]?.id > 0 ? task.assignees[0]?.id : task.creator?.id;
  const taskInsert = {
    clickup_task_id: task.id,
    name: task.name,
    clickup_list_id: task.list.id,
    clickup_due_date: task.due_date,
    clickup_task_description: task.description,
    clickup_time_estimate: task.time_estimate,
    clickup_user_id: userId < 0 ? null : userId,
  };

  const { data, error } = await supabaseServiceClient()
    .from('tasks')
    .insert(taskInsert)
    .select('*, projects(bexio_project_id)')
    .single();

  if (error) {
    return new Error(`Error inserting task into db: ${JSON.stringify(error)}`);
  }

  if (!data) {
    return { info: `No data returned from inserting task into db: ${JSON.stringify(data)}` };
  }

  if (!data.projects || !data.projects.bexio_project_id) {
    return { info: `No bexio project id found for task: ${JSON.stringify(data)}` };
  }

  // 3 INSERT TASK INTO BEXIO
  const bexioWorkPackage = {
    name: trimLength(data.name, 254), // TRIM TO 255 CHARS
    comment: trimLength(data.clickup_task_description, 999), // TRIM TO 1000 CHARS
    estimated_time_in_hours: convertMillisecondsToHours(Number(data.clickup_time_estimate)),
  };
  const bexioClient = new BexioClient();
  const createdWorkPackage = await bexioClient.createWorkPackage(data.projects.bexio_project_id, bexioWorkPackage);

  if (isBexioError(createdWorkPackage)) {
    return new Error(`Error creating work package in bexio: ${JSON.stringify(createdWorkPackage)}`);
  }

  // 4 UPDATE TASK WITH BEXIO ID
  // @ts-ignore
  const { error: insertBexioWPIdError } = await supabaseServiceClient()
    .from('tasks')
    .update({
      bexio_work_package_id: createdWorkPackage.id,
    })
    .eq('clickup_task_id', data.clickup_task_id);

  if (insertBexioWPIdError) {
    return new Error(`Error updating task with bexio work package id: ${JSON.stringify(insertBexioWPIdError)}`);
  }

  return null;
}
