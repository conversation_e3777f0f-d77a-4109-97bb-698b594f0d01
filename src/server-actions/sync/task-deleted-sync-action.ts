'use server';

import { createClient, supabaseServiceClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { isBexioError } from '@/data/types/bexio.types';

export async function taskDeletedSyncAction(taskId: string) {
  // 1 GET BEXIO WORK PACKAGE ID
  const supabase = createClient();
  const { data: taskData, error: taskError } = await supabase
    .from('tasks')
    .select('bexio_work_package_id, projects(bexio_project_id)')
    .eq('clickup_task_id', taskId)
    .single();

  if (taskError) {
    return new Error(`Error fetching task: ${JSON.stringify(taskError)}`);
  }

  if (!taskData) {
    return { info: `No data returned from fetching task: ${JSON.stringify(taskData)}` };
  }

  if (taskData.projects && taskData.projects.bexio_project_id && taskData.bexio_work_package_id) {
    // 2 REMOVE TASK FROM BEXIO
    const bexioClient = new BexioClient();
    const deleteResponse = await bexioClient.deleteWorkPackage(
      taskData.projects.bexio_project_id,
      taskData.bexio_work_package_id,
    );

    if (isBexioError(deleteResponse)) {
      return new Error(`Error deleting work package in bexio: ${JSON.stringify(deleteResponse)}`);
    }
  }

  // 3 REMOVE TASK FROM DB
  const { error: deleteError } = await supabaseServiceClient().from('tasks').delete().eq('clickup_task_id', taskId);

  if (deleteError) {
    return new Error(`Error deleting task from db: ${JSON.stringify(deleteError)}`);
  }

  return null;
}
