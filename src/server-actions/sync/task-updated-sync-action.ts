'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { createClient } from '@/data/supabase-server';
import { BexioClient } from '@/data/bexio-clients';
import { convertMillisecondsToHours, trimLength } from '@/lib/utils';
import { isClickUpError } from '@/data/types/clickup.types';
import { isBexioError } from '@/data/types/bexio.types';

export async function taskUpdatedSyncAction(taskId: string) {
  // 1 GET TASK FROM CLICKUP
  const clickupClient = new ClickUpClient();
  const task = await clickupClient.getTask(taskId);

  if (isClickUpError(task)) {
    return new Error(`Error fetching task: ${JSON.stringify(task)}`);
  }

  // 2 UPDATE TASK IN DB
  const userId = task.assignees[0]?.id && task.assignees[0]?.id > 0 ? task.assignees[0]?.id : task.creator?.id;
  const taskUpdate = {
    clickup_task_id: task.id,
    name: task.name,
    clickup_list_id: task.list.id,
    clickup_due_date: Number(task.due_date),
    clickup_task_description: task.description,
    clickup_time_estimate: task.time_estimate,
    clickup_user_id: userId < 0 ? null : userId,
  };

  const supabase = createClient();
  const { data: oldData, error: oldError } = await supabase
    .from('tasks')
    .select('*, projects(bexio_project_id)')
    .eq('clickup_task_id', taskId)
    .single();

  if (oldError) {
    return new Error(`Error fetching old task data: ${JSON.stringify(oldError)}`);
  }

  const { data, error } = await supabase
    .from('tasks')
    // @ts-ignore
    .update(taskUpdate)
    .eq('clickup_task_id', taskId)
    .select('*, projects(bexio_project_id)')
    .single();

  if (error) {
    return new Error(`Error updating task in db: ${JSON.stringify(error)}`);
  }

  if (!data || !oldData) {
    return { info: `No data returned from updating task into db: ${JSON.stringify(data)}` };
  }

  if (!data.projects || !data.projects.bexio_project_id || !oldData.projects || !oldData.projects.bexio_project_id) {
    return { info: `No bexio project id found for task: ${JSON.stringify(data)}` };
  }

  // 3 UPDATE TASK IN BEXIO (only when name, comment or estimated_time_in_hours has changed)
  const bexioWorkPackage = {
    name: trimLength(data.name, 254), // TRIM TO 255 CHARS
    comment: trimLength(data.clickup_task_description, 999), // TRIM TO 1000 CHARS
    estimated_time_in_hours: convertMillisecondsToHours(Number(data.clickup_time_estimate)),
  };
  const bexioClient = new BexioClient();
  const createdWorkPackage = await bexioClient.updateWorkPackage(
    oldData.projects.bexio_project_id,
    Number(data.bexio_work_package_id),
    bexioWorkPackage,
  );

  if (isBexioError(createdWorkPackage)) {
    return new Error(`Error updating work package in bexio: ${JSON.stringify(createdWorkPackage)}`);
  }

  return null;
}
