'use server';

import { DateRange } from 'react-day-picker';
import { createClient } from '@/data/supabase-server';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';

export async function getFaultyTagsEntriesAction(
  userIds: string[],
  filterType: string,
  dateRange: DateRange | undefined,
) {
  const supabase = createClient();
  const query = supabase.from('time_entries').select('*, employees(name)');

  query.in('clickup_user_id', userIds);

  if (filterType === 'missing-labels') {
    query.eq('clickup_task_tag', 'null').eq('billable', true);
  } else if (filterType === 'missing-task') {
    query.is('clickup_task_id', null);
  } else if (filterType !== 'overlapping') {
    query.or('and(clickup_task_tag.eq.null,billable.eq.true),clickup_task_id.is.null');
  }

  if (dateRange) {
    query.gte('clickup_start', dateRange.from?.getTime()).lte('clickup_end', dateRange.to?.getTime());
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Error fetching time entries: ${JSON.stringify(error)}`);
  }

  let overlappingEntries: { entry1: TimeEntry; entry2: TimeEntry }[] = [];
  if (!filterType || filterType === 'overlapping' || filterType === 'all') {
    overlappingEntries = await getOverlappingEntries(dateRange!.from?.getTime()!, dateRange!.to?.getTime()!, userIds);
  }

  return { faultyEntries: (filterType !== 'overlapping' && data) || [], overlappingEntries };
}

interface TimeEntry {
  id: number;
  clickup_start: number;
  clickup_end: number;
  clickup_user_id: string;
  employees: { name: string };
  tasks?: { clickup_list_id: string };
  clickup_task_id: string;
}

async function getTimeEntries(startDate: number, endDate: number): Promise<TimeEntry[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('time_entries')
    .select('id, clickup_start, clickup_end, clickup_user_id, employees(name), clickup_task_id, tasks(clickup_list_id)')
    .gte('clickup_start', startDate)
    .lte('clickup_end', endDate);

  if (error) {
    console.error('Error fetching time entries:', error);
    return [];
  }

  return data as TimeEntry[];
}

function findOverlappingEntries(entries: TimeEntry[]): { entry1: TimeEntry; entry2: TimeEntry }[] {
  const overlaps: { entry1: TimeEntry; entry2: TimeEntry }[] = [];

  for (let i = 0; i < entries.length; i++) {
    for (let j = i + 1; j < entries.length; j++) {
      const entry1 = entries[i];
      const entry2 = entries[j];

      if (entry1.clickup_user_id === entry2.clickup_user_id) {
        if (
          (entry1.clickup_start < entry2.clickup_end && entry1.clickup_end > entry2.clickup_start) ||
          (entry2.clickup_start < entry1.clickup_end && entry2.clickup_end > entry1.clickup_start)
        ) {
          const overlapStart = Math.max(entry1.clickup_start, entry2.clickup_start);
          const overlapEnd = Math.min(entry1.clickup_end, entry2.clickup_end);
          const overlapDuration = overlapEnd - overlapStart;

          if (overlapDuration >= 60 * 1000) {
            overlaps.push({ entry1, entry2 });
          }
        }
      }
    }
  }

  return overlaps;
}

async function getOverlappingEntries(
  startDate: number,
  endDate: number,
  userIds: string[],
): Promise<{ entry1: TimeEntry; entry2: TimeEntry }[]> {
  const entries = await getTimeEntries(startDate, endDate);
  const globalSettings = await getGlobalSettings();
  let entriesFiltered = entries.filter((entry) => userIds.includes(String(entry.clickup_user_id)));
  entriesFiltered = entriesFiltered.filter(
    (entry) => entry.tasks?.clickup_list_id !== String(globalSettings.data?.pto_list_id),
  );
  return findOverlappingEntries(entriesFiltered);
}
