'use server';

import { createClient } from '@/data/supabase-server';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';

export async function getFaultyEntriesAction() {
  const { data: globalSettings } = await getGlobalSettings();
  const listId = String(globalSettings?.false_timeentries_list_id);

  const supabase = createClient();
  const { data, error } = await supabase
    .from('time_entries')
    .select('*, employees(name), tasks(clickup_list_id, clickup_url)');

  if (error) {
    return { error };
  }

  const faultyEntries =
    data?.filter((te) => {
      return te.tasks?.clickup_list_id === listId;
    }) || [];

  return { data: faultyEntries };
}
