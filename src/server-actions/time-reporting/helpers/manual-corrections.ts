'use server';

import { createClient } from '@/data/supabase-server';
import { getISODateString } from '@/lib/utils';

export async function getManualCorrectionsFromDb(start: Date, end: Date) {
  const supabase = createClient();
  return supabase
    .from('time_corrections')
    .select('*, employees(name, inactive)')
    .gte('date', start.toISOString())
    .lte('date', end.toISOString());
}

type FullManCorr = {
  [key: string]: {
    pto: number;
    ptoTf: number;
    nonPto: number;
    nonPtoTf: number;
    ptoNegativeTf: number;
    negativePto: number;
    positivePto: number;
  };
};

export async function getFullManualCorrections(
  startTotalRang: Date,
  endTotalRange: Date,
  startTimeFrame: string,
  endTimeFrame: string,
): Promise<FullManCorr> {
  const endTotalRangeWithTime = new Date(new Date(endTotalRange).setHours(23, 59, 59, 999));
  const { data: manualCorrections } = await getManualCorrectionsFromDb(startTotalRang, endTotalRangeWithTime);
  return (manualCorrections || []).reduce((acc, curr) => {
    if (!curr.employees?.name) {
      return acc;
    }

    const name = String(curr.employees?.name);
    if (!acc[name]) {
      acc[name] = { pto: 0, ptoTf: 0, nonPto: 0, nonPtoTf: 0, ptoNegativeTf: 0, negativePto: 0, positivePto: 0 };
    }

    const currDate = getISODateString(new Date(curr.date));
    if (curr.is_pto) {
      acc[name].pto += curr.hours;

      if (curr.hours > 0) {
        acc[name].positivePto += curr.hours;
      } else if (curr.hours < 0) {
        acc[name].negativePto += curr.hours;

        if (currDate >= startTimeFrame && currDate <= endTimeFrame) {
          acc[name].ptoNegativeTf += curr.hours;
        }
      }

      if (currDate >= startTimeFrame && currDate <= endTimeFrame) {
        acc[name].ptoTf += curr.hours;
      }
    } else {
      acc[name].nonPto += curr.hours;

      if (currDate >= startTimeFrame && currDate <= endTimeFrame) {
        acc[name].nonPtoTf += curr.hours;
      }
    }

    return acc;
  }, {} as FullManCorr);
}

type SimpleManCorr = {
  [key: string]: {
    pto: number;
    nonPto: number;
  };
};

export async function getSimpleManualCorrections(startRange: Date, endRange: Date): Promise<SimpleManCorr> {
  const { data: manCorr } = await getManualCorrectionsFromDb(startRange, endRange);
  return (
    manCorr?.reduce((acc, curr) => {
      if (!curr.employees?.name) {
        return acc;
      }

      const name = String(curr.employees?.name);
      if (!acc[name]) {
        acc[name] = { pto: 0, nonPto: 0 };
      }

      if (curr.is_pto) {
        acc[name].pto += curr.hours;
      } else {
        acc[name].nonPto += curr.hours;
      }

      return acc;
    }, {} as SimpleManCorr) || {}
  );
}
