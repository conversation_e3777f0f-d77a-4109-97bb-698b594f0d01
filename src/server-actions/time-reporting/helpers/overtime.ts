'use server';

import { ClickUpTask, ClickUpTimeEntry } from '@/data/types/clickup.types';
import { getFirstDayOfWeek, getISODateString, getLastDayOfWeek } from '@/lib/utils';
import { getEmployees, WorkingHours } from '@/server-actions/time-reporting/simple-report';
import { getAllPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';
import { getManualCorrectionsFromDb } from '@/server-actions/time-reporting/helpers/manual-corrections';

function getWeeklyOvertime(
  clickupEntries: ClickUpTimeEntry[],
  ptoTasks: ClickUpTask[],
  workingHours: WorkingHours | undefined,
  previousOvertime: {
    [key: string]: number;
  },
  start: Date,
  end: Date,
) {
  // date strings between start and end
  const dateStrings: string[] = [];
  let currentDate = new Date(start);
  while (currentDate <= end) {
    dateStrings.push(getISODateString(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  const employees = workingHours?.map((wh) => String(wh.employees?.name)) || [];
  const uniqueEmployees = employees.filter((v, i, a) => a.indexOf(v) === i);

  const ptoTasksForTimeRange = ptoTasks.filter((task) => {
    return Number(task.start_date) >= start.getTime() && Number(task.start_date) <= end.getTime();
  });

  // sum up clickup entries for time range
  const clickupEntriesForTimeRange = clickupEntries.filter((entry) => {
    return Number(entry.start) >= start.getTime() && Number(entry.start) <= end.getTime();
  });

  const totalHours = clickupEntriesForTimeRange.reduce((acc, curr) => {
    if (!curr.user.username) {
      return acc;
    }

    const name = String(curr.user.username);
    if (!acc[name]) {
      acc[name] = 0;
    }

    acc[name] += Number(curr.duration);
    return acc;
  }, {} as any);

  // calculate hours for each day
  const dailyHours = clickupEntriesForTimeRange.reduce((acc, curr) => {
    if (!curr.user.username) {
      return acc;
    }

    const name = String(curr.user.username);
    if (!acc[name]) {
      acc[name] = {};
    }

    const date = new Date(Number(curr.start));
    const dateStr = getISODateString(date);
    if (!acc[name][dateStr]) {
      acc[name][dateStr] = 0;
    }

    acc[name][dateStr] += Number(curr.duration) / 1000 / 60 / 60;
    return acc;
  }, {} as any);

  const workingHoursByNameAndDate = workingHours?.reduce((acc, curr) => {
    if (!curr.employees?.name || curr.employees?.inactive) {
      return acc;
    }

    const name = String(curr.employees?.name);
    if (!acc[name]) {
      acc[name] = {};
    }

    const date = new Date(curr.date);
    const dateStr = getISODateString(date);
    if (!acc[name][dateStr]) {
      acc[name][dateStr] = 0;
    }

    acc[name][dateStr] += curr.hours;
    return acc;
  }, {} as any);
  // console.log('workingHoursByNameAndDate', workingHoursByNameAndDate);

  // calculate difference between total hours and workingHours for each day
  const weeklyOvertime = Object.keys(workingHoursByNameAndDate).reduce((acc, curr) => {
    let currOvertime = previousOvertime[curr] || 0;
    for (const date of dateStrings) {
      const startTs = new Date(date).setHours(0, 0, 0, 0);
      const endTs = new Date(date).setHours(23, 59, 59, 999);
      const ptoTasksTR = ptoTasks.filter((task) => {
        return Number(task.start_date) >= startTs && Number(task.start_date) <= endTs;
      });
      const ptoTasksUser = ptoTasksTR.filter((task) => {
        const names = task.assignees.map((assignee) => String(assignee.username));
        return names.includes(curr);
      });
      const ptoTime = ptoTasksUser.reduce((prevVal, curTask) => {
        const timeSpent = 'time_spent' in curTask && curTask.time_spent ? Number(curTask.time_spent) : 0;
        return prevVal + timeSpent / 1000 / 60 / 60 / (curTask.assignees.length || 1);
      }, 0);

      let shouldWorkHours = 0;
      if (!!workingHoursByNameAndDate[curr] && !!workingHoursByNameAndDate[curr][date]) {
        shouldWorkHours = workingHoursByNameAndDate[curr][date];
      }

      let workedHours = 0;
      if (!!dailyHours[curr] && !!dailyHours[curr][date]) {
        workedHours = dailyHours[curr][date];
      }

      if (ptoTime > 0) {
        workedHours += ptoTime;
      }

      if (!acc[curr]) {
        acc[curr] = 0;
      }

      const bruttoOvertime = Math.min(workedHours - shouldWorkHours, 0);
      if (currOvertime > 0) {
        const newOvertime = Math.max(currOvertime + bruttoOvertime, 0);
        const correctedDifference = newOvertime - currOvertime;
        acc[curr] += correctedDifference;
        if (curr == 'Michel Giesser' && date >= '2024-12-01') {
          // console.debug(curr, date, workedHours, shouldWorkHours, correctedDifference, currOvertime, newOvertime);
        }

        currOvertime = newOvertime;
      } else {
        if (curr == 'Michel Giesser' && date >= '2024-12-01') {
          // console.debug(curr, date, workedHours, shouldWorkHours, 0, currOvertime, currOvertime);
        }
      }
    }

    return acc;
  }, {} as any);

  // add pto hours to total hours
  const ptoHours = ptoTasksForTimeRange.reduce((acc, curr) => {
    const count = curr.assignees.length;
    for (const user of curr.assignees) {
      if (!acc[user.username]) {
        acc[user.username] = 0;
      }
      acc[user.username] += (curr.time_spent || 0) / count;
    }
    return acc;
  }, {} as any);

  // calculate overtime (iterate over totalHours keys and check if > 50)
  return uniqueEmployees.reduce((acc, curr) => {
    const curTotal = totalHours[curr] + (ptoHours[curr] || 0);
    const fiftyHours = 50 * 60 * 60 * 1000;
    acc[curr] = 0;
    if (curTotal > fiftyHours) {
      acc[curr] += (curTotal - fiftyHours) / 1000 / 60 / 60;
    }

    if (weeklyOvertime[curr] && weeklyOvertime[curr] < 0) {
      acc[curr] += Math.min(weeklyOvertime[curr], 0);
    }
    // console.debug('OVERTIME', curr, acc[curr]);
    return acc;
  }, {} as any);
}

export async function getOvertime(
  clickupEntries: ClickUpTimeEntry[],
  workingHours: WorkingHours | null,
  ptoListId: string,
  start: Date,
  end: Date,
) {
  const workingHoursWithoutToday = workingHours?.filter((wh) => wh.date < getISODateString(new Date()));
  const employees = (await getEmployees())?.map((employee) => String(employee.name)) || [];
  const ptoTasks: ClickUpTask[] = await getAllPTOTimes(ptoListId);
  const startDate = getFirstDayOfWeek(start);
  const endDate = getLastDayOfWeek(end);
  let currentDate = new Date(new Date(startDate).getTime() + 2 * 60 * 60 * 1000);
  let result = employees.reduce((acc, curr) => {
    acc[curr] = 0;
    return acc;
  }, {} as any);

  const { data: manCorr } = await getManualCorrectionsFromDb(start, end);
  const overtimeCorr = manCorr?.filter((manCorr) => manCorr.is_overtime);

  let oldResult = {} as any;
  while (currentDate <= endDate) {
    const weekStart = new Date(new Date(currentDate).setHours(0, 0, 0, 0));
    const weekEnd = new Date(getLastDayOfWeek(weekStart).setHours(23, 59, 59, 999));

    const overtime = getWeeklyOvertime(
      clickupEntries,
      ptoTasks,
      workingHoursWithoutToday,
      oldResult,
      weekStart,
      weekEnd,
    );

    const appliedOvertimeCorrections = overtimeCorr?.filter(
      (corr) => weekStart <= new Date(corr.date) && new Date(corr.date) <= weekEnd,
    );

    result = Object.keys(overtime).reduce((acc, currName) => {
      const corrections = appliedOvertimeCorrections?.filter((corr) => corr.employees?.name === currName);
      const correctionSumHrs = corrections?.reduce((acc, curr) => acc + curr.hours, 0);

      if (correctionSumHrs) {
        console.log(currName, correctionSumHrs);
      }
      acc[currName] += overtime[currName] + (correctionSumHrs || 0);
      return acc;
    }, result);
    oldResult = result;

    // Increment by one week
    currentDate.setDate(currentDate.getDate() + 7);
  }

  const manualOTCorrections =
    overtimeCorr?.reduce(
      (acc, curr) => {
        if (!curr.employees?.name) {
          return acc;
        }

        const name = String(curr.employees?.name);
        if (!acc[name]) {
          acc[name] = 0;
        }

        acc[name] += curr.hours;

        return acc;
      },
      {} as { [key: string]: number },
    ) || {};

  Array.from(Object.keys(result)).forEach((key) => {
    const hours = result[String(key)];
    const manualHours = 0; // manualOTCorrections[String(key)] || 0;
    result[String(key)] = hours + manualHours;
  });

  return result;
}
