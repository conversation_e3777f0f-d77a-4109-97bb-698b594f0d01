'use server';

import { cache } from '@/lib/cache';
import { ClickUpTask, ClickUpTimeEntry, isClickUpError } from '@/data/types/clickup.types';
import { ClickUpClient } from '@/data/clickup-client';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';

export const getAllPTOTimes = (ptoListId: string) =>
  cache<ClickUpTask[]>(
    'fetch-pto-list',
    async () => {
      const clickupClient = new ClickUpClient();
      let isLastPage = false;
      let tasks: ClickUpTask[] = [];
      let page = 0;
      while (!isLastPage) {
        const tasksResponse = await clickupClient.getCustomTasksQuery(ptoListId, page, {
          include_closed: true,
          subtasks: true,
        });
        if (isClickUpError(tasksResponse)) {
          throw Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)}`);
        }
        tasks = tasks.concat(tasksResponse.tasks);
        isLastPage = tasksResponse.last_page;
        page++;
      }
      return tasks;
    },
    6000,
  );

export const getAllFridgeTimes = (fridgeListId: string) =>
  cache<ClickUpTask[]>(
    'fetch-fridge-list',
    async () => {
      const clickupClient = new ClickUpClient();
      let isLastPage = false;
      let tasks: ClickUpTask[] = [];
      let page = 0;
      while (!isLastPage) {
        const tasksResponse = await clickupClient.getCustomTasksQuery(fridgeListId, page, {
          include_closed: true,
          subtasks: true,
        });
        if (isClickUpError(tasksResponse)) {
          throw Error(`Error fetching tasks: ${JSON.stringify(tasksResponse)}`);
        }
        tasks = tasks.concat(tasksResponse.tasks);
        isLastPage = tasksResponse.last_page;
        page++;
      }

      return tasks.filter((task) => {
        const taskTypeOptions = task.custom_fields?.find((field) => field.name === 'Aufgabentyp TFC')?.type_config
          ?.options;
        const taskTypeIndex = task.custom_fields?.find((field) => field.name === 'Aufgabentyp TFC')?.value;
        const taskTypeName = taskTypeOptions?.find(
          (option) => Number(option.orderindex) === Number(taskTypeIndex),
        )?.name;
        return taskTypeName == 'EVENT';
      });
    },
    6000,
  );

type PTOSummary = {
  [key: string]: {
    estimated: number;
    estimatedNotUsed: number;
    used: number;
    holiday: number;
    sick: number;
    accident: number;
    military: number;
    parent: number;
    publicHoliday: number;
    education: number;
    other: number;
  };
};

export async function getPTOTimes(ptoListId: string, start: Date, end: Date, customSaldoEndDate?: Date) {
  const tasks: ClickUpTask[] = await getAllPTOTimes(ptoListId);

  const allHolidayTasks = tasks.filter((task) =>
    task.custom_fields?.some((field) => field.name === 'PTO Typ' && Number(field.value) === 0),
  );

  const filteredTasks =
    tasks.filter((task) => {
      return Number(task.start_date) >= start.getTime() && Number(task.start_date) <= end.getTime();
    }) || [];

  const ptoSummary = filteredTasks.reduce((acc, curr) => {
    const count = curr.assignees.length;
    for (const user of curr.assignees) {
      //  Ferien = 0, Krankheit = 1, Kompensation = 2, Weiterbildung = 3, Militär/ZS = 4, Unfall = 5, Mutterschaft/Vaterschaft = 6, Feiertag = 7, Sonstige = 8, Freizeit = 9
      if (!acc[user.username]) {
        acc[user.username] = {
          estimated: 0,
          estimatedNotUsed: 0,
          used: 0,
          holiday: 0,
          sick: 0,
          accident: 0,
          military: 0,
          parent: 0,
          publicHoliday: 0,
          education: 0,
          other: 0,
        };
      }
      acc[user.username].estimated += (curr.time_estimate || 0) / count;
      acc[user.username].used += (curr.time_spent || 0) / count;

      const ptoOptions = curr?.custom_fields?.find((field) => field.name === 'PTO Typ')?.type_config?.options;
      const ptoTypeIndex = curr?.custom_fields?.find((field) => field.name === 'PTO Typ')?.value;
      const ptoTypeName = ptoOptions?.find((option) => Number(option.orderindex) === Number(ptoTypeIndex))?.name;

      acc[user.username].holiday += ptoTypeName === 'Ferien' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].sick += ptoTypeName === 'Krankheit' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].accident += ptoTypeName === 'Unfall' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].military += ptoTypeName === 'Militär/ZS' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].parent += ptoTypeName === 'Mutterschaft/Vaterschaft' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].publicHoliday += ptoTypeName === 'Feiertag' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].education += ptoTypeName === 'Weiterbildung' ? (curr.time_spent || 0) / count : 0;
      acc[user.username].other += ptoTypeName === 'Sonstige' ? (curr.time_spent || 0) / count : 0;
      const isClosed = curr.status.status === 'closed';
      acc[user.username].estimatedNotUsed += isClosed ? (curr.time_estimate || 0) / count : 0;
    }

    return acc;
  }, {} as PTOSummary);

  const saldoEndDate = customSaldoEndDate || new Date().setDate(new Date().getDate() - 1);
  const saldoEndDateMs = new Date(saldoEndDate).setHours(23, 59, 59, 999);
  const saldoStart = new Date('2023-01-01').setHours(0, 0, 0, 0);
  const filteredTasksSaldo =
    tasks.filter((task) => {
      return Number(task.start_date) >= saldoStart && Number(task.start_date) <= saldoEndDateMs;
    }) || [];

  const allPTOTillSaldoEnd = filteredTasksSaldo.reduce((acc, curr) => {
    const count = curr.assignees.length;
    for (const user of curr.assignees) {
      if (!acc[user.username]) {
        acc[user.username] = {
          estimated: 0,
          estimatedNotUsed: 0,
          used: 0,
        };
      }

      acc[user.username].estimated += (curr.time_estimate || 0) / count;
      acc[user.username].used += (curr.time_spent || 0) / count;
      const isClosed = curr.status.status === 'completed';
      acc[user.username].estimatedNotUsed += isClosed ? (curr.time_estimate || 0) / count : 0;
    }

    return acc;
  }, {} as any);

  const allHolidaySummary = allHolidayTasks.reduce((acc, curr) => {
    const count = curr.assignees.length;
    for (const user of curr.assignees) {
      if (!acc[user.username]) {
        acc[user.username] = { estimatedNotComplete: 0, estimated: 0, used: 0 };
      }
      if (curr.status.status === 'approved') {
        acc[user.username].estimatedNotComplete += (curr.time_estimate || 0) / count;
      }
      acc[user.username].estimated += (curr.time_estimate || 0) / count;
      acc[user.username].used += (curr.time_spent || 0) / count;
    }

    return acc;
  }, {} as any);

  return { ptoSummary, allPTOTillSaldoEnd, allHolidaySummary };
}

type FridgeSummary = {
  [key: string]: number;
};

export async function getFridgeTimes(fridgeListId: string, start: Date, end: Date) {
  const tasks: ClickUpTask[] = await getAllFridgeTimes(fridgeListId);
  const syntheticTimeEntries: ClickUpTimeEntry[] = [];

  const filteredTasks =
    tasks.filter((task) => {
      const taskStart = task.start_date ? new Date(Number(task.start_date)) : null;
      return taskStart && taskStart >= start && taskStart <= end;
    }) || [];

  let result = {} as FridgeSummary;
  filteredTasks.forEach((task) => {
    task.assignees.forEach((assignee) => {
      if (!(assignee.username in result)) {
        result[assignee.username] = 0;
      }

      if (task.time_spent) {
        syntheticTimeEntries.push({
          task: task,
          user: assignee,
          billable: false,
          start: task.start_date!,
          end: task.due_date!,
          duration: task.time_spent / task.assignees.length,
          description: 'Fridge Club',
          source: 'custom',
          task_url: task.url,
        } as unknown as ClickUpTimeEntry);
        result[assignee.username] += task.time_spent / task.assignees.length / 1000 / 60 / 60;
      }
    });
  });

  return syntheticTimeEntries;
}

export async function getSummedPTOTimes(start: Date, end: Date, ptoListId?: string) {
  if (!ptoListId) {
    const globalSettings = await getGlobalSettings();
    ptoListId = String(globalSettings.data?.pto_list_id);
  }

  const tasks: ClickUpTask[] = await getAllPTOTimes(ptoListId);

  const filteredTasks =
    tasks.filter((task) => {
      return Number(task.due_date) >= start.getTime() && Number(task.start_date) <= end.getTime();
    }) || [];

  return filteredTasks.reduce((acc, curr) => {
    return acc + (curr.time_spent || 0);
  }, 0);
}
