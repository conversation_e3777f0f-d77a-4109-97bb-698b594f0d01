'use server';

import { ClickUpTimeEntry } from '@/data/types/clickup.types';
import { getISODateString } from '@/lib/utils';
import { getWorkingHours } from '@/server-actions/time-reporting/simple-report';
import { getSimpleManualCorrections } from '@/server-actions/time-reporting/helpers/manual-corrections';

type SaldoOverHours = {
  [key: string]: number;
};

export async function getSaldoOverHours(
  clickupEntries: ClickUpTimeEntry[],
  ptoTillSaldoEnd: any,
  saldoEndDate?: Date,
): Promise<SaldoOverHours> {
  const startDate = new Date(new Date('2023-01-01').setHours(0, 0, 0, 0));
  const endDate =
    saldoEndDate || new Date(new Date(new Date().setDate(new Date().getDate() - 1)).setHours(23, 59, 59, 999));

  const { data: workingHours } = await getWorkingHours(getISODateString(startDate), getISODateString(endDate));
  const simpleManCor = await getSimpleManualCorrections(startDate, endDate);

  const totalHours = clickupEntries.reduce(
    (acc, curr) => {
      if (!curr.user.username) {
        return acc;
      }

      // if not in time range
      if (Number(curr.start) < startDate.getTime() || Number(curr.start) > endDate.getTime()) {
        return acc;
      }

      const name = String(curr.user.username);
      if (!acc[name]) {
        acc[name] = 0;
      }

      acc[name] += Number(curr.duration);
      return acc;
    },
    {} as { [key: string]: number },
  );

  const workingHoursSummary =
    workingHours?.reduce(
      (acc, curr) => {
        if (!curr.employees?.name || curr.employees?.inactive) {
          return acc;
        }

        const name = String(curr.employees?.name);
        if (!acc[name]) {
          acc[name] = curr.hours;
        } else {
          acc[name] += curr.hours;
        }
        return acc;
      },
      {} as { [key: string]: number },
    ) || {};

  return Object.keys(workingHoursSummary).reduce((acc, curr) => {
    acc[curr] =
      (totalHours[curr] || 0) / 1000 / 60 / 60 +
      (ptoTillSaldoEnd[curr]?.used || 0) / 1000 / 60 / 60 +
      (simpleManCor[curr]?.nonPto || 0) -
      (workingHoursSummary[curr] || 0);
    return acc;
  }, {} as SaldoOverHours);
}
