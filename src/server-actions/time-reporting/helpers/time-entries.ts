'use server';

import { createClient } from '@/data/supabase-server';
import { QueryData } from '@supabase/supabase-js';

type TimeEntriesDB = QueryData<ReturnType<typeof getTimeEntriesFromDB>>;

export async function getTimeEntriesFromDB(start: Date, end: Date) {
  const supabase = createClient();
  return supabase
    .from('time_entries')
    .select('*, employees(clickup_user_id, name, inactive), tasks(clickup_list_id)')
    .gte('clickup_start', start.getTime())
    .lte('clickup_start', end.getTime());
}

type TimeEntriesSummary = {
  [key: string]: {
    billableHours: number;
    nonBillableHours: number;
    emptyHours: number;
    id: string;
  };
};

export async function getTimeEntries(
  startRange: Date,
  endRange: Date,
  ptoListId: string,
): Promise<{
  timeEntriesSummary: TimeEntriesSummary;
}> {
  const { data: timeEntriesData } = await getTimeEntriesFromDB(startRange, endRange);
  const timeEntries: TimeEntriesDB =
    timeEntriesData?.filter((entry) => entry.tasks?.clickup_list_id !== ptoListId) || [];

  const summary =
    timeEntries?.reduce((acc, curr) => {
      if (!curr.employees?.name || curr.employees?.inactive) {
        return acc;
      }

      const name = String(curr.employees?.name);
      if (!acc[name]) {
        acc[name] = {
          billableHours: 0,
          nonBillableHours: 0,
          emptyHours: 0,
          id: String(curr.employees?.clickup_user_id),
        };
      }

      if (curr.billable && curr.clickup_task_id) {
        acc[name].billableHours += curr.clickup_duration || 0;
      } else if (!curr.billable && curr.clickup_task_id) {
        acc[name].nonBillableHours += curr.clickup_duration || 0;
      } else {
        acc[name].emptyHours += curr.clickup_duration || 0;
      }

      return acc;
    }, {} as TimeEntriesSummary) || {};

  return {
    timeEntriesSummary: summary,
  };
}
