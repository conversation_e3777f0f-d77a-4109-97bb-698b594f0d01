export function millisToHours(milli?: number) {
  if (!milli) {
    return 0;
  }

  if (isNaN(Number(milli))) {
    return 0;
  }

  return Number(milli) / 1000 / 60 / 60;
}

export function hoursToDays(hours?: number) {
  if (!hours) {
    return 0;
  }

  if (isNaN(Number(hours))) {
    return 0;
  }

  return Number(hours) / 8.6;
}

export function millisToDays(milli?: number) {
  if (!milli) {
    return 0;
  }

  if (isNaN(Number(milli))) {
    return 0;
  }

  return Number(milli) / 1000 / 60 / 60 / 8.6;
}
