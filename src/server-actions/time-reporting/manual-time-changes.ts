'use server';

import { MTCFSubmission } from '@/app/working-hours/_components/manual-time-changes-form';
import { createClient } from '@/data/supabase-server';

export async function saveManualTimeChange(timeChange: MTCFSubmission) {
  const supabase = createClient();

  if (timeChange.id) {
    return supabase.from('time_corrections').update(timeChange).eq('id', timeChange.id);
  }

  return supabase.from('time_corrections').insert(timeChange);
}

export async function deleteManualTimeChange(id: number) {
  const supabase = createClient();
  return supabase.from('time_corrections').delete().eq('id', id);
}

export async function getManualTimeChanges(userId: string) {
  const supabase = createClient();
  return supabase
    .from('time_corrections')
    .select('*, employees(*)')
    .eq('user_id', userId)
    .order('id', { ascending: false });
}
