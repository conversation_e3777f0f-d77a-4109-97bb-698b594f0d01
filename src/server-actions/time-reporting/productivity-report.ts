'use server';

import { createClient } from '@/data/supabase-server';
import { PostgrestError } from '@supabase/supabase-js';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { getSummedPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';

type ProductivityReport = {
  billableTime: number;
  nonBillableTime: number;
  timeSpent: number;
};

export async function getProductivityReport(
  start: Date,
  end: Date,
  userIds: string[],
): Promise<{ error: PostgrestError } | { data: ProductivityReport }> {
  const startDate = new Date(start);
  const endDate = new Date(new Date(end).setHours(23, 59, 59, 999));

  const supabase = createClient();
  const result = await supabase
    .from('time_entries')
    .select('*, tasks(clickup_list_id)')
    .gte('clickup_start', startDate.getTime())
    .lte('clickup_end', endDate.getTime())
    .in('clickup_user_id', userIds);

  if (result.error) {
    return { error: result.error };
  }

  const globalSettings = await getGlobalSettings();
  const ptoListId = String(globalSettings.data?.pto_list_id);

  const report = result.data.reduce((acc, curr) => {
    if (String(curr.tasks?.clickup_list_id) == ptoListId) {
      return acc;
    }

    const duration = curr.clickup_duration || 0;
    if (!('billableTime' in acc)) {
      return {
        billableTime: curr.billable ? duration : 0,
        nonBillableTime: curr.billable ? 0 : duration,
        timeSpent: duration,
      };
    }

    return {
      billableTime: acc.billableTime + (curr.billable ? duration : 0),
      nonBillableTime: acc.nonBillableTime + (curr.billable ? 0 : duration),
      timeSpent: acc.timeSpent + duration,
    };
  }, {} as ProductivityReport);

  const ptoHrs = await getSummedPTOTimes(startDate, endDate);
  report.nonBillableTime += ptoHrs;
  report.timeSpent += ptoHrs;

  return { data: report };
}
