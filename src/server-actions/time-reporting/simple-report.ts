'use server';

import { createClient } from '@/data/supabase-server';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTimeEntry, isClickUpError } from '@/data/types/clickup.types';
import { cache } from '@/lib/cache';
import { getISODateString } from '@/lib/utils';
import { QueryData } from '@supabase/supabase-js';
import { getSaldoOverHours } from '@/server-actions/time-reporting/helpers/saldo-over-hours';
import { getFullManualCorrections } from '@/server-actions/time-reporting/helpers/manual-corrections';
import { getOvertime } from '@/server-actions/time-reporting/helpers/overtime';
import { getTimeEntries } from '@/server-actions/time-reporting/helpers/time-entries';
import { getFridgeTimes, getPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';
import { hoursToDays, millisToDays, millisToHours } from '@/server-actions/time-reporting/helpers/utils';

export type WorkingHours = QueryData<ReturnType<typeof getWorkingHours>>;

export async function getWorkingHours(start: string, end: string) {
  const supabase = createClient();
  return supabase
    .from('working_hours')
    .select('*, employees(name, inactive, active_time_report)')
    .gte('date', start)
    .lte('date', end);
}

export async function getPTOCredits() {
  const supabase = createClient();
  return supabase.from('pto_credits').select('*, employees(name, inactive, active_time_report)');
}

export async function getEmployees() {
  const supabase = createClient();
  const { data } = await supabase.from('employees').select('*').order('clickup_user_id', { ascending: true });
  return data;
}

export const getAllClickupTimeEntries = (ptoListId: string) =>
  cache<ClickUpTimeEntry[]>(
    'fetch-all-time-entries',
    async () => {
      const employees = (await getEmployees()) || [];
      const userIds = employees?.map((employee) => String(employee.clickup_user_id));

      const clickupClient = new ClickUpClient();
      const timeEntries = await clickupClient.getAllTimeEntries(userIds);

      if (isClickUpError(timeEntries)) {
        throw Error(`Error fetching time entries: ${JSON.stringify(timeEntries)}`);
      }

      const timeEntriesDurationNulled = timeEntries.data.map((timeEntry) => {
        if (!timeEntry.duration) {
          return {
            ...timeEntry,
            duration: timeEntry.duration || 0,
          } as ClickUpTimeEntry;
        }
        return timeEntry;
      });

      return timeEntriesDurationNulled.filter((entry) => String(entry.task_location?.list_id) !== ptoListId);
    },
    600,
  );

export async function getSimpleTimeReportAction(
  startDate: string,
  endDate: string,
  start: Date,
  end: Date,
  userIds?: string[],
  customSaldoEndDate?: string,
) {
  if (userIds && userIds.length === 0) {
    return { data: [] };
  }

  console.log('getSimpleTimeReportAction', startDate, endDate, start, end, customSaldoEndDate);

  startDate = getISODateString(new Date(startDate));
  endDate = getISODateString(new Date(endDate));
  start = new Date(new Date(startDate).setHours(0, 0, 0, 0));
  end = new Date(new Date(endDate).setHours(23, 59, 59, 999));
  console.log('getSimpleTimeReportActionV2', startDate, endDate, start, end, customSaldoEndDate);
  console.log('TS', start.getTime(), end.getTime());
  const globalSettings = await getGlobalSettings();
  const ptoListId = String(globalSettings.data?.pto_list_id);
  const calcStart = new Date('2023-01-01');
  const yesterdayString = getISODateString(new Date(new Date().setDate(new Date().getDate() - 1)));
  const saldoEndDate = customSaldoEndDate || yesterdayString;
  const saldoEnd = new Date(new Date(saldoEndDate).setHours(23, 59, 59, 999));
  console.log('SALDO END DATE', saldoEndDate, saldoEnd);
  const employees = await getEmployees();
  const employeeList =
    employees
      ?.filter((e) => e.active_time_report)
      ?.map((e) => {
        return { key: e.name, id: e.clickup_user_id };
      }) || [];

  const { timeEntriesSummary } = await getTimeEntries(start, end, ptoListId);
  let clickupEntries: ClickUpTimeEntry[] = await getAllClickupTimeEntries(ptoListId);
  const fridgeTimeEntries = await getFridgeTimes(globalSettings.data?.fridge_club_list_id!, start, end);
  clickupEntries = clickupEntries.concat(fridgeTimeEntries);

  const { data: workingHours } = await getWorkingHours(startDate, endDate);
  const { data: saldoWorkingHours } = await getWorkingHours('2024-01-01', saldoEndDate); // saldo starts from 2024 (bc its only used for overtime calc)
  const { data: ptoCredits } = await getPTOCredits();
  const manCorrSummary = await getFullManualCorrections(calcStart, saldoEnd, startDate, endDate);
  const { ptoSummary, allPTOTillSaldoEnd, allHolidaySummary } = await getPTOTimes(ptoListId, start, end, saldoEnd);

  const workingHoursSummary = workingHours?.reduce((acc, curr) => {
    if (!curr.employees?.name || !curr.employees?.active_time_report) {
      return acc;
    }

    const name = String(curr.employees?.name);
    if (!acc[name]) {
      acc[name] = curr.hours;
    } else {
      acc[name] += curr.hours;
    }
    return acc;
  }, {} as any);

  const totalHoursSummary = clickupEntries.reduce((acc, curr) => {
    if (!curr.user.username || Number(curr.start) < start.getTime() || Number(curr.start) > end.getTime()) {
      return acc;
    }

    const name = String(curr.user.username);
    if (!acc[name]) {
      acc[name] = 0;
    }

    acc[name] += Number(curr.duration);
    return acc;
  }, {} as any);

  const first2024 = new Date(new Date('2024-01-01').setHours(0, 0, 0, 0));
  const totalOvertime = await getOvertime(clickupEntries, saldoWorkingHours, ptoListId, first2024, saldoEnd);
  const overHoursSaldo = await getSaldoOverHours(clickupEntries, allPTOTillSaldoEnd, saldoEnd);

  const result = employeeList.map(({ key, id }) => {
    const manCorrNonPTOTf = Number(manCorrSummary[key]?.nonPtoTf || 0);
    const ptoHoursInMs = ptoSummary[key]?.used || 0;
    const totalClickupHours = totalHoursSummary[key] || 0;

    const clickupTimeHours = millisToHours(Number(totalClickupHours + ptoHoursInMs));
    const totalHours = millisToHours(Number(totalClickupHours + ptoHoursInMs)) + manCorrNonPTOTf;

    // total pto credits
    const ptoCreditsInDays =
      (ptoCredits
        ?.filter((pto) => pto.employees?.name === key)
        .reduce((acc, curr) => {
          acc += curr.hours;
          return acc;
        }, 0) || 0) / 8.6;
    // pto type holiday used in time frame
    const tfUsedPTOInHrs = millisToHours(ptoSummary[key]?.holiday || 0);
    // negative man correction count as used pto (-- = +)
    const tfUsedPTOInDays = hoursToDays(tfUsedPTOInHrs - Number(manCorrSummary[key]?.ptoNegativeTf || 0));
    // (allHolidaySummary[key]?.used - only negative manCorrPTO)
    const totalUsedPTOInDays = hoursToDays(
      millisToHours(allHolidaySummary[key]?.used || 0) - (Number(manCorrSummary[key]?.negativePto || 0) || 0),
    );
    const manPosPtoCorrInDays = hoursToDays(Number(manCorrSummary[key]?.positivePto || 0));
    const productivity = timeEntriesSummary[key]?.billableHours
      ? Number(
          (timeEntriesSummary[key]?.billableHours /
            (timeEntriesSummary[key]?.billableHours + timeEntriesSummary[key]?.nonBillableHours)) *
            100,
        )
      : 0;

    return {
      id: String(timeEntriesSummary[key]?.id || id),
      name: key,
      billableHours: millisToHours(timeEntriesSummary[key]?.billableHours).toFixed(2),
      nonBillableHours: millisToHours(timeEntriesSummary[key]?.nonBillableHours).toFixed(2),
      emptyHours: millisToHours(timeEntriesSummary[key]?.emptyHours).toFixed(2),
      productivity: productivity.toFixed(2),
      totalHours: totalHours.toFixed(2),
      clickupHours: clickupTimeHours.toFixed(2),
      workingHours: workingHoursSummary[key] ? Number(workingHoursSummary[key]).toFixed(2) : '0',
      overHours: (totalHours - (workingHoursSummary[key] ? workingHoursSummary[key] : 0)).toFixed(2),
      overHoursSaldo: overHoursSaldo[key] ? Number(overHoursSaldo[key]).toFixed(2) : '0',
      overtime: totalOvertime[key] ? Number(totalOvertime[key]).toFixed(2) : '0',
      ptoCreditsSaldo: Number(ptoCreditsInDays + manPosPtoCorrInDays - totalUsedPTOInDays).toFixed(2),
      usedPTO: tfUsedPTOInDays.toFixed(2),
      sickPTO: millisToDays(ptoSummary[key]?.sick || 0).toFixed(2),
      militaryPTO: millisToDays(ptoSummary[key]?.military || 0).toFixed(2),
      parentPTO: millisToDays(ptoSummary[key]?.parent || 0).toFixed(2),
      accidentPTO: millisToDays(ptoSummary[key]?.accident || 0).toFixed(2),
      educationPTO: millisToDays(ptoSummary[key]?.education || 0).toFixed(2),
      publicHolidayPTO: millisToDays(ptoSummary[key]?.publicHoliday || 0).toFixed(2),
      estimatedNotUsedPTO: millisToDays(allHolidaySummary[key]?.estimatedNotComplete || 0).toFixed(2),
      otherPTO: millisToDays(ptoSummary[key]?.other || 0).toFixed(2),
    };
  });

  if (userIds) {
    return { data: result.filter((summary) => userIds.includes(summary.id)) };
  }

  return { data: result };
}
