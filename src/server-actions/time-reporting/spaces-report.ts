'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

type SpacesReport = {
  spaces: { [key: string]: { name: string; timeSpent: number } };
  totalTimeSpent: number;
};

export async function getSpacesReport(
  start: Date,
  end: Date,
  userIds: string[],
): Promise<{ error: any } | { data: SpacesReport }> {
  const startTimer = new Date().getTime();
  const clickupClient = new ClickUpClient();
  const spaces = await clickupClient.getSpaces();
  const endTimer1 = new Date().getTime();
  console.log('fetchReportData Fetching', endTimer1 - startTimer);

  if (isClickUpError(spaces)) {
    return { error: spaces.err };
  }

  const selectedUserIds = userIds.join(',');
  const timeEntries = await clickupClient.getAllTimeEntriesRange(selectedUserIds, start.getTime(), end.getTime());
  const endTimer2 = new Date().getTime();
  console.log('fetchReportData Fetching2', endTimer2 - endTimer1);

  if (isClickUpError(timeEntries)) {
    return { error: timeEntries.err };
  }

  // Step 1: Preprocess spaces into a map for faster lookups
  const spacesMap = new Map(spaces.spaces.map((space) => [String(space.id), { name: space.name, timeSpent: 0 }]));

  // Step 2: Accumulate time spent in each space
  timeEntries.data.forEach((entry) => {
    const spaceId = String(entry.task_location?.space_id);
    if (spacesMap.has(spaceId)) {
      spacesMap.get(spaceId)!.timeSpent += Number(entry.duration);
    }
  });

  // Step 3: Convert the map back to an object
  const timeBySpaces = Object.fromEntries(spacesMap);

  // Step 4: Calculate total time spent
  const totalTimeSpent = Object.values(timeBySpaces).reduce((acc, curr) => acc + curr.timeSpent, 0);

  const endTimer3 = new Date().getTime();
  console.log('fetchReportData Processing', endTimer3 - endTimer2);
  return { data: { spaces: timeBySpaces, totalTimeSpent } };
}
