'use server';

import { createClient } from '@/data/supabase-server';

type TimeLabelsReport = {
  labels: { [key: string]: { name: string; timeSpent: number } };
  totalTimeSpent: number;
};

export async function getTimeLabelsReport(
  start: Date,
  end: Date,
  userIds: string[],
): Promise<{ error: any } | { data: TimeLabelsReport }> {
  const supabase = createClient();
  const { data: labels, error: labelsError } = await supabase.from('business_activities').select('*');
  const { data: timeEntries, error } = await supabase
    .from('time_entries')
    .select('clickup_duration, business_activities(*)')
    .gte('clickup_start', start.getTime())
    .lte('clickup_end', end.getTime())
    .in('clickup_user_id', userIds)
    .eq('billable', true);

  if (labelsError || error) {
    console.error(labelsError, error);
    return { error: labelsError || error };
  }

  // Step 1: Preprocess labels into a map for faster lookups
  const labelsMap = new Map(
    labels.map((label) => [String(label.clickup_task_tag), { name: label.bexio_business_activity_name, timeSpent: 0 }]),
  );

  // Step 2: Accumulate time spent for each label
  timeEntries.forEach((entry) => {
    const labelId = String(entry.business_activities?.clickup_task_tag);
    if (labelsMap.has(labelId)) {
      labelsMap.get(labelId)!.timeSpent += Number(entry.clickup_duration);
    }
  });

  // Step 3: Convert the map back to an object
  const timeByLabel = Object.fromEntries(labelsMap);

  // Step 4: Calculate total time spent
  const totalTimeInHrs = Object.values(timeByLabel).reduce((acc, curr) => acc + curr.timeSpent, 0);

  return { data: { labels: timeByLabel, totalTimeSpent: totalTimeInHrs } };
}
