'use server';

import { createClient } from '@/data/supabase-server';
import { PostgrestError } from '@supabase/supabase-js';
import { isGenericError, msToHours } from '@/lib/utils';
import { cache } from 'react';
import { getSummedPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';

type Top10Report = {
  projects: Top10Project[];
  totalTimeInHours: number;
};

type Top10Project = {
  name: string;
  timeInHours: number;
};

export const getTop10GlobalReport = cache(
  async (
    start: Date,
    end: Date,
    userIds: string[],
    onlyClients: boolean | null = null,
  ): Promise<{ error: PostgrestError } | { data: Top10Report }> => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const projects = await fetchReportData(startDate, endDate, userIds);
    if (isGenericError(projects)) {
      return { error: projects.error };
    }

    const filteredProjects = projects.filter(
      (project) => onlyClients === null || project.isClientProject === onlyClients,
    );
    let totalTimeInHours = filteredProjects.reduce((acc, project) => acc + project.timeInHours, 0);
    let top10Projects = filteredProjects.sort((a, b) => b.timeInHours - a.timeInHours).slice(0, 10);
    const hasPTOProject = top10Projects.some((project) => project.name === 'PTO Kalender');

    if (hasPTOProject) {
      const ptoHrs = await getSummedPTOTimes(startDate, endDate);
      for (const project of top10Projects) {
        if (project.name === 'PTO Kalender') {
          totalTimeInHours = totalTimeInHours - project.timeInHours + ptoHrs / 1000 / 60 / 60;
          project.timeInHours = ptoHrs / 1000 / 60 / 60;
        }
      }
      top10Projects = filteredProjects.sort((a, b) => b.timeInHours - a.timeInHours);
    }

    return { data: { projects: top10Projects, totalTimeInHours } };
  },
);

// Preprocess time entries by task ID
const preprocessTimeEntries = (timeEntriesData: any) => {
  const timeEntriesMap = new Map();
  for (const timeEntry of timeEntriesData) {
    const taskId = String(timeEntry.clickup_task_id);
    if (!timeEntriesMap.has(taskId)) {
      timeEntriesMap.set(taskId, []);
    }
    timeEntriesMap.get(taskId).push(timeEntry);
  }
  return timeEntriesMap;
};

// Preprocess tasks by list ID
const preprocessTasks = (tasksData: any) => {
  const tasksMap = new Map();
  for (const task of tasksData) {
    const listId = task.clickup_list_id;
    if (!tasksMap.has(listId)) {
      tasksMap.set(listId, []);
    }
    tasksMap.get(listId).push(task);
  }
  return tasksMap;
};

const fetchReportData = cache(async (start: Date, end: Date, userIds: string[]) => {
  const supabase = createClient();
  const startTimer = new Date().getTime();
  const { data: projectsData, error: projectsError } = await supabase
    .from('projects')
    .select('name, is_client_project, clickup_list_id');
  const endTimer1 = new Date().getTime();

  if (projectsError) {
    return { error: projectsError };
  }

  const startTimer2 = new Date().getTime();
  const { data: tasksData, error: tasksError } = await supabase
    .from('tasks')
    .select('clickup_task_id, clickup_list_id');

  const endTimer2 = new Date().getTime();
  if (tasksError) {
    return { error: tasksError };
  }

  const startTimer3 = new Date().getTime();
  const { data: timeEntriesData, error: timeEntriesError } = await supabase
    .from('time_entries')
    .select('clickup_task_id, clickup_duration, clickup_start, clickup_user_id');
  const endTimer3 = new Date().getTime();

  if (timeEntriesError) {
    return { error: timeEntriesError };
  }

  console.log('fetchReportData Fetching', endTimer1 - startTimer, endTimer2 - startTimer2, endTimer3 - startTimer3);
  const startTimer4 = new Date().getTime();
  // const projects =
  //   projectsData.map((project) => {
  //     const taskIds = tasksData
  //       .filter((task) => task.clickup_list_id === project.clickup_list_id)
  //       .map((task) => String(task.clickup_task_id));
  //     const timeEntries = timeEntriesData
  //       .filter((timeEntry) => taskIds.includes(String(timeEntry.clickup_task_id)))
  //       .filter(
  //         (timeEntry) =>
  //           Number(timeEntry.clickup_start) <= end.getTime() && Number(timeEntry.clickup_start) >= start.getTime(),
  //       )
  //       .filter((timeEntry) => userIds.includes(String(timeEntry.clickup_user_id)));
  //     const totalTimeInHours = timeEntries.reduce((acc, task) => acc + msToHours(task.clickup_duration), 0);
  //
  //     return {
  //       name: String(project.name),
  //       isClientProject: project.is_client_project,
  //       timeInHours: totalTimeInHours,
  //     };
  //   }) || [];
  const endTimer4 = new Date().getTime();

  const timeEntriesMap = preprocessTimeEntries(timeEntriesData);
  const tasksMap = preprocessTasks(tasksData);
  const projects =
    projectsData.map((project) => {
      const listId = project.clickup_list_id;
      const taskIds = (tasksMap.get(listId) || []).map((task: any) => String(task.clickup_task_id));

      const timeEntries = [];
      for (const taskId of taskIds) {
        const entries = timeEntriesMap.get(taskId) || [];
        for (const entry of entries) {
          if (
            Number(entry.clickup_start) <= end.getTime() &&
            Number(entry.clickup_start) >= start.getTime() &&
            userIds.includes(String(entry.clickup_user_id))
          ) {
            timeEntries.push(entry);
          }
        }
      }
      const totalTimeInHours = timeEntries.reduce((acc, timeEntry) => acc + msToHours(timeEntry.clickup_duration), 0);

      return {
        name: String(project.name),
        isClientProject: project.is_client_project,
        timeInHours: totalTimeInHours,
      };
    }) || [];

  const endTimer5 = new Date().getTime();

  console.log('fetchReportData Processing', endTimer4 - startTimer4);
  console.log('fetchReportData Processing2', endTimer5 - endTimer4);
  return projects;
});
