'use server';

import { createClient } from '@/data/supabase-server';
import { PostgrestError } from '@supabase/supabase-js';
import { isGenericError, msToHours } from '@/lib/utils';
import { cache } from 'react';

type Top30Report = {
  tasks: Top30Task[];
  totalTimeInHours: number;
};

type Top30Task = {
  name: string;
  timeInHours: number;
};

export async function getTop30TasksReport(
  start: Date,
  end: Date,
  userIds: string[],
): Promise<{ error: PostgrestError } | { data: Top30Report }> {
  const startDate = new Date(start);
  const endDate = new Date(end);

  const tasks = await fetchTasksReportData(startDate, endDate, userIds);

  if (isGenericError(tasks)) {
    return { error: tasks.error };
  }

  const totalTimeInHours = tasks.reduce((acc, project) => acc + project.timeInHours, 0);

  const top30Tasks = tasks.sort((a, b) => b.timeInHours - a.timeInHours).slice(0, 30);

  return { data: { tasks: top30Tasks, totalTimeInHours } };
}

const preprocessTimeEntries = (timeEntriesData: any) => {
  const timeEntriesMap = new Map();

  for (const timeEntry of timeEntriesData) {
    const taskId = String(timeEntry.clickup_task_id);
    if (!timeEntriesMap.has(taskId)) {
      timeEntriesMap.set(taskId, []);
    }
    timeEntriesMap.get(taskId).push(timeEntry);
  }

  return timeEntriesMap;
};

const fetchTasksReportData = cache(
  async (
    start: Date,
    end: Date,
    userIds: string[],
  ): Promise<{ error: PostgrestError } | { name: string; timeInHours: number }[]> => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const supabase = createClient();
    const startTimer = new Date().getTime();
    const { data: tasksData, error: tasksError } = await supabase.from('tasks').select('name, clickup_task_id');
    const endTimer1 = new Date().getTime();
    if (tasksError) {
      return { error: tasksError };
    }

    const startTimer2 = new Date().getTime();
    const { data: timeEntriesData, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('clickup_task_id, clickup_duration, clickup_start, clickup_user_id');
    const endTimer2 = new Date().getTime();
    if (timeEntriesError) {
      return { error: timeEntriesError };
    }

    console.log('fetchTasksReportData Fetching', endTimer1 - startTimer, endTimer2 - startTimer2);

    const startTimer3 = new Date().getTime();
    // const tasks = tasksData.map((task) => {
    //   const timeEntries = timeEntriesData
    //     .filter((timeEntry) => String(timeEntry.clickup_task_id) === String(task.clickup_task_id))
    //     .filter(
    //       (timeEntry) =>
    //         Number(timeEntry.clickup_start) <= endDate.getTime() &&
    //         Number(timeEntry.clickup_start) >= startDate.getTime(),
    //     )
    //     .filter((timeEntry) => userIds.includes(String(timeEntry.clickup_user_id)));
    //   const totalTimeInHours = timeEntries.reduce((acc, task) => acc + msToHours(task.clickup_duration), 0);
    //
    //   return {
    //     name: String(task.name),
    //     timeInHours: totalTimeInHours,
    //   };
    // });
    const endTimer3 = new Date().getTime();
    const timeEntriesMap = preprocessTimeEntries(timeEntriesData);

    const tasks = tasksData.map((task) => {
      const taskId = String(task.clickup_task_id);
      const timeEntries = (timeEntriesMap.get(taskId) || []).filter(
        (timeEntry: any) =>
          Number(timeEntry.clickup_start) <= endDate.getTime() &&
          Number(timeEntry.clickup_start) >= startDate.getTime() &&
          userIds.includes(String(timeEntry.clickup_user_id)),
      );

      const totalTimeInHours = timeEntries.reduce(
        (acc: number, timeEntry: any) => acc + msToHours(timeEntry.clickup_duration),
        0,
      );

      return {
        name: String(task.name),
        timeInHours: totalTimeInHours,
      };
    });
    const endTimer4 = new Date().getTime();

    console.log('fetchTasksReportData Processing', endTimer3 - startTimer3);
    console.log('fetchTasksReportData Processing2', endTimer4 - endTimer3);

    return tasks;
  },
);
