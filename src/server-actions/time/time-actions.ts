'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { createClient } from '@/data/supabase-server';

export async function deleteClickupTimeEntry(entryId: string) {
  const clickupClient = new ClickUpClient();
  return clickupClient.deleteTimeEntry(entryId);
}

export async function deleteTimeEntry(timeEntryId: string) {
  const clickupResponse = await deleteClickupTimeEntry(timeEntryId);

  if (isClickUpError(clickupResponse)) {
    return clickupResponse;
  }

  const supabase = createClient();
  const res = await supabase.from('time_entries').delete().eq('clickup_time_entry_id', timeEntryId);

  if (res.error) {
    return res;
  }

  return clickupResponse;
}
