'use server';

import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';
import { revalidatePath } from 'next/cache';

export async function getWebhooksAction() {
  const clickUpClient = new ClickUpClient();
  const webhooks = await clickUpClient.getWebhooks();

  if (isClickUpError(webhooks)) {
    return { error: webhooks };
  }

  return { data: webhooks.webhooks };
}

export async function createWebhookAction(spaceId: string) {
  const clickUpClient = new ClickUpClient();
  const events = [
    'folderUpdated',
    'folderDeleted',
    'listCreated',
    'listUpdated',
    'listDeleted',
    'taskCreated',
    'taskUpdated',
    'taskDeleted',
  ];
  const webhook = await clickUpClient.createWebhookForSpace(Number(spaceId), events);

  if (isClickUpError(webhook)) {
    return { error: webhook };
  }

  revalidatePath('/settings');
}

export async function deleteWebhook(webhookId: string) {
  const clickUpClient = new ClickUpClient();
  const response = await clickUpClient.deleteWebhook(webhookId);

  if (isClickUpError(response)) {
    return { error: response };
  }

  revalidatePath('/settings');
}
