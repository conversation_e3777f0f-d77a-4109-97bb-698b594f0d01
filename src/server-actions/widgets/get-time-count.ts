'use server';

import { createClient } from '@/data/supabase-server';

export async function getTimeCountAction() {
  const supabase = createClient();
  const { data, error } = await supabase.from('time_entries').select('clickup_duration, billable, bexio_timesheet_id');

  if (error || !data) {
    return { error };
  }

  const timeInMs = data?.reduce((acc, task) => acc + (task.clickup_duration || 0), 0) || 0;
  const timeInHours = timeInMs / 1000 / 60 / 60;

  // @ts-ignore
  const billableTimeInMs = data?.reduce((acc, task) => acc + (task.billable ? task.clickup_duration : 0), 0) || 0;
  const billableTimeInHours = billableTimeInMs / 1000 / 60 / 60;

  const billableSyncedTimeInMs =
    // @ts-ignore
    data?.reduce((acc, task) => acc + (task.billable && task.bexio_timesheet_id ? task.clickup_duration : 0), 0) || 0;
  const billableSyncedTimeInHours = billableSyncedTimeInMs / 1000 / 60 / 60;

  return {
    data: {
      total: timeInHours.toFixed(2),
      billable: billableTimeInHours.toFixed(2),
      billableSynced: billableSyncedTimeInHours.toFixed(2),
    },
  };
}
