'use server';

import { createClient } from '@/data/supabase-server';

export async function getPTO() {
  const supabase = createClient();
  return supabase.from('pto_credits').select('*, employees(*)').eq('employees.inactive', false);
}

export async function getUsedPTO(startDate?: string, endDate?: string) {
  const startOfThisYearUnix = new Date(new Date().getFullYear(), 0, 1).getTime();
  const start = startDate || startOfThisYearUnix;
  const end = endDate || new Date().getTime();
  const supabase = createClient();
  const { data, error } = await supabase
    .from('global_settings')
    .select('id, projects:pto_list_id(tasks(time_entries(*, employees(*))))')
    .eq('id', 1)
    .gte('projects.tasks.time_entries.clickup_start', start)
    .lte('projects.tasks.time_entries.clickup_start', end)
    .single();

  if (error) {
    return { error };
  }

  // @ts-ignore
  const response = data.projects?.tasks?.flatMap((task: any) => task.time_entries);
  return response?.reduce(
    (acc: any, curr: any) => {
      if (!curr.employees?.name || curr.employees?.inactive) {
        return acc;
      }

      const name = String(curr.employees?.name);
      if (!acc[name] && acc[name] != 0) {
        acc[name] = curr.clickup_duration;
      } else {
        acc[name] += curr.clickup_duration;
      }

      return acc;
    },
    {} as { [key: string]: number },
  );
}
