'use server';

import { createClient } from '@/data/supabase-server';

export async function getWorkingHours(userId: string) {
  if (!userId) {
    return { error: 'No user id provided' };
  }

  const supabase = createClient();
  const { data, error } = await supabase.from('working_hours').select('*').eq('user_id', userId).neq('hours', 0);

  if (error) {
    return { error };
  }

  const workingHours = new Map<string, { created_at: string; date: string; hours: number; user_id: string }[]>();
  data?.forEach((workingHour) => {
    const date = new Date(workingHour.date);
    const key = `${date.getFullYear()}-${date.getMonth() + 1}`;
    const oldEntry = workingHours.get(key);
    const entry = oldEntry ? oldEntry.concat([workingHour]) : [workingHour];
    workingHours.set(key, entry);
  });

  return workingHours;
}
