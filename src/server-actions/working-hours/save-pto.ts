'use server';

import { PTOSubmission } from '@/app/working-hours/_components/pto-form';
import { createClient } from '@/data/supabase-server';

export async function savePTO(submission: PTOSubmission) {
  const upsertData = {
    user_id: submission.user_id,
    year: submission.year,
    hours: submission.hours * submission.days,
  };

  const supabase = createClient();
  return supabase.from('pto_credits').upsert(upsertData, { onConflict: 'user_id, year' });
}
