'use server';

import { supabaseServiceClient } from '@/data/supabase-server';
import { WorkingHoursSubmission } from '@/app/working-hours/_components/working-hours-form';

export async function saveWorkingHoursAction(workingHoursSubmission: WorkingHoursSubmission) {
  const dates = [];
  let currentDate = workingHoursSubmission.start;
  const endDate = workingHoursSubmission.end;
  while (currentDate <= endDate) {
    if (workingHoursSubmission.weekdays.includes(currentDate.getDay())) {
      dates.push({
        date: new Date(currentDate),
        hours: workingHoursSubmission.hours,
        user_id: workingHoursSubmission.user_id,
      });
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  console.log('DATES');
  console.log(dates);

  return supabaseServiceClient().from('working_hours').upsert(dates, { onConflict: 'date,user_id' });
}
