// Core data structure for tracking operations
export interface SyncOperation {
  id: string; // Unique operation ID
  origin: 'CLICKUP' | 'ODOO'; // Where the change originated
  entity: string; // e.g., "task", "time_entry"
  entity_id: string; // ID in the originating system
  target_id?: string; // ID in the target system (may be populated later)
  operation?: 'RENAME' | 'DELETE' | 'CREATE' | 'UPDATE'; // The actual operation
  created_at: Date; // For ordering operations
  payload: any; // The actual data
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'SUPERSEDED';
  attempts: number; // For retry logic
  last_attempt?: Date; // Timestamp of last attempt
}
