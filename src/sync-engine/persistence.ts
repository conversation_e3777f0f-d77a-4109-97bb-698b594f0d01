import 'server-only';
import { createClient } from '@/data/supabase-server';

function extractEntity(payload: any) {
  const clickupEvent = payload.event;
  switch (clickupEvent) {
    case 'taskCreated':
      return 'TASK';
    case 'taskUpdated':
      if (!payload?.data?.description) {
        return 'TASK';
      }
      return 'TIME_ENTRY';
    case 'taskDeleted':
      return 'TASK';
    case 'folderCreated':
      return 'FOLDER';
    case 'folderUpdated':
      return 'FOLDER';
    case 'folderDeleted':
      return 'FOLDER';
    case 'listCreated':
      return 'LIST';
    case 'listUpdated':
      return 'LIST';
    case 'listDeleted':
      return 'LIST';
    default:
      return 'unknown';
  }
}

function extractEntityId(entity: string, payload: any) {
  switch (entity) {
    case 'TASK':
      return payload.task_id;
    case 'TIME_ENTRY':
      return payload.data.interval_id;
    case 'FOLDER':
      return payload.folder_id;
    case 'LIST':
      return payload.list_id;
    default:
      return 'unknown';
  }
}

function extractOperation(entity: string, payload: any) {
  if (entity !== 'TIME_ENTRY') {
    return payload.event.includes('Created')
      ? 'CREATE'
      : payload.event.includes('Updated')
        ? 'UPDATE'
        : payload.event.includes('Deleted')
          ? 'DELETE'
          : 'unknown';
  }

  const timeData = payload.data;
  return timeData.description.includes('Created')
    ? 'CREATE'
    : timeData.description.includes('Edited')
      ? 'UPDATE'
      : timeData.description.includes('Deleted')
        ? 'DELETE'
        : 'unknown';
}

export async function ingestClickupWebhook(payload: any) {
  const supabase = createClient();
  const entity = extractEntity(payload);
  const entityId = extractEntityId(entity, payload);
  const operation = extractOperation(entity, payload);
  const { error } = await supabase.from('sync_operations').insert({
    origin: 'CLICKUP',
    entity,
    entity_id: entityId,
    operation,
    payload,
    status: 'PENDING',
  });

  if (error) {
    console.error(error);
  }
}
