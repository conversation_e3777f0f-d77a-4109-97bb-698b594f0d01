import { SyncOperation } from './domain';
import { getValidProcessingOrder, coalesceOperations } from './processing';

// Helper to create a test operation
function createTestOperation(
  id: string,
  entity: string,
  entityId: string,
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'RENAME',
  createdAt: Date,
): SyncOperation {
  return {
    id,
    entity,
    entity_id: entityId,
    operation,
    origin: 'CLICKUP',
    created_at: createdAt,
    payload: {},
    status: 'PENDING',
    attempts: 0,
  };
}

describe('getValidProcessingOrder', () => {
  test('should order operations by timestamp when they are for different entities', () => {
    const op1 = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const op2 = createTestOperation('2', 'TASK', '2', 'CREATE', new Date('2023-01-02'));
    const op3 = createTestOperation('3', 'TASK', '3', 'CREATE', new Date('2023-01-03'));

    const operations = [op3, op1, op2];
    const result = getValidProcessingOrder(operations);

    expect(result).toEqual([op1, op2, op3]);
  });

  test('should ensure CREATE comes before UPDATE for the same entity', () => {
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-02'));
    const update = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-01'));

    const operations = [update, create];
    const result = getValidProcessingOrder(operations);

    expect(result).toEqual([create, update]);
  });

  test('should ensure DELETE comes after other operations for the same entity', () => {
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const update = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const del = createTestOperation('3', 'TASK', '1', 'DELETE', new Date('2023-01-01.5'));

    const operations = [create, del, update];
    const result = getValidProcessingOrder(operations);

    expect(result).toEqual([create, update, del]);
  });

  test('should maintain timestamp order for operations of the same type', () => {
    const update1 = createTestOperation('1', 'TASK', '1', 'UPDATE', new Date('2023-01-01'));
    const update2 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));

    const operations = [update2, update1];
    const result = getValidProcessingOrder(operations);

    expect(result).toEqual([update1, update2]);
  });

  test('should handle mixed operation types across different entities', () => {
    const create1 = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const update1 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const create2 = createTestOperation('3', 'TASK', '2', 'CREATE', new Date('2023-01-03'));
    const delete2 = createTestOperation('4', 'TASK', '2', 'DELETE', new Date('2023-01-04'));
    const update2 = createTestOperation('5', 'TASK', '2', 'UPDATE', new Date('2023-01-05'));

    const operations = [delete2, update1, create2, create1, update2];
    const result = getValidProcessingOrder(operations);

    // Should order by timestamp but respect operation type ordering within same entity
    expect(result).toEqual([create1, update1, create2, update2, delete2]);
  });
});

describe('coalesceOperations', () => {
  test('should keep all operations when they are for different entities', () => {
    const op1 = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const op2 = createTestOperation('2', 'TASK', '2', 'CREATE', new Date('2023-01-02'));
    const op3 = createTestOperation('3', 'TASK', '3', 'CREATE', new Date('2023-01-03'));

    const operations = [op3, op1, op2];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(3);
    expect(supersededOperations).toHaveLength(0);
    expect(remainingOperations).toEqual(expect.arrayContaining([op1, op2, op3]));
  });

  test('should supersede all operations when CREATE and DELETE exist for the same entity', () => {
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const update = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const del = createTestOperation('3', 'TASK', '1', 'DELETE', new Date('2023-01-03'));

    const operations = [create, update, del];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(0);
    expect(supersededOperations).toHaveLength(3);
    expect(supersededOperations).toEqual(expect.arrayContaining([create, update, del]));
  });

  test('should keep only the most recent DELETE and supersede UPDATEs', () => {
    const update = createTestOperation('1', 'TASK', '1', 'UPDATE', new Date('2023-01-01'));
    const del1 = createTestOperation('2', 'TASK', '1', 'DELETE', new Date('2023-01-02'));
    const del2 = createTestOperation('3', 'TASK', '1', 'DELETE', new Date('2023-01-03'));

    const operations = [update, del1, del2];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(1);
    expect(supersededOperations).toHaveLength(2);
    expect(remainingOperations[0]).toEqual(del2);
    expect(supersededOperations).toEqual(expect.arrayContaining([update, del1]));
  });

  test('should keep only the earliest CREATE when multiple CREATEs exist', () => {
    const create1 = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const create2 = createTestOperation('2', 'TASK', '1', 'CREATE', new Date('2023-01-02'));

    const operations = [create1, create2];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(1);
    expect(supersededOperations).toHaveLength(1);
    expect(remainingOperations[0]).toEqual(create1);
    expect(supersededOperations[0]).toEqual(create2);
  });

  test('should keep only the most recent UPDATE when multiple UPDATEs exist', () => {
    const update1 = createTestOperation('1', 'TASK', '1', 'UPDATE', new Date('2023-01-01'));
    const update2 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const update3 = createTestOperation('3', 'TASK', '1', 'UPDATE', new Date('2023-01-03'));

    const operations = [update1, update2, update3];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(1);
    expect(supersededOperations).toHaveLength(2);
    expect(remainingOperations[0]).toEqual(update3);
    expect(supersededOperations).toEqual(expect.arrayContaining([update1, update2]));
  });

  test('should keep CREATE and most recent UPDATE when both exist', () => {
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const update1 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const update2 = createTestOperation('3', 'TASK', '1', 'UPDATE', new Date('2023-01-03'));

    const operations = [create, update1, update2];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    expect(remainingOperations).toHaveLength(2);
    expect(supersededOperations).toHaveLength(1);
    expect(remainingOperations).toEqual(expect.arrayContaining([create, update2]));
    expect(supersededOperations[0]).toEqual(update1);

    // Verify CREATE comes before UPDATE in the result
    expect(remainingOperations.indexOf(create)).toBeLessThan(remainingOperations.indexOf(update2));
  });

  test('should handle operations of different types across multiple entities', () => {
    const create1 = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const update1 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));
    const create2 = createTestOperation('3', 'TASK', '2', 'CREATE', new Date('2023-01-03'));
    const delete2 = createTestOperation('4', 'TASK', '2', 'DELETE', new Date('2023-01-04'));
    const update3 = createTestOperation('5', 'TASK', '3', 'UPDATE', new Date('2023-01-05'));

    const operations = [update1, create1, delete2, create2, update3];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Entity 1: CREATE + UPDATE should keep both
    // Entity 2: CREATE + DELETE should supersede both
    // Entity 3: Only UPDATE should be kept
    expect(remainingOperations).toHaveLength(3);
    expect(supersededOperations).toHaveLength(2);
    expect(remainingOperations).toEqual(expect.arrayContaining([create1, update1, update3]));
    expect(supersededOperations).toEqual(expect.arrayContaining([create2, delete2]));
  });

  test('should handle RENAME operations correctly', () => {
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', new Date('2023-01-01'));
    const rename = createTestOperation('2', 'TASK', '1', 'RENAME', new Date('2023-01-02'));
    const update = createTestOperation('3', 'TASK', '1', 'UPDATE', new Date('2023-01-03'));

    const operations = [create, rename, update];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // RENAME should be treated as a regular operation
    expect(remainingOperations).toHaveLength(3);
    expect(supersededOperations).toHaveLength(0);
    expect(remainingOperations).toEqual([create, rename, update]);
  });

  test('should handle empty input', () => {
    const { remainingOperations, supersededOperations } = coalesceOperations([]);

    expect(remainingOperations).toHaveLength(0);
    expect(supersededOperations).toHaveLength(0);
  });

  test('should handle operations with the same timestamp', () => {
    const sameTime = new Date('2023-01-01');
    const create = createTestOperation('1', 'TASK', '1', 'CREATE', sameTime);
    const update = createTestOperation('2', 'TASK', '1', 'UPDATE', sameTime);
    const del = createTestOperation('3', 'TASK', '1', 'DELETE', sameTime);

    const operations = [update, del, create];
    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Should still supersede all operations when CREATE and DELETE exist
    expect(remainingOperations).toHaveLength(0);
    expect(supersededOperations).toHaveLength(3);
  });

  test('should handle complex scenario with multiple entities and operation types', () => {
    // Entity 1: Multiple updates, should keep only the latest
    const update1a = createTestOperation('1', 'TASK', '1', 'UPDATE', new Date('2023-01-01'));
    const update1b = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-02'));

    // Entity 2: Create and multiple updates, should keep create and latest update
    const update2a = createTestOperation('3', 'TASK', '2', 'UPDATE', new Date('2023-01-02'));
    const create2 = createTestOperation('4', 'TASK', '2', 'CREATE', new Date('2023-01-03'));
    const update2b = createTestOperation('5', 'TASK', '2', 'UPDATE', new Date('2023-01-05'));

    // Entity 3: Create, update, and delete, should supersede all
    const delete3 = createTestOperation('6', 'TASK', '3', 'DELETE', new Date('2023-01-01'));
    const create3 = createTestOperation('7', 'TASK', '3', 'CREATE', new Date('2023-01-06'));
    const update3 = createTestOperation('8', 'TASK', '3', 'UPDATE', new Date('2023-01-07'));

    // Entity 4: Only delete, should keep it
    const delete4 = createTestOperation('9', 'TASK', '4', 'DELETE', new Date('2023-01-09'));

    const operations = [update1a, update1b, create2, update2a, update2b, create3, update3, delete3, delete4];

    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Expected:
    // - Entity 1: Keep update1b
    // - Entity 2: Keep create2 and update2b
    // - Entity 3: Supersede all (create3, update3, delete3)
    // - Entity 4: Keep delete4
    expect(remainingOperations).toHaveLength(4);
    expect(supersededOperations).toHaveLength(5);

    expect(remainingOperations).toEqual(expect.arrayContaining([update1b, create2, update2b, delete4]));
    expect(supersededOperations).toEqual(expect.arrayContaining([update1a, update2a, create3, update3, delete3]));

    // Verify CREATE comes before UPDATE for entity 2
    const create2Index = remainingOperations.findIndex((op) => op.id === create2.id);
    const update2bIndex = remainingOperations.findIndex((op) => op.id === update2b.id);
    expect(create2Index).toBeLessThan(update2bIndex);
  });

  test('should handle weird operation order with interleaved operations across entities', () => {
    // Create operations with a weird, non-chronological order
    // Entity 1, 2, 3 with operations in a mixed order
    const update2a = createTestOperation('1', 'TASK', '2', 'UPDATE', new Date('2023-01-01')); // (UPDATE, 2)
    const delete3 = createTestOperation('2', 'TASK', '3', 'DELETE', new Date('2023-01-02')); // (DELETE, 3)
    const delete2 = createTestOperation('3', 'TASK', '2', 'DELETE', new Date('2023-01-03')); // (DELETE, 2)
    const create2 = createTestOperation('4', 'TASK', '2', 'CREATE', new Date('2023-01-04')); // (CREATE, 2)
    const delete1 = createTestOperation('5', 'TASK', '1', 'DELETE', new Date('2023-01-05')); // (DELETE, 1)
    const create1 = createTestOperation('6', 'TASK', '1', 'CREATE', new Date('2023-01-06')); // (CREATE, 1)
    const update2b = createTestOperation('7', 'TASK', '2', 'UPDATE', new Date('2023-01-07')); // (UPDATE, 2)
    const update3 = createTestOperation('8', 'TASK', '3', 'UPDATE', new Date('2023-01-08')); // (UPDATE, 3)

    const operations = [update2a, delete3, delete2, create2, delete1, create1, update2b, update3];

    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Expected outcomes:
    // - Entity 1: CREATE and DELETE should supersede each other
    // - Entity 2: CREATE, UPDATE, DELETE should all supersede each other
    // - Entity 3: DELETE supersedes UPDATE

    // All operations should be in either remainingOperations or supersededOperations
    expect(remainingOperations.length + supersededOperations.length).toBe(8);

    // Entity 1: Both CREATE and DELETE should be superseded
    expect(supersededOperations).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ entity: 'TASK', entity_id: '1', operation: 'CREATE' }),
        expect.objectContaining({ entity: 'TASK', entity_id: '1', operation: 'DELETE' }),
      ]),
    );

    // Entity 2: Either all operations are superseded, or only the latest DELETE is kept
    if (remainingOperations.some((op) => op.entity === 'TASK' && op.entity_id === '2')) {
      // If any operation for entity 2 remains, it should be the DELETE
      expect(remainingOperations).toEqual(
        expect.arrayContaining([expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'DELETE' })]),
      );

      // And CREATE and UPDATE should be superseded
      expect(supersededOperations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'CREATE' }),
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'UPDATE', id: update2a.id }),
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'UPDATE', id: update2b.id }),
        ]),
      );
    } else {
      // Otherwise all entity 2 operations should be superseded
      expect(supersededOperations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'CREATE' }),
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'DELETE' }),
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'UPDATE', id: update2a.id }),
          expect.objectContaining({ entity: 'TASK', entity_id: '2', operation: 'UPDATE', id: update2b.id }),
        ]),
      );
    }

    // Entity 3: DELETE should supersede UPDATE
    expect(supersededOperations).toEqual(
      expect.arrayContaining([expect.objectContaining({ entity: 'TASK', entity_id: '3', operation: 'UPDATE' })]),
    );

    // Either DELETE is kept or it's also superseded (if there was a CREATE that we didn't include in the test)
    if (remainingOperations.some((op) => op.entity === 'TASK' && op.entity_id === '3')) {
      expect(remainingOperations).toEqual(
        expect.arrayContaining([expect.objectContaining({ entity: 'TASK', entity_id: '3', operation: 'DELETE' })]),
      );
    } else {
      expect(supersededOperations).toEqual(
        expect.arrayContaining([expect.objectContaining({ entity: 'TASK', entity_id: '3', operation: 'DELETE' })]),
      );
    }
  });

  test('should handle operations arriving in reverse chronological order', () => {
    // Operations in reverse chronological order
    const delete1 = createTestOperation('1', 'TASK', '1', 'DELETE', new Date('2023-01-05'));
    const update1 = createTestOperation('2', 'TASK', '1', 'UPDATE', new Date('2023-01-04'));
    const create1 = createTestOperation('3', 'TASK', '1', 'CREATE', new Date('2023-01-03'));
    const update2 = createTestOperation('4', 'TASK', '2', 'UPDATE', new Date('2023-01-02'));
    const create2 = createTestOperation('5', 'TASK', '2', 'CREATE', new Date('2023-01-01'));

    const operations = [delete1, update1, create1, update2, create2];

    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Expected:
    // - Entity 1: All operations superseded (CREATE + DELETE)
    // - Entity 2: Keep CREATE and UPDATE
    expect(remainingOperations).toHaveLength(2);
    expect(supersededOperations).toHaveLength(3);

    expect(remainingOperations).toEqual(expect.arrayContaining([create2, update2]));
    expect(supersededOperations).toEqual(expect.arrayContaining([create1, update1, delete1]));

    // Verify CREATE comes before UPDATE for entity 2
    const create2Index = remainingOperations.findIndex((op) => op.id === create2.id);
    const update2Index = remainingOperations.findIndex((op) => op.id === update2.id);
    expect(create2Index).toBeLessThan(update2Index);
  });

  test('should handle multiple operations with identical timestamps', () => {
    // All operations have the same timestamp
    const sameTime = new Date('2023-01-01');

    const create1 = createTestOperation('1', 'TASK', '1', 'CREATE', sameTime);
    const update1 = createTestOperation('2', 'TASK', '1', 'UPDATE', sameTime);
    const delete2 = createTestOperation('3', 'TASK', '2', 'DELETE', sameTime);
    const create2 = createTestOperation('4', 'TASK', '2', 'CREATE', sameTime);
    const update3 = createTestOperation('5', 'TASK', '3', 'UPDATE', sameTime);
    const update3b = createTestOperation('6', 'TASK', '3', 'UPDATE', sameTime);

    const operations = [create1, update1, delete2, create2, update3, update3b];

    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Expected:
    // - Entity 1: Keep CREATE and UPDATE
    // - Entity 2: All operations superseded (CREATE + DELETE)
    // - Entity 3: Keep only one UPDATE
    expect(remainingOperations).toHaveLength(3);
    expect(supersededOperations).toHaveLength(3);

    // Verify CREATE comes before UPDATE for entity 1
    const create1Index = remainingOperations.findIndex((op) => op.id === create1.id);
    const update1Index = remainingOperations.findIndex((op) => op.id === update1.id);
    expect(create1Index).toBeLessThan(update1Index);
  });

  test('should handle circular dependencies in operation order', () => {
    // Create a scenario where the ordering logic could potentially create a circular dependency
    // Entity 1: CREATE with late timestamp
    // Entity 2: UPDATE with early timestamp
    // Entity 3: DELETE with middle timestamp
    const update2 = createTestOperation('1', 'TASK', '2', 'UPDATE', new Date('2023-01-01'));
    const delete3 = createTestOperation('2', 'TASK', '3', 'DELETE', new Date('2023-01-02'));
    const create1 = createTestOperation('3', 'TASK', '1', 'CREATE', new Date('2023-01-03'));
    const update1 = createTestOperation('4', 'TASK', '1', 'UPDATE', new Date('2023-01-04'));
    const create3 = createTestOperation('5', 'TASK', '3', 'CREATE', new Date('2023-01-05'));
    const create2 = createTestOperation('6', 'TASK', '2', 'CREATE', new Date('2023-01-06'));

    const operations = [update2, delete3, create1, update1, create3, create2];

    const { remainingOperations, supersededOperations } = coalesceOperations(operations);

    // Expected:
    // - Entity 1: Keep CREATE and UPDATE
    // - Entity 2: Keep CREATE and UPDATE
    // - Entity 3: All operations superseded (CREATE + DELETE)
    expect(remainingOperations).toHaveLength(4);
    expect(supersededOperations).toHaveLength(2);

    expect(remainingOperations).toEqual(expect.arrayContaining([create1, update1, create2, update2]));
    expect(supersededOperations).toEqual(expect.arrayContaining([create3, delete3]));

    // Verify CREATE comes before UPDATE for both entities
    const create1Index = remainingOperations.findIndex((op) => op.id === create1.id);
    const update1Index = remainingOperations.findIndex((op) => op.id === update1.id);
    expect(create1Index).toBeLessThan(update1Index);

    const create2Index = remainingOperations.findIndex((op) => op.id === create2.id);
    const update2Index = remainingOperations.findIndex((op) => op.id === update2.id);
    expect(create2Index).toBeLessThan(update2Index);
  });
});
