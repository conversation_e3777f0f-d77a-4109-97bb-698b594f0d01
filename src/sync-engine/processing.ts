import 'server-only';
import { SyncOperation } from './domain';

/**
 * Determines the valid processing order for operations.
 * Ensures CREATE operations come before UPDATE operations for the same entity,
 * and DELETE operations come after other operations.
 */
export function getValidProcessingOrder(operations: SyncOperation[]) {
  // First sort by timestamp
  const orderedOperations = [...operations].sort((a, b) => a.created_at.getTime() - b.created_at.getTime());

  // Then ensure proper operation order for the same entity
  orderedOperations.sort((a, b) => {
    // If same entity and different operation types
    if (a.entity === b.entity && a.entity_id === b.entity_id && a.operation !== b.operation) {
      // CREATE comes before other operations
      if (a.operation === 'CREATE') return -1;
      if (b.operation === 'CREATE') return 1;
      // DELETE comes after other operations
      if (a.operation === 'DELETE') return 1;
      if (b.operation === 'DELETE') return -1;
    }
    // Otherwise keep timestamp order
    return a.created_at.getTime() - b.created_at.getTime();
  });

  return orderedOperations;
}

/**
 * Coalesces operations by identifying and marking superseded operations.
 * Handles special cases like:
 * - When the same entity is updated and deleted, supersede the update and only keep the delete
 * - When there is a create and delete for the same entity, mark all operations as superseded
 * - When there is a create and update, keep both but ensure create comes before update
 * - Only keep the most recent update per entity
 */
export function coalesceOperations(operations: SyncOperation[]) {
  // First get operations in the correct processing order
  const orderedOperations = getValidProcessingOrder(operations);

  // Group operations by entity and entityId
  const operationsByEntity = new Map<string, SyncOperation[]>();

  for (const operation of orderedOperations) {
    const key = `${operation.entity}:${operation.entity_id}`;
    if (!operationsByEntity.has(key)) {
      operationsByEntity.set(key, []);
    }
    operationsByEntity.get(key)!.push(operation);
  }

  const remainingOperations: SyncOperation[] = [];
  const supersededOperations: SyncOperation[] = [];

  // Process each entity's operations
  for (const [_, entityOperations] of Array.from(operationsByEntity)) {
    // Check for CREATE and DELETE combination (all operations superseded)
    const hasCreate = entityOperations.some((op) => op.operation === 'CREATE');
    const hasDelete = entityOperations.some((op) => op.operation === 'DELETE');

    if (hasCreate && hasDelete) {
      // All operations for this entity are superseded
      supersededOperations.push(...entityOperations);
      continue;
    }

    // Handle other cases
    const creates: SyncOperation[] = [];
    const updates: SyncOperation[] = [];
    const deletes: SyncOperation[] = [];
    const others: SyncOperation[] = [];

    // Sort operations by type
    for (const op of entityOperations) {
      if (op.operation === 'CREATE') creates.push(op);
      else if (op.operation === 'UPDATE') updates.push(op);
      else if (op.operation === 'DELETE') deletes.push(op);
      else others.push(op);
    }

    // If we have a DELETE, it supersedes all UPDATEs
    if (deletes.length > 0) {
      // Keep only the most recent DELETE
      const latestDelete = deletes.reduce((latest, current) =>
        latest.created_at.getTime() > current.created_at.getTime() ? latest : current,
      );
      remainingOperations.push(latestDelete);

      // Mark all other DELETEs and all UPDATEs as superseded
      supersededOperations.push(...deletes.filter((op) => op.id !== latestDelete.id));
      supersededOperations.push(...updates);
    } else {
      // No DELETEs, handle CREATEs and UPDATEs

      // For CREATEs, keep only the earliest one
      if (creates.length > 0) {
        const earliestCreate = creates.reduce((earliest, current) =>
          earliest.created_at.getTime() < current.created_at.getTime() ? earliest : current,
        );
        remainingOperations.push(earliestCreate);
        supersededOperations.push(...creates.filter((op) => op.id !== earliestCreate.id));
      }

      // For UPDATEs, keep only the latest one
      if (updates.length > 0) {
        const latestUpdate = updates.reduce((latest, current) =>
          latest.created_at.getTime() > current.created_at.getTime() ? latest : current,
        );
        remainingOperations.push(latestUpdate);
        supersededOperations.push(...updates.filter((op) => op.id !== latestUpdate.id));
      }
    }

    // Add other operation types
    remainingOperations.push(...others);
  }

  // Sort the remaining operations using the valid processing order
  remainingOperations.sort((a, b) => {
    // If same entity and different operation types
    if (a.entity === b.entity && a.entity_id === b.entity_id && a.operation !== b.operation) {
      // CREATE comes before other operations
      if (a.operation === 'CREATE') return -1;
      if (b.operation === 'CREATE') return 1;
      // DELETE comes after other operations
      if (a.operation === 'DELETE') return 1;
      if (b.operation === 'DELETE') return -1;
    }
    // Otherwise sort by timestamp
    return a.created_at.getTime() - b.created_at.getTime();
  });

  console.log('Coalesced operations from', operations.length, 'to', remainingOperations.length);
  return { remainingOperations, supersededOperations };
}
