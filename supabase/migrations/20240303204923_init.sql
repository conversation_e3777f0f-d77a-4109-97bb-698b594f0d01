create table "public"."business_activities" (
    "clickup_task_tag" text not null,
    "bexio_business_activity_name" text not null,
    "bexio_business_activity_id" bigint not null
);


create table "public"."clients" (
    "id" bigint generated by default as identity not null,
    "bexio_contact_id" bigint,
    "clickup_folder_id" text,
    "name" text not null,
    "clickup_folder_assignee_id" text,
    "clickup_archived" boolean default false
);


create table "public"."employees" (
    "user_id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "email" text not null,
    "profile_picture_url" text,
    "clickup_user_id" text,
    "bexio_user_id" bigint
);


create table "public"."global_settings" (
    "id" bigint generated by default as identity not null,
    "webhooks_active" boolean not null default true,
    "false_timeentries_list_id" text,
    "pto_list_id" text
);


create table "public"."projects" (
    "id" bigint generated by default as identity not null,
    "bexio_project_id" bigint,
    "clickup_list_id" text,
    "name" text,
    "clickup_folder_id" text,
    "clickup_is_archived" boolean not null default false,
    "clickup_name" text,
    "clickup_archived" boolean not null default false,
    "dropbox_folder_id" text,
    "is_client_project" boolean not null default false,
    "clickup_start" bigint
);


create table "public"."tasks" (
    "id" bigint generated by default as identity not null,
    "bexio_work_package_id" bigint,
    "clickup_task_id" text,
    "name" text not null default 'NONE'::text,
    "clickup_list_id" text,
    "clickup_due_date" bigint,
    "clickup_task_description" text,
    "clickup_time_estimate" bigint,
    "clickup_user_id" text,
    "clickup_url" text,
    "is_client_task" boolean not null default false
);


create table "public"."time_entries" (
    "id" bigint generated by default as identity not null,
    "clickup_time_entry_id" text,
    "clickup_task_id" text,
    "clickup_start" bigint,
    "clickup_end" bigint,
    "clickup_duration" bigint,
    "clickup_user_id" text,
    "clickup_task_tag" text,
    "bexio_timesheet_id" bigint,
    "billable" boolean default true,
    "clickup_description" text
);


create table "public"."working_hours" (
    "user_id" uuid not null,
    "date" date not null,
    "hours" real not null default '0'::real,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX bexio_project_id_unique ON public.projects USING btree (bexio_project_id);

CREATE UNIQUE INDEX business_activities_bexio_business_activity_id_key ON public.business_activities USING btree (bexio_business_activity_id);

CREATE UNIQUE INDEX business_activities_bexio_business_activity_name_key ON public.business_activities USING btree (bexio_business_activity_name);

CREATE UNIQUE INDEX business_activities_clickup_time_label_key ON public.business_activities USING btree (clickup_task_tag);

CREATE UNIQUE INDEX business_activities_pkey ON public.business_activities USING btree (bexio_business_activity_id);

CREATE UNIQUE INDEX clickup_folder_id_unique ON public.clients USING btree (clickup_folder_id);

CREATE UNIQUE INDEX clickup_list_id_unique ON public.projects USING btree (clickup_list_id);

CREATE UNIQUE INDEX clickup_task_id_unique ON public.tasks USING btree (clickup_task_id);

CREATE UNIQUE INDEX clickup_user_id_unique ON public.employees USING btree (clickup_user_id);

CREATE UNIQUE INDEX clients_bexio_contact_id_key ON public.clients USING btree (bexio_contact_id);

CREATE UNIQUE INDEX clients_id_key ON public.clients USING btree (id);

CREATE UNIQUE INDEX clients_name_unique ON public.clients USING btree (name);

CREATE UNIQUE INDEX clients_pkey ON public.clients USING btree (id);

CREATE UNIQUE INDEX employees_bexio_user_id_key ON public.employees USING btree (bexio_user_id);

CREATE UNIQUE INDEX employees_email_key ON public.employees USING btree (email);

CREATE UNIQUE INDEX employees_pkey ON public.employees USING btree (user_id);

CREATE UNIQUE INDEX global_settings_pkey ON public.global_settings USING btree (id);

CREATE UNIQUE INDEX project_name_unique ON public.projects USING btree (name);

CREATE UNIQUE INDEX projects_pkey ON public.projects USING btree (id);

CREATE UNIQUE INDEX tasks_pkey ON public.tasks USING btree (id);

CREATE UNIQUE INDEX time_entries_clickup_time_entry_id_key ON public.time_entries USING btree (clickup_time_entry_id);

CREATE UNIQUE INDEX time_entries_pkey ON public.time_entries USING btree (id);

CREATE UNIQUE INDEX working_hours_pkey ON public.working_hours USING btree (user_id, date);

alter table "public"."business_activities" add constraint "business_activities_pkey" PRIMARY KEY using index "business_activities_pkey";

alter table "public"."clients" add constraint "clients_pkey" PRIMARY KEY using index "clients_pkey";

alter table "public"."employees" add constraint "employees_pkey" PRIMARY KEY using index "employees_pkey";

alter table "public"."global_settings" add constraint "global_settings_pkey" PRIMARY KEY using index "global_settings_pkey";

alter table "public"."projects" add constraint "projects_pkey" PRIMARY KEY using index "projects_pkey";

alter table "public"."tasks" add constraint "tasks_pkey" PRIMARY KEY using index "tasks_pkey";

alter table "public"."time_entries" add constraint "time_entries_pkey" PRIMARY KEY using index "time_entries_pkey";

alter table "public"."working_hours" add constraint "working_hours_pkey" PRIMARY KEY using index "working_hours_pkey";

alter table "public"."business_activities" add constraint "business_activities_bexio_business_activity_id_key" UNIQUE using index "business_activities_bexio_business_activity_id_key";

alter table "public"."business_activities" add constraint "business_activities_bexio_business_activity_name_key" UNIQUE using index "business_activities_bexio_business_activity_name_key";

alter table "public"."business_activities" add constraint "business_activities_clickup_time_label_key" UNIQUE using index "business_activities_clickup_time_label_key";

alter table "public"."clients" add constraint "clickup_folder_id_unique" UNIQUE using index "clickup_folder_id_unique";

alter table "public"."clients" add constraint "clients_bexio_contact_id_key" UNIQUE using index "clients_bexio_contact_id_key";

alter table "public"."clients" add constraint "clients_clickup_folder_assignee_id_fkey" FOREIGN KEY (clickup_folder_assignee_id) REFERENCES employees(clickup_user_id) not valid;

alter table "public"."clients" validate constraint "clients_clickup_folder_assignee_id_fkey";

alter table "public"."clients" add constraint "clients_id_key" UNIQUE using index "clients_id_key";

alter table "public"."clients" add constraint "clients_name_unique" UNIQUE using index "clients_name_unique";

alter table "public"."employees" add constraint "clickup_user_id_unique" UNIQUE using index "clickup_user_id_unique";

alter table "public"."employees" add constraint "employees_bexio_user_id_key" UNIQUE using index "employees_bexio_user_id_key";

alter table "public"."employees" add constraint "employees_email_key" UNIQUE using index "employees_email_key";

alter table "public"."global_settings" add constraint "global_settings_false_timeentries_list_id_fkey" FOREIGN KEY (false_timeentries_list_id) REFERENCES projects(clickup_list_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."global_settings" validate constraint "global_settings_false_timeentries_list_id_fkey";

alter table "public"."global_settings" add constraint "global_settings_pto_list_id_fkey" FOREIGN KEY (pto_list_id) REFERENCES projects(clickup_list_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."global_settings" validate constraint "global_settings_pto_list_id_fkey";

alter table "public"."projects" add constraint "bexio_project_id_unique" UNIQUE using index "bexio_project_id_unique";

alter table "public"."projects" add constraint "clickup_list_id_unique" UNIQUE using index "clickup_list_id_unique";

alter table "public"."projects" add constraint "project_name_unique" UNIQUE using index "project_name_unique";

alter table "public"."projects" add constraint "projects_clickup_folder_id_fkey" FOREIGN KEY (clickup_folder_id) REFERENCES clients(clickup_folder_id) not valid;

alter table "public"."projects" validate constraint "projects_clickup_folder_id_fkey";

alter table "public"."tasks" add constraint "clickup_task_id_unique" UNIQUE using index "clickup_task_id_unique";

alter table "public"."tasks" add constraint "tasks_clickup_list_id_fkey" FOREIGN KEY (clickup_list_id) REFERENCES projects(clickup_list_id) not valid;

alter table "public"."tasks" validate constraint "tasks_clickup_list_id_fkey";

alter table "public"."tasks" add constraint "tasks_clickup_user_id_fkey" FOREIGN KEY (clickup_user_id) REFERENCES employees(clickup_user_id) not valid;

alter table "public"."tasks" validate constraint "tasks_clickup_user_id_fkey";

alter table "public"."time_entries" add constraint "time_entries_clickup_task_id_fkey" FOREIGN KEY (clickup_task_id) REFERENCES tasks(clickup_task_id) not valid;

alter table "public"."time_entries" validate constraint "time_entries_clickup_task_id_fkey";

alter table "public"."time_entries" add constraint "time_entries_clickup_task_tag_fkey" FOREIGN KEY (clickup_task_tag) REFERENCES business_activities(clickup_task_tag) not valid;

alter table "public"."time_entries" validate constraint "time_entries_clickup_task_tag_fkey";

alter table "public"."time_entries" add constraint "time_entries_clickup_time_entry_id_key" UNIQUE using index "time_entries_clickup_time_entry_id_key";

alter table "public"."time_entries" add constraint "time_entries_clickup_user_id_fkey" FOREIGN KEY (clickup_user_id) REFERENCES employees(clickup_user_id) not valid;

alter table "public"."time_entries" validate constraint "time_entries_clickup_user_id_fkey";

alter table "public"."working_hours" add constraint "working_hours_user_id_fkey" FOREIGN KEY (user_id) REFERENCES employees(user_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."working_hours" validate constraint "working_hours_user_id_fkey";

grant delete on table "public"."business_activities" to "anon";

grant insert on table "public"."business_activities" to "anon";

grant references on table "public"."business_activities" to "anon";

grant select on table "public"."business_activities" to "anon";

grant trigger on table "public"."business_activities" to "anon";

grant truncate on table "public"."business_activities" to "anon";

grant update on table "public"."business_activities" to "anon";

grant delete on table "public"."business_activities" to "authenticated";

grant insert on table "public"."business_activities" to "authenticated";

grant references on table "public"."business_activities" to "authenticated";

grant select on table "public"."business_activities" to "authenticated";

grant trigger on table "public"."business_activities" to "authenticated";

grant truncate on table "public"."business_activities" to "authenticated";

grant update on table "public"."business_activities" to "authenticated";

grant delete on table "public"."business_activities" to "service_role";

grant insert on table "public"."business_activities" to "service_role";

grant references on table "public"."business_activities" to "service_role";

grant select on table "public"."business_activities" to "service_role";

grant trigger on table "public"."business_activities" to "service_role";

grant truncate on table "public"."business_activities" to "service_role";

grant update on table "public"."business_activities" to "service_role";

grant delete on table "public"."clients" to "anon";

grant insert on table "public"."clients" to "anon";

grant references on table "public"."clients" to "anon";

grant select on table "public"."clients" to "anon";

grant trigger on table "public"."clients" to "anon";

grant truncate on table "public"."clients" to "anon";

grant update on table "public"."clients" to "anon";

grant delete on table "public"."clients" to "authenticated";

grant insert on table "public"."clients" to "authenticated";

grant references on table "public"."clients" to "authenticated";

grant select on table "public"."clients" to "authenticated";

grant trigger on table "public"."clients" to "authenticated";

grant truncate on table "public"."clients" to "authenticated";

grant update on table "public"."clients" to "authenticated";

grant delete on table "public"."clients" to "service_role";

grant insert on table "public"."clients" to "service_role";

grant references on table "public"."clients" to "service_role";

grant select on table "public"."clients" to "service_role";

grant trigger on table "public"."clients" to "service_role";

grant truncate on table "public"."clients" to "service_role";

grant update on table "public"."clients" to "service_role";

grant delete on table "public"."employees" to "anon";

grant insert on table "public"."employees" to "anon";

grant references on table "public"."employees" to "anon";

grant select on table "public"."employees" to "anon";

grant trigger on table "public"."employees" to "anon";

grant truncate on table "public"."employees" to "anon";

grant update on table "public"."employees" to "anon";

grant delete on table "public"."employees" to "authenticated";

grant insert on table "public"."employees" to "authenticated";

grant references on table "public"."employees" to "authenticated";

grant select on table "public"."employees" to "authenticated";

grant trigger on table "public"."employees" to "authenticated";

grant truncate on table "public"."employees" to "authenticated";

grant update on table "public"."employees" to "authenticated";

grant delete on table "public"."employees" to "service_role";

grant insert on table "public"."employees" to "service_role";

grant references on table "public"."employees" to "service_role";

grant select on table "public"."employees" to "service_role";

grant trigger on table "public"."employees" to "service_role";

grant truncate on table "public"."employees" to "service_role";

grant update on table "public"."employees" to "service_role";

grant delete on table "public"."global_settings" to "anon";

grant insert on table "public"."global_settings" to "anon";

grant references on table "public"."global_settings" to "anon";

grant select on table "public"."global_settings" to "anon";

grant trigger on table "public"."global_settings" to "anon";

grant truncate on table "public"."global_settings" to "anon";

grant update on table "public"."global_settings" to "anon";

grant delete on table "public"."global_settings" to "authenticated";

grant insert on table "public"."global_settings" to "authenticated";

grant references on table "public"."global_settings" to "authenticated";

grant select on table "public"."global_settings" to "authenticated";

grant trigger on table "public"."global_settings" to "authenticated";

grant truncate on table "public"."global_settings" to "authenticated";

grant update on table "public"."global_settings" to "authenticated";

grant delete on table "public"."global_settings" to "service_role";

grant insert on table "public"."global_settings" to "service_role";

grant references on table "public"."global_settings" to "service_role";

grant select on table "public"."global_settings" to "service_role";

grant trigger on table "public"."global_settings" to "service_role";

grant truncate on table "public"."global_settings" to "service_role";

grant update on table "public"."global_settings" to "service_role";

grant delete on table "public"."projects" to "anon";

grant insert on table "public"."projects" to "anon";

grant references on table "public"."projects" to "anon";

grant select on table "public"."projects" to "anon";

grant trigger on table "public"."projects" to "anon";

grant truncate on table "public"."projects" to "anon";

grant update on table "public"."projects" to "anon";

grant delete on table "public"."projects" to "authenticated";

grant insert on table "public"."projects" to "authenticated";

grant references on table "public"."projects" to "authenticated";

grant select on table "public"."projects" to "authenticated";

grant trigger on table "public"."projects" to "authenticated";

grant truncate on table "public"."projects" to "authenticated";

grant update on table "public"."projects" to "authenticated";

grant delete on table "public"."projects" to "service_role";

grant insert on table "public"."projects" to "service_role";

grant references on table "public"."projects" to "service_role";

grant select on table "public"."projects" to "service_role";

grant trigger on table "public"."projects" to "service_role";

grant truncate on table "public"."projects" to "service_role";

grant update on table "public"."projects" to "service_role";

grant delete on table "public"."tasks" to "anon";

grant insert on table "public"."tasks" to "anon";

grant references on table "public"."tasks" to "anon";

grant select on table "public"."tasks" to "anon";

grant trigger on table "public"."tasks" to "anon";

grant truncate on table "public"."tasks" to "anon";

grant update on table "public"."tasks" to "anon";

grant delete on table "public"."tasks" to "authenticated";

grant insert on table "public"."tasks" to "authenticated";

grant references on table "public"."tasks" to "authenticated";

grant select on table "public"."tasks" to "authenticated";

grant trigger on table "public"."tasks" to "authenticated";

grant truncate on table "public"."tasks" to "authenticated";

grant update on table "public"."tasks" to "authenticated";

grant delete on table "public"."tasks" to "service_role";

grant insert on table "public"."tasks" to "service_role";

grant references on table "public"."tasks" to "service_role";

grant select on table "public"."tasks" to "service_role";

grant trigger on table "public"."tasks" to "service_role";

grant truncate on table "public"."tasks" to "service_role";

grant update on table "public"."tasks" to "service_role";

grant delete on table "public"."time_entries" to "anon";

grant insert on table "public"."time_entries" to "anon";

grant references on table "public"."time_entries" to "anon";

grant select on table "public"."time_entries" to "anon";

grant trigger on table "public"."time_entries" to "anon";

grant truncate on table "public"."time_entries" to "anon";

grant update on table "public"."time_entries" to "anon";

grant delete on table "public"."time_entries" to "authenticated";

grant insert on table "public"."time_entries" to "authenticated";

grant references on table "public"."time_entries" to "authenticated";

grant select on table "public"."time_entries" to "authenticated";

grant trigger on table "public"."time_entries" to "authenticated";

grant truncate on table "public"."time_entries" to "authenticated";

grant update on table "public"."time_entries" to "authenticated";

grant delete on table "public"."time_entries" to "service_role";

grant insert on table "public"."time_entries" to "service_role";

grant references on table "public"."time_entries" to "service_role";

grant select on table "public"."time_entries" to "service_role";

grant trigger on table "public"."time_entries" to "service_role";

grant truncate on table "public"."time_entries" to "service_role";

grant update on table "public"."time_entries" to "service_role";

grant delete on table "public"."working_hours" to "anon";

grant insert on table "public"."working_hours" to "anon";

grant references on table "public"."working_hours" to "anon";

grant select on table "public"."working_hours" to "anon";

grant trigger on table "public"."working_hours" to "anon";

grant truncate on table "public"."working_hours" to "anon";

grant update on table "public"."working_hours" to "anon";

grant delete on table "public"."working_hours" to "authenticated";

grant insert on table "public"."working_hours" to "authenticated";

grant references on table "public"."working_hours" to "authenticated";

grant select on table "public"."working_hours" to "authenticated";

grant trigger on table "public"."working_hours" to "authenticated";

grant truncate on table "public"."working_hours" to "authenticated";

grant update on table "public"."working_hours" to "authenticated";

grant delete on table "public"."working_hours" to "service_role";

grant insert on table "public"."working_hours" to "service_role";

grant references on table "public"."working_hours" to "service_role";

grant select on table "public"."working_hours" to "service_role";

grant trigger on table "public"."working_hours" to "service_role";

grant truncate on table "public"."working_hours" to "service_role";

grant update on table "public"."working_hours" to "service_role";



