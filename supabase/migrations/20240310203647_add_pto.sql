create table "public"."pto_credits" (
    "year" integer not null,
    "user_id" uuid not null,
    "hours" integer not null,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX pto_credits_pkey ON public.pto_credits USING btree (year, user_id);

alter table "public"."pto_credits" add constraint "pto_credits_pkey" PRIMARY KEY using index "pto_credits_pkey";

alter table "public"."pto_credits" add constraint "pto_credits_user_id_fkey" FOREIGN KEY (user_id) REFERENCES employees(user_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."pto_credits" validate constraint "pto_credits_user_id_fkey";

grant delete on table "public"."pto_credits" to "anon";

grant insert on table "public"."pto_credits" to "anon";

grant references on table "public"."pto_credits" to "anon";

grant select on table "public"."pto_credits" to "anon";

grant trigger on table "public"."pto_credits" to "anon";

grant truncate on table "public"."pto_credits" to "anon";

grant update on table "public"."pto_credits" to "anon";

grant delete on table "public"."pto_credits" to "authenticated";

grant insert on table "public"."pto_credits" to "authenticated";

grant references on table "public"."pto_credits" to "authenticated";

grant select on table "public"."pto_credits" to "authenticated";

grant trigger on table "public"."pto_credits" to "authenticated";

grant truncate on table "public"."pto_credits" to "authenticated";

grant update on table "public"."pto_credits" to "authenticated";

grant delete on table "public"."pto_credits" to "service_role";

grant insert on table "public"."pto_credits" to "service_role";

grant references on table "public"."pto_credits" to "service_role";

grant select on table "public"."pto_credits" to "service_role";

grant trigger on table "public"."pto_credits" to "service_role";

grant truncate on table "public"."pto_credits" to "service_role";

grant update on table "public"."pto_credits" to "service_role";



