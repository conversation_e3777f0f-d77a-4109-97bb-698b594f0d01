create table "public"."time_corrections" (
    "id" bigint generated by default as identity not null,
    "user_id" uuid,
    "hours" real not null default '0'::real,
    "date" timestamp with time zone not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."business_activities" add column "clickup_tag_fg" text;

alter table "public"."business_activities" add column "clickup_task_bg" text;

CREATE UNIQUE INDEX time_corrections_pkey ON public.time_corrections USING btree (id);

alter table "public"."time_corrections" add constraint "time_corrections_pkey" PRIMARY KEY using index "time_corrections_pkey";

alter table "public"."time_corrections" add constraint "time_corrections_user_id_fkey" FOREIGN KEY (user_id) REFERENCES employees(user_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."time_corrections" validate constraint "time_corrections_user_id_fkey";

grant delete on table "public"."time_corrections" to "anon";

grant insert on table "public"."time_corrections" to "anon";

grant references on table "public"."time_corrections" to "anon";

grant select on table "public"."time_corrections" to "anon";

grant trigger on table "public"."time_corrections" to "anon";

grant truncate on table "public"."time_corrections" to "anon";

grant update on table "public"."time_corrections" to "anon";

grant delete on table "public"."time_corrections" to "authenticated";

grant insert on table "public"."time_corrections" to "authenticated";

grant references on table "public"."time_corrections" to "authenticated";

grant select on table "public"."time_corrections" to "authenticated";

grant trigger on table "public"."time_corrections" to "authenticated";

grant truncate on table "public"."time_corrections" to "authenticated";

grant update on table "public"."time_corrections" to "authenticated";

grant delete on table "public"."time_corrections" to "service_role";

grant insert on table "public"."time_corrections" to "service_role";

grant references on table "public"."time_corrections" to "service_role";

grant select on table "public"."time_corrections" to "service_role";

grant trigger on table "public"."time_corrections" to "service_role";

grant truncate on table "public"."time_corrections" to "service_role";

grant update on table "public"."time_corrections" to "service_role";



