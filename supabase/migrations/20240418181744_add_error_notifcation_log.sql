create table "public"."error_notifications" (
    "id" bigint generated by default as identity not null,
    "message_hash" text not null,
    "last_sent" timestamp with time zone not null default now(),
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX error_notifications_pkey ON public.error_notifications USING btree (id);

alter table "public"."error_notifications" add constraint "error_notifications_pkey" PRIMARY KEY using index "error_notifications_pkey";

grant delete on table "public"."error_notifications" to "anon";

grant insert on table "public"."error_notifications" to "anon";

grant references on table "public"."error_notifications" to "anon";

grant select on table "public"."error_notifications" to "anon";

grant trigger on table "public"."error_notifications" to "anon";

grant truncate on table "public"."error_notifications" to "anon";

grant update on table "public"."error_notifications" to "anon";

grant delete on table "public"."error_notifications" to "authenticated";

grant insert on table "public"."error_notifications" to "authenticated";

grant references on table "public"."error_notifications" to "authenticated";

grant select on table "public"."error_notifications" to "authenticated";

grant trigger on table "public"."error_notifications" to "authenticated";

grant truncate on table "public"."error_notifications" to "authenticated";

grant update on table "public"."error_notifications" to "authenticated";

grant delete on table "public"."error_notifications" to "service_role";

grant insert on table "public"."error_notifications" to "service_role";

grant references on table "public"."error_notifications" to "service_role";

grant select on table "public"."error_notifications" to "service_role";

grant trigger on table "public"."error_notifications" to "service_role";

grant truncate on table "public"."error_notifications" to "service_role";

grant update on table "public"."error_notifications" to "service_role";



