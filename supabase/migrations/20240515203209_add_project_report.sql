create table "public"."bexio_projects" (
    "bexio_id" integer not null,
    "name" text not null,
    "nr" text not null,
    "status" text not null,
    "substatus" text,
    "bexio_user_id" bigint,
    "time_estimate" real not null default '0'::real,
    "time_spent" real not null default '0'::real,
    "budget" real not null default '0'::real,
    "effective_cost" real not null default '0'::real,
    "factorized_amount" real not null default '0'::real,
    "services_amount" real not null default '0'::real,
    "trades_amount" real not null default '0'::real,
    "bexio_client_id" bigint,
    "invoice_ids" integer[] not null default '{}'::integer[]
);


CREATE UNIQUE INDEX bexio_projects_pkey ON public.bexio_projects USING btree (bexio_id);

alter table "public"."bexio_projects" add constraint "bexio_projects_pkey" PRIMARY KEY using index "bexio_projects_pkey";

alter table "public"."bexio_projects" add constraint "public_bexio_projects_bexio_client_id_fkey" FOREIGN KEY (bexio_client_id) REFERENCES clients(bexio_contact_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."bexio_projects" validate constraint "public_bexio_projects_bexio_client_id_fkey";

alter table "public"."bexio_projects" add constraint "public_bexio_projects_bexio_user_id_fkey" FOREIGN KEY (bexio_user_id) REFERENCES employees(bexio_user_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."bexio_projects" validate constraint "public_bexio_projects_bexio_user_id_fkey";

grant delete on table "public"."bexio_projects" to "anon";

grant insert on table "public"."bexio_projects" to "anon";

grant references on table "public"."bexio_projects" to "anon";

grant select on table "public"."bexio_projects" to "anon";

grant trigger on table "public"."bexio_projects" to "anon";

grant truncate on table "public"."bexio_projects" to "anon";

grant update on table "public"."bexio_projects" to "anon";

grant delete on table "public"."bexio_projects" to "authenticated";

grant insert on table "public"."bexio_projects" to "authenticated";

grant references on table "public"."bexio_projects" to "authenticated";

grant select on table "public"."bexio_projects" to "authenticated";

grant trigger on table "public"."bexio_projects" to "authenticated";

grant truncate on table "public"."bexio_projects" to "authenticated";

grant update on table "public"."bexio_projects" to "authenticated";

grant delete on table "public"."bexio_projects" to "service_role";

grant insert on table "public"."bexio_projects" to "service_role";

grant references on table "public"."bexio_projects" to "service_role";

grant select on table "public"."bexio_projects" to "service_role";

grant trigger on table "public"."bexio_projects" to "service_role";

grant truncate on table "public"."bexio_projects" to "service_role";

grant update on table "public"."bexio_projects" to "service_role";



