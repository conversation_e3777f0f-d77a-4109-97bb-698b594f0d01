alter table "public"."bexio_projects" add column "client_name" text;

alter table "public"."bexio_projects" add column "contact_person" text;

alter table "public"."bexio_projects" add column "end_date" timestamp without time zone;

alter table "public"."bexio_projects" alter column "budget" drop default;

alter table "public"."bexio_projects" alter column "budget" drop not null;

alter table "public"."bexio_projects" alter column "effective_cost" drop default;

alter table "public"."bexio_projects" alter column "effective_cost" drop not null;

alter table "public"."bexio_projects" alter column "factorized_amount" drop default;

alter table "public"."bexio_projects" alter column "factorized_amount" drop not null;

alter table "public"."bexio_projects" alter column "invoice_ids" drop default;

alter table "public"."bexio_projects" alter column "invoice_ids" drop not null;

alter table "public"."bexio_projects" alter column "name" drop not null;

alter table "public"."bexio_projects" alter column "nr" drop not null;

alter table "public"."bexio_projects" alter column "services_amount" drop default;

alter table "public"."bexio_projects" alter column "services_amount" drop not null;

alter table "public"."bexio_projects" alter column "status" drop not null;

alter table "public"."bexio_projects" alter column "time_estimate" drop default;

alter table "public"."bexio_projects" alter column "time_estimate" drop not null;

alter table "public"."bexio_projects" alter column "time_spent" drop default;

alter table "public"."bexio_projects" alter column "time_spent" drop not null;

alter table "public"."bexio_projects" alter column "trades_amount" drop default;

alter table "public"."bexio_projects" alter column "trades_amount" drop not null;



