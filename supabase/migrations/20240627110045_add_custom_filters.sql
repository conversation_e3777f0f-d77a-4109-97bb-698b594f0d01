create table "public"."custom_filters" (
                                           "id" bigint generated by default as identity not null,
                                           "name" text not null,
                                           "filter" jsonb[] not null default '{}'::jsonb[],
                                           "created_at" timestamp with time zone not null default now()
);


alter table "public"."custom_filters" enable row level security;

CREATE UNIQUE INDEX custom_filter_pkey ON public.custom_filters USING btree (id);

alter table "public"."custom_filters" add constraint "custom_filter_pkey" PRIMARY KEY using index "custom_filter_pkey";

grant delete on table "public"."custom_filters" to "anon";

grant insert on table "public"."custom_filters" to "anon";

grant references on table "public"."custom_filters" to "anon";

grant select on table "public"."custom_filters" to "anon";

grant trigger on table "public"."custom_filters" to "anon";

grant truncate on table "public"."custom_filters" to "anon";

grant update on table "public"."custom_filters" to "anon";

grant delete on table "public"."custom_filters" to "authenticated";

grant insert on table "public"."custom_filters" to "authenticated";

grant references on table "public"."custom_filters" to "authenticated";

grant select on table "public"."custom_filters" to "authenticated";

grant trigger on table "public"."custom_filters" to "authenticated";

grant truncate on table "public"."custom_filters" to "authenticated";

grant update on table "public"."custom_filters" to "authenticated";

grant delete on table "public"."custom_filters" to "service_role";

grant insert on table "public"."custom_filters" to "service_role";

grant references on table "public"."custom_filters" to "service_role";

grant select on table "public"."custom_filters" to "service_role";

grant trigger on table "public"."custom_filters" to "service_role";

grant truncate on table "public"."custom_filters" to "service_role";

grant update on table "public"."custom_filters" to "service_role";

alter table "public"."custom_filters" disable row level security;

