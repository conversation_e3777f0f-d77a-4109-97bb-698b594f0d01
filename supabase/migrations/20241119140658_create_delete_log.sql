create table "public"."delete_log" (
    "id" uuid not null default gen_random_uuid(),
    "type" text not null,
    "entity_id" text not null,
    "delete_confirmed" boolean not null default false,
    "created_at" timestamp with time zone not null default now()
);


CREATE UNIQUE INDEX delete_log_pkey ON public.delete_log USING btree (id);

alter table "public"."delete_log" add constraint "delete_log_pkey" PRIMARY KEY using index "delete_log_pkey";

grant delete on table "public"."delete_log" to "anon";

grant insert on table "public"."delete_log" to "anon";

grant references on table "public"."delete_log" to "anon";

grant select on table "public"."delete_log" to "anon";

grant trigger on table "public"."delete_log" to "anon";

grant truncate on table "public"."delete_log" to "anon";

grant update on table "public"."delete_log" to "anon";

grant delete on table "public"."delete_log" to "authenticated";

grant insert on table "public"."delete_log" to "authenticated";

grant references on table "public"."delete_log" to "authenticated";

grant select on table "public"."delete_log" to "authenticated";

grant trigger on table "public"."delete_log" to "authenticated";

grant truncate on table "public"."delete_log" to "authenticated";

grant update on table "public"."delete_log" to "authenticated";

grant delete on table "public"."delete_log" to "service_role";

grant insert on table "public"."delete_log" to "service_role";

grant references on table "public"."delete_log" to "service_role";

grant select on table "public"."delete_log" to "service_role";

grant trigger on table "public"."delete_log" to "service_role";

grant truncate on table "public"."delete_log" to "service_role";

grant update on table "public"."delete_log" to "service_role";



