create table "public"."bexio_invoices" (
    "id" integer generated by default as identity not null,
    "document_nr" text not null,
    "project_id" integer,
    "contact_id" integer,
    "total_gross" real not null,
    "total_net" real not null,
    "total_taxes" real not null,
    "total_received_payments" real not null,
    "total_remaining_payments" real not null,
    "total" real not null,
    "trades_amount" real not null,
    "services_amount" real not null
);


CREATE UNIQUE INDEX bexio_invoices_pkey ON public.bexio_invoices USING btree (id);

alter table "public"."bexio_invoices" add constraint "bexio_invoices_pkey" PRIMARY KEY using index "bexio_invoices_pkey";

grant delete on table "public"."bexio_invoices" to "anon";

grant insert on table "public"."bexio_invoices" to "anon";

grant references on table "public"."bexio_invoices" to "anon";

grant select on table "public"."bexio_invoices" to "anon";

grant trigger on table "public"."bexio_invoices" to "anon";

grant truncate on table "public"."bexio_invoices" to "anon";

grant update on table "public"."bexio_invoices" to "anon";

grant delete on table "public"."bexio_invoices" to "authenticated";

grant insert on table "public"."bexio_invoices" to "authenticated";

grant references on table "public"."bexio_invoices" to "authenticated";

grant select on table "public"."bexio_invoices" to "authenticated";

grant trigger on table "public"."bexio_invoices" to "authenticated";

grant truncate on table "public"."bexio_invoices" to "authenticated";

grant update on table "public"."bexio_invoices" to "authenticated";

grant delete on table "public"."bexio_invoices" to "service_role";

grant insert on table "public"."bexio_invoices" to "service_role";

grant references on table "public"."bexio_invoices" to "service_role";

grant select on table "public"."bexio_invoices" to "service_role";

grant trigger on table "public"."bexio_invoices" to "service_role";

grant truncate on table "public"."bexio_invoices" to "service_role";

grant update on table "public"."bexio_invoices" to "service_role";



