create table "public"."bexio_contact_sectors" (
    "id" integer not null,
    "name" text not null
);


alter table "public"."bexio_contact_sectors" enable row level security;

alter table "public"."clients" add column "bexio_sector_ids" integer[] not null default '{}'::integer[];

CREATE UNIQUE INDEX bexio_contact_sectors_id_key ON public.bexio_contact_sectors USING btree (id);

CREATE UNIQUE INDEX bexio_contact_sectors_pkey ON public.bexio_contact_sectors USING btree (id);

alter table "public"."bexio_contact_sectors" add constraint "bexio_contact_sectors_pkey" PRIMARY KEY using index "bexio_contact_sectors_pkey";

alter table "public"."bexio_contact_sectors" add constraint "bexio_contact_sectors_id_key" UNIQUE using index "bexio_contact_sectors_id_key";

grant delete on table "public"."bexio_contact_sectors" to "anon";

grant insert on table "public"."bexio_contact_sectors" to "anon";

grant references on table "public"."bexio_contact_sectors" to "anon";

grant select on table "public"."bexio_contact_sectors" to "anon";

grant trigger on table "public"."bexio_contact_sectors" to "anon";

grant truncate on table "public"."bexio_contact_sectors" to "anon";

grant update on table "public"."bexio_contact_sectors" to "anon";

grant delete on table "public"."bexio_contact_sectors" to "authenticated";

grant insert on table "public"."bexio_contact_sectors" to "authenticated";

grant references on table "public"."bexio_contact_sectors" to "authenticated";

grant select on table "public"."bexio_contact_sectors" to "authenticated";

grant trigger on table "public"."bexio_contact_sectors" to "authenticated";

grant truncate on table "public"."bexio_contact_sectors" to "authenticated";

grant update on table "public"."bexio_contact_sectors" to "authenticated";

grant delete on table "public"."bexio_contact_sectors" to "service_role";

grant insert on table "public"."bexio_contact_sectors" to "service_role";

grant references on table "public"."bexio_contact_sectors" to "service_role";

grant select on table "public"."bexio_contact_sectors" to "service_role";

grant trigger on table "public"."bexio_contact_sectors" to "service_role";

grant truncate on table "public"."bexio_contact_sectors" to "service_role";

grant update on table "public"."bexio_contact_sectors" to "service_role";



