create table "public"."sync_operations" (
    "id" uuid not null default gen_random_uuid(),
    "origin" text not null,
    "entity" text not null,
    "entity_id" text not null,
    "target_id" text,
    "operation" text not null,
    "payload" jsonb,
    "status" text not null default 'PENDING'::text,
    "last_attempt" timestamp with time zone,
    "attempts" integer not null default 0,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."sync_operations" enable row level security;

CREATE UNIQUE INDEX sync_operations_pkey ON public.sync_operations USING btree (id);

alter table "public"."sync_operations" add constraint "sync_operations_pkey" PRIMARY KEY using index "sync_operations_pkey";

grant delete on table "public"."sync_operations" to "anon";

grant insert on table "public"."sync_operations" to "anon";

grant references on table "public"."sync_operations" to "anon";

grant select on table "public"."sync_operations" to "anon";

grant trigger on table "public"."sync_operations" to "anon";

grant truncate on table "public"."sync_operations" to "anon";

grant update on table "public"."sync_operations" to "anon";

grant delete on table "public"."sync_operations" to "authenticated";

grant insert on table "public"."sync_operations" to "authenticated";

grant references on table "public"."sync_operations" to "authenticated";

grant select on table "public"."sync_operations" to "authenticated";

grant trigger on table "public"."sync_operations" to "authenticated";

grant truncate on table "public"."sync_operations" to "authenticated";

grant update on table "public"."sync_operations" to "authenticated";

grant delete on table "public"."sync_operations" to "service_role";

grant insert on table "public"."sync_operations" to "service_role";

grant references on table "public"."sync_operations" to "service_role";

grant select on table "public"."sync_operations" to "service_role";

grant trigger on table "public"."sync_operations" to "service_role";

grant truncate on table "public"."sync_operations" to "service_role";

grant update on table "public"."sync_operations" to "service_role";



