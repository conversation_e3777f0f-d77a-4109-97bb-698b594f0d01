create table "public"."recurring_tasks" (
    "clickup_task_id" text not null,
    "schedule" text,
    "schedule_options" jsonb,
    "skip_weekends" boolean not null default false,
    "recur_end" timestamp with time zone
);


alter table "public"."recurring_tasks" enable row level security;

CREATE UNIQUE INDEX recurring_tasks_pkey ON public.recurring_tasks USING btree (clickup_task_id);

alter table "public"."recurring_tasks" add constraint "recurring_tasks_pkey" PRIMARY KEY using index "recurring_tasks_pkey";

alter table "public"."recurring_tasks" add constraint "recurring_tasks_clickup_id_fkey" FOREIGN KEY (clickup_task_id) REFERENCES tasks(clickup_task_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."recurring_tasks" validate constraint "recurring_tasks_clickup_id_fkey";

grant delete on table "public"."recurring_tasks" to "anon";

grant insert on table "public"."recurring_tasks" to "anon";

grant references on table "public"."recurring_tasks" to "anon";

grant select on table "public"."recurring_tasks" to "anon";

grant trigger on table "public"."recurring_tasks" to "anon";

grant truncate on table "public"."recurring_tasks" to "anon";

grant update on table "public"."recurring_tasks" to "anon";

grant delete on table "public"."recurring_tasks" to "authenticated";

grant insert on table "public"."recurring_tasks" to "authenticated";

grant references on table "public"."recurring_tasks" to "authenticated";

grant select on table "public"."recurring_tasks" to "authenticated";

grant trigger on table "public"."recurring_tasks" to "authenticated";

grant truncate on table "public"."recurring_tasks" to "authenticated";

grant update on table "public"."recurring_tasks" to "authenticated";

grant delete on table "public"."recurring_tasks" to "service_role";

grant insert on table "public"."recurring_tasks" to "service_role";

grant references on table "public"."recurring_tasks" to "service_role";

grant select on table "public"."recurring_tasks" to "service_role";

grant trigger on table "public"."recurring_tasks" to "service_role";

grant truncate on table "public"."recurring_tasks" to "service_role";

grant update on table "public"."recurring_tasks" to "service_role";



